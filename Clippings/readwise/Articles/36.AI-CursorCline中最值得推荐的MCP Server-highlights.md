---
人员: 
  - "[[36.AI]]"
tags:
  - articles
日期: 2025-06-29
时间: 2025-07-10 02:53:37.229329+00:00
相关:
  - "[[<PERSON> 4]]"
  - "[[Cline]]"
  - "[[Context 7]]"
  - "[[<PERSON><PERSON><PERSON>]]"
  - "[[Firecrawl]]"
  - "[[Gemini 2.5]]"
  - "[[GPT-4.1]]"
  - "[[LLM]]"
  - "[[Markdown]]"
  - "[[MCP Server]]"
  - "[[Perplexity]]"
  - "[[Puppeteer]]"
  - "[[Sequential Thinking]]"
  - "[[上下文]]"
  - "[[代码质量]]"
  - "[[克隆网站]]"
  - "[[商业模型]]"
  - "[[复杂问题]]"
  - "[[实时搜索]]"
  - "[[导航]]"
  - "[[库]]"
  - "[[开发者]]"
  - "[[批量抓取]]"
  - "[[推理]]"
  - "[[搜索]]"
  - "[[文档]]"
  - "[[模型]]"
  - "[[步骤]]"
  - "[[测试]]"
  - "[[浏览器控制]]"
  - "[[点击]]"
  - "[[生成代码]]"
  - "[[科技]]"
  - "[[编码体验]]"
  - "[[网页应用]]"
  - "[[网页抓取]]"
  - "[[自动执行]]"
  - "[[训练数据]]"
  - "[[项目]]"

链接: https://mp.weixin.qq.com/s/PqaEDWGJ_NxkR1SO4tzSug?clicktime=1752079693&enterid=1752079693&exptype=unsubscribed_card_recommend_article_u2i_mainprocess_coarse_sort_pcfeeds&ranksessionid=1752079679_2&scene=169&subscene=200
附件: https://mmbiz.qpic.cn/sz_mmbiz_jpg/h988a0nsgw7ayR61YGzn0U0FVLPMg7FYtorzISW0QicSgE3GJqEzKNBrVXZBk9RKibPKMClMyXQNUsHspQTfBcGA/0?wx_fmt=jpeg)
---
## Document Note

## Summary

当你在使用 Cursor或者Cline时，生成的代码质量可能会受到你选择的模型的限制。\x0d\x0aMCP 服务器是一种非常好的方式， 为这些模型提供额外的上下文，提升其生成代码的能力。

## Full Document
[[Full Document Contents/Articles/36.AI-CursorCline中最值得推荐的MCP Server.md|See full document content →]]

## Highlights
- Perplexity 用于研究，
  Context 7 用于文档，
  Puppeteer 用于浏览器控制，
  Firecrawl 用于网页抓取，
  Sequential Thinking 用于逻辑推理。 ([View Highlight](https://read.readwise.io/read/01jzs3zqmzdt88js1phy0gvkbm))
- 下面我们看看这些 MCP 服务器如何提升你的编码体验：
  LLM 通常无法获取训练数据之外的信息。
  Perplexity 能够实时搜索网页，获取相关数据。
  当 Cline配备了 Perplexity 时，它可以将搜索结果纳入上下文中使用。 ([View Highlight](https://read.readwise.io/read/01jzs40avzd0pb1497hwaws2nd))
- ![[Attachments/750512d602bc32f8ac28881b6d0c0a30_MD5.webp]]([View Highlight](https://read.readwise.io/read/01jzs41c809p8as3d834p0jgsf))
- ![[Attachments/bac4d078bb1eb414898e518a262ae253_MD5.webp]]([View Highlight](https://read.readwise.io/read/01jzs40mwknndv909zqhyk7shf))
