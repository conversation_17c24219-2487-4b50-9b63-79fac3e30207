---
人员: 
  - "[[@imxiaohu on Twitter]]"
tags:
  - tweets
日期: None
时间: 2025-06-25 13:20:42.232140+00:00
链接: https://twitter.com/imxiaohu/status/1937847459117687004
附件: https://pbs.twimg.com/profile_images/1765404718959095808/BX7VN1hS.jpg)
---
## Document Note

## Summary

## Highlights
- 兄弟们 
  发现一个牛P的AI设计网站
  设计的界面非常精美、炸裂，完全可以直接使用。
  最重要的它大幅的简化了对设计提示词的要求
  你只要简单描述你的设计需求即可，它会先给你出一套非常详细的设计方案，让你选择，再开始设计
  最牛P的是它还能根据界面设计上下文逻辑 为你生成剩余的二级三级页面...
  而且还支持对页面各种元素 、文字、图像等进行精准编辑<video controls><source src="https://video.twimg.com/amplify_video/1937845736743546881/pl/xL4ajm9VVeNLkR7u.m3u8?tag=21" type="application/x-mpegURL"><source src="https://video.twimg.com/amplify_video/1937845736743546881/vid/avc1/456x270/xFvSSgEsJn1ajYGa.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1937845736743546881/vid/avc1/610x360/cz_qw41069QqOOid.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1937845736743546881/vid/avc1/1220x720/2Xi48_J9oPcE59qy.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1937845736743546881/vid/avc1/1830x1080/06XtKtTmzRWl15Y-.mp4?tag=21" type="video/mp4">Your browser does not support the video tag.</video> ([View Tweet](https://twitter.com/imxiaohu/status/1937847459117687004))
- 你提出需求后
  它会根据你的需求出一套完整的设计方案
  包含了方方面面
  例如主题区布局、交互、动画、配色、界面的效果方案等等
  不满意你可以编辑这个方案，然后提交再开始设计
  相当于帮你写了详细的设计提示词 
  ![[Attachments/5b8c17487ca57ebda599ce0351693820_MD5.jpg]] ([View Tweet](https://twitter.com/imxiaohu/status/1937847463060426763))
- 当然也支持你上传你的原型图或者设计稿
  作为参考资料
  为你设计 
  ![[Attachments/14f06b6a60ef7b8815a2054871dc04cf_MD5.jpg]] ([View Tweet](https://twitter.com/imxiaohu/status/1937847466747130088))
- 它最牛p之处在于
  当你鼠标放在生成好的界面逻辑上任意可以跳转的地方
  它都会根据上下文和界面逻辑为你生成后续的二级、三级页面
  等于是可以使用这种方式完成整套设计
  而且保持页面逻辑、界面效果配色方案和之前的统一一致 
  ![[Attachments/1a6a4448fe562631ca058e0fadbd4e3c_MD5.jpg]] ([View Tweet](https://twitter.com/imxiaohu/status/1937847469699915831))
- 还有一个就是它支持对生成界面上的的各种组件、元素进行精准的修改
  你可以通过页面选择器
  精准的选择界面中的任意元素、模块、文字、图像等进行精准编辑
  可以修改替换文字、图像、组件、模块，增加链接、生成视频等各种操作。
  这样大大方便了修改，不用导出所见即所得
  当然它也支持导出到Figma <video controls><source src="https://video.twimg.com/amplify_video/1937847749640359936/pl/h4sBxOwMo-3OvcCT.m3u8?tag=21" type="application/x-mpegURL"><source src="https://video.twimg.com/amplify_video/1937847749640359936/vid/avc1/454x270/c7vsDBBsWyUfMIx1.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1937847749640359936/vid/avc1/604x360/ip6UcDfTCFcJfq54.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1937847749640359936/vid/avc1/1210x720/0SOhUFE7Pjz62Q7b.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1937847749640359936/vid/avc1/1816x1080/YjQmeEiskjXhVJT-.mp4?tag=21" type="video/mp4">Your browser does not support the video tag.</video> ([View Tweet](https://twitter.com/imxiaohu/status/1937848723851366874))
- 下面是我生成的网站效果
  另外它支持直接绑定域名和DNS一键部署发布网站
  其实最重要的我试了很多次，设计审美确实在线，生成的网站页面非常美观、效果非常炸裂。
  重点，试了多次，发现英文提示效果更好
  在线体验：https://t.co/9zljnuKC4q
  我生成网站效果
  首页：https://t.co/ULTEQJMcVt
  二级页面：https://t.co/ZeM6rGzuRS<video controls><source src="https://video.twimg.com/amplify_video/1937848872593965056/pl/LYqYp64j3Esmgjb8.m3u8?tag=21" type="application/x-mpegURL"><source src="https://video.twimg.com/amplify_video/1937848872593965056/vid/avc1/470x270/bSWGCekY21hKE1P2.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1937848872593965056/vid/avc1/626x360/XzA8BQFgBZhRDMOP.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1937848872593965056/vid/avc1/1252x720/b8LNg2mWAhcbrENm.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1937848872593965056/vid/avc1/1880x1080/gI7Armoukaqzt1ad.mp4?tag=21" type="video/mp4">Your browser does not support the video tag.</video> ([View Tweet](https://twitter.com/imxiaohu/status/1937849258461610227))
