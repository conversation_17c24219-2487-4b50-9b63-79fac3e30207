---
人员: 
  - "[[@op7418 on Twitter]]"
tags:
  - tweets
日期: None
时间: 2025-07-04 02:21:46.277744+00:00
链接: https://twitter.com/op7418/status/1940342354155880938
附件: https://pbs.twimg.com/profile_images/1636981205504786434/xDl77JIw.jpg)
---
## Document Note

## Summary

## Highlights
- 这周藏师傅呕心沥血的大活来了！
  Gemini CLI 不写代码帮普通人提效的的一万种用法！
  - 如何低门槛两步用上 Gemini CLI
  - 批量修改系统设置
  - 编辑查找文档，生成 PPT
  - 剪辑视频、修改图片、下载视频
  -不同文档格式的互转 等等
  下面是详细的教程和案例🧵 
  ![[Attachments/9ea1061d5007ba94c9efce6aa004e492_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940342354155880938))
- 等不及施工的可以先收藏
  或者直接看这里：https://t.co/Khd79XwymX ([View Tweet](https://twitter.com/op7418/status/1940342489103335887))
- 来看一下我会教你用 Gemini CLI 实现哪些能力：
  如何低门槛两步用上 Gemini CLI
  查找和批量编辑本地文档
  分析你的 Obsidian 笔记库，将相关的笔记链接起来
  分析本地图片内容批量修改文件名，给图片打标
  批量修改系统设置，创建你的工作模式一键开关
  为本地文档生成效果丰富美观的 PPT
  帮你剪辑和处理本地视频，加水印、转 gif、加音乐。。。
  帮你快速批量下载 youtube 等网站视频和封面
  帮你处理图片，加滤镜、加水印、改大小、多张拼合。。。
  帮你实现不同文档格式的互转，Markdown 转 Word。。。 ([View Tweet](https://twitter.com/op7418/status/1940342715482579061))
- 简单解释一下这类产品和 Cursor 之类的区别。
  首先他们是没有界面的，所有的操作都是在终端以命令行的方式展示。
  然后就是也是 Agents 可以自动执行任务处理本地文件，同时内置了非常多的工具，比如谷歌搜索、阅读文件、查找文件、搜索文字、写入文件、保存记忆等，你输入 /tools 然后回车就可以让他列出目前支持的工具。
  ![[Attachments/ecd1c524709381786714a5fe16fb0738_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940342807597830452))
- 我不会编程是不能不能用
  很多朋友说命令行是不是很复杂啊，我不会编程是不是会很难用。
  其实并没有，如果你的网络环境正常，能够正常登录 Gemini CLI 的话，跟使用 Cursor 没有本质区别。
  因为核心交互的时候还是主要为提示词输入框，命令行又不用你写，Gemini 写就行。 
  ![[Attachments/0342dabc321b4aa34af9494f931538c8_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940343005585797554))
- 如何使用 Gemini CLI
  首先要做的第一步就是进入到我们的启动台，搜索终端两个字，搜到之后打开。
  这时候你就看到一个空白界面里面写了些你看不懂的字，不要担心。
  这里我建议我们想好要进行的任务之后，新建一个文件夹把需要的任务素材扔进去，然后按住 option 按键鼠标右键选择“将 XXXX 文件夹拷贝为路径名称”，这时候你就快速获得了这个文件夹的路径。
  然后我们回到我们的终端窗口，输入 cd + 空格 + 你刚才复制的路径，接下来你终端的所有操作都只会影响这个文件夹的内容，不用担心把电脑搞坏。
  ![[Attachments/1468cd8681cc0c82d22cf8168ea7c84c_MD5.jpg]]
  ![[Attachments/b4b8623efaf7bb8491cf1f7c4c581275_MD5.jpg]]
  ![[Attachments/80a5fc9400bd5c0ccc2628ac1fa28956_MD5.png]]
  ![[Attachments/0a59324c5739b411c0c857f701ee1cb6_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940343432070988141))
- 到这一步我们终于开始安装 Gemini CLI 了，非常简单
  先去安装 Node.js
  然后你只需要输入下面的内容然后回车就行。
  npx https://t.co/Fv7ddaLF2i
  安装成功你就会看到这个界面，应该会先让你选择命令行的颜色主题，然后让你选择登录方式。
  这里需要注意：终端的操作大部分时间需要用上下左右方向键来操作选项，选中之后按回车确认。
  你只需要选择一个自己喜欢的主题之后，选择正常的谷歌账号登录，在拉起网页登录后关掉就行。
  我这个这里已经登录了，所以没有这些选项，然后你就能看到提示词输入框了。
  ![[Attachments/c952a574e6221db751b3691e2a34501a_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940344257124217170))
- 恭喜你到这里，你已经完成了 Gemini 的安装。
  由于用的 NPX 的安装方式，所以你以后每次关掉终端重新使用 Gemini CLI 的时候都需要输入开始的那个命令，不过不用登录了，直接就能用。
  最后由于命令行本身都是英文的，可能很多人会望而却步，这个时候你可以装个 Bob 这个翻译软件，支持划词翻译，看不懂的选项直接选中划词翻译就行。
  ![[Attachments/a2a23623fafbb53f3af77510c51accc1_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940344507192807524))
- 来点基础用法
  由于 Gemini 可以看到你的文件并且操作，而且它还有生成能力，本身模型还是多模态的，所以即使只用本身的工具也可以有很多用法。 ([View Tweet](https://twitter.com/op7418/status/1940344771010351312))
- 查找和生成本地文档
  首先是 Gemini CLI 本身支持谷歌搜索，你可以让他搜索指定内容给你写成文档，也可以对你本身的文档进行编辑。
  当然搜索工具经常会限额，这个有点恶心，比如让他搜索歸藏的信息并且整理一个介绍文档。 
  ![[Attachments/eefdc1550f4edbef4574503e2ee170da_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940344877302366213))
- 你也可以让他分析你保存在本地的文章之后进行改写，生成新的文章。
  比如我这里就让他把 Karpathy 的软件 3.0 文章改写成适合发布的博客文章，同时生成对应的推特发布版本，也可以对于会议总结之类的文档进行分析和处理。
  提示词：
  读取我刚才录音转写的会议纪要 meeting_notes.txt，总结出关键决策点，并识别出分配给我的所有待办事项，将它们以任务列表的形式添加到我的 todo .md 文件中。
  根据Andrej Karpathy 软件 3.0 分享的文章，将其改写成一篇约 800字的博客文章，风格要轻松有趣。然后，为这篇文章生成 3 个适合在 Twitter 上发布的推文版本，并附上 标签
  ![[Attachments/b39f6c7031952bc2ff0e2300ae8c975a_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940345090515562812))
- 找到你的 Obsidian 文件夹打开之后启动 Gemini CLI，然后让 Gemini CLI 查找相关的内容。
  让他检索我所有的剪藏文件，找到 MCP 相关的文章，然后给我生成一个带反向链接的《MCP 剪藏内容索引》文档
  每个无序列表都有文件标题以及文章的总结，最后还有链接可以直达那个文章。 
  ![[Attachments/105d2aae5749e23b5cbfb796d1d96c7a_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940345751302963686))
- Obsidian 的一个知识图谱的功能，它可以把所有有反向链接的相关文档都链接起来，形成你自己的网状笔记网络，方便你学习和回顾。
  反向链接需要自己手动加，现在有了 Gemini CLI 问题解决了，可以让他帮你给你文件夹中的相关文档加反向链接。 <video controls><source src="https://video.twimg.com/amplify_video/1940346102768656384/pl/_NefTiszL-OFXBt6.m3u8?tag=21" type="application/x-mpegURL"><source src="https://video.twimg.com/amplify_video/1940346102768656384/vid/avc1/478x270/9rqDcwiAygZnDcnn.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1940346102768656384/vid/avc1/638x360/UZwmZiwStP_IpMsz.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1940346102768656384/vid/avc1/1276x720/YZS9cCRaOLRm0yOv.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1940346102768656384/vid/avc1/1916x1080/8tcFj1ZSe67jZC-h.mp4?tag=21" type="video/mp4">Your browser does not support the video tag.</video> ([View Tweet](https://twitter.com/op7418/status/1940346222935765372))
- 图片分析和处理
  由于本身 Gemini CLI 是多模态的的，所以你的图片也可以让他帮忙处理。
  比如我打开了一个全是图片的文件夹，里面的图片名字乱七八糟的，这时候就可以让他分析图片内容之后根据图片内容给图片重新命名。 
  ![[Attachments/e5bafe565b8849c9cf8506abf854bd97_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940346334600626260))
- 我们都知道在训练图像模型或者 Lora 的时候需要对图像进行标注，大部分训练工具都是把标注放在一个跟图片命名一样的文本文件里，现在我们就可以让 Gemini CLI 来做这件事了。
  可以看到他执行的非常完美，以往这些你还得找对应的工具，而且不好自定义要求，现在提示词就行。 
  ![[Attachments/885419024dc07b75519ec8c7c80c643f_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940346453618229746))
- 修改系统设置整理文件
  Gemini CLI 除了可以读取文件和修改文件外也是可以控制系统设置的。
  比如我们就可以写好自己日常对于软件和系统设置在不同工作时间的喜好，需要的时候一键完成所有操作的更改。
  这里我就让他给我关掉浏览器，然后打开 Obsidian，降低系统音量，直接进入工作模式。 <video controls><source src="https://video.twimg.com/amplify_video/1940346611890061312/pl/2WmCjMfTizsjfjC5.m3u8?tag=21" type="application/x-mpegURL"><source src="https://video.twimg.com/amplify_video/1940346611890061312/vid/avc1/450x270/3Ipfco0GOpRRUAgL.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1940346611890061312/vid/avc1/600x360/RnIDy5v2d9689jKC.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1940346611890061312/vid/avc1/1202x720/5hT8tcylUpmM_0GK.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1940346611890061312/vid/avc1/1804x1080/m1syjY8jqxkr_hCL.mp4?tag=21" type="video/mp4">Your browser does not support the video tag.</video> ([View Tweet](https://twitter.com/op7418/status/1940346799530889694))
- 我们肯定也有很多时候桌面或者文件没有整理乱七八糟。
  这个时候就可以让 Gemini CLI 新建文件夹进行分类和整理。
  但是这里得注意，不要让他整理过大的过于重要的文件夹，不然误删了就痛苦了。
  这里我就让他把刚才的图像和标注文件新建了两个文件夹分别整理了。 
  ![[Attachments/0387dc942ccbfa1a4cc2d6b551d608f9_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940346941025771966))
- Gemini CLI 的高级使用方法
  上面都是些基本用法，你最近可能也看到了一些。
  但是我发现结合一些本地软件，Gemini CLI 能实现对各种文件更加高级的处理，比如视频转 gif、youtube 视频下载、加水印、文档格式转换等。
  这些就非常牛皮了，而且我们日常内容创作大部分都非常需要。 ([View Tweet](https://twitter.com/op7418/status/1940347100556070942))
- 为本地文档生成PPT
  前面我们有了文档了，但是很多时候演示的时候总不能真给人看 Markdown 文档吧，能不能生成 PPT 呢？
  可以的，朋友，必须可以，比如我这里就把前面我那个 MCP 索引文档的内容直接转换为 PPT 了。
  这个依赖一个叫 Slidev 的项目，它可以用类似 Markdown 文档的格式将内容变成带有丰富样式的 PPT。
  你不需要知道这个项目的细节，直接用我下面的提示词生成文件之后，复制文件到这个页面（https://t.co/zk0DYyMf1u）预览就行。<video controls><source src="https://video.twimg.com/amplify_video/1940347239752245248/pl/7cpJ6MGi_7Chw2FW.m3u8?tag=21" type="application/x-mpegURL"><source src="https://video.twimg.com/amplify_video/1940347239752245248/vid/avc1/486x270/O5a9-c3pRz5jlbN3.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1940347239752245248/vid/avc1/648x360/s-Wz8raMA7fxAb55.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1940347239752245248/vid/avc1/1298x720/Pxbsb7F6V1k0VB9H.mp4?tag=21" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1940347239752245248/vid/avc1/1948x1080/jpfYlx8OgobZPCNB.mp4?tag=21" type="video/mp4">Your browser does not support the video tag.</video> ([View Tweet](https://twitter.com/op7418/status/1940347489816838620))
- 用 ffmpeg 帮助处理视频
  首先先介绍一下 ffmpeg 这个项目，给予他你可以实现非常强的视频编辑能力，理论上剪映之类的视频编辑软件都是基于这个完成的。
  你可以对本地的视频进行拼接、剪辑、增加文字、转换格式、转换分辨率、增加音乐，基本上你能想到的视频编辑能力他都能做到。
  首先我们需要大概你需要处理视频的的文件夹，然后启动 Gemini CLI 第一个命令是让他安装 Homebrew。
  你只需要说两句话，就全部安装了。
  ![[Attachments/35ce7be4cf358b58cb7b6a36443a71d9_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940397262087282708))
- 然后我们就可以爽用了，先给我们指定的视频加个水印试试。
  可以看到新的视频右上角果然有了一个水印，这种言出法随的感觉谁不喜欢，你不需要了解原理，你就只需要知道所有的视频编辑他都能帮你搞定。
  提示词：帮我将 XXX 这个视频用 ffmpeg 打上一个 10% 透明度的水印在右上角，水印内容为“guizang”然后存储为新视频。
  ![[Attachments/719681cc72ebb13ac326db6406cd8e68_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940397422854951088))
- 然后我想要给视频配乐也可以，你只需要告诉他视频文件和音乐文件的名字就行，我甚至让他给音乐加上了淡入和淡出。
  他先是获取了一下视频的时长，然后就开始操作了，然后搞定了，非常完美，严丝合缝，淡入淡出也加上了，可以看剪映界面预览的频谱。 
  ![[Attachments/7ab9d1829d339daee75115e140b5b173_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940397742398005589))
- 我们很多时候需要把视频转换为序列帧，然后拿其中一帧进行处理，或者处理所有的帧，以前你是不是还得到处找这种工具，而且转换效果不一定好。
  Gemini CLI 一句话就能搞定，处理的又快又好，而且节省了用网页工具上传下载的时间。 
  ![[Attachments/64b66723fd0011038dfb57679744c40a_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940397844021846346))
- 另一个常见的任务就是视频转 gif，尤其公众号有 10 个视频的限制，很多时候迫不得已得转成 gif 发布。
  Gemini 捕捉到了高品质这个关键词还制作了调色板保证颜色还原度，最后处理的非常完美。 
  ![[Attachments/40176cd5a460edc730e4591ad6c94df8_MD5.jpg]]<video controls><source src="https://video.twimg.com/tweet_video/Gu2tsJKXgAEh1T2.mp4" type="video/mp4">Your browser does not support the video tag.</video> ([View Tweet](https://twitter.com/op7418/status/1940398052965228640))
- 利用 yt-dlp 下载视频
  yt-dlp 这个项目跟 ffmpeg 配合几乎可以下载你能想到的所有视频平台的视频。
  我们还是可以让 Gemini CLI 帮我们安装 yt-dlp 这个项目就行。
  然后直接提供视频链接他就会帮你下载，甚至可以批量下载多个视频或者一起连封面和视频一起下载 
  ![[Attachments/c9536f0d05a6fa71188a8d59c26065f5_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940444157090439369))
- 利用 ImageMagick 处理图片
  处理视频我们有 ffmpeg 这种项目，当然图片也有。
  ImageMagick 是一个极其强大的工具集，你可以用它来转换格式、缩放、裁剪、旋转、添加滤镜、组合图片等等。
  依然是老一套，先让 Gemini 帮我们安装
  帮我用 Homebrew 安装 ImageMagick 
  ![[Attachments/19b1fd0a602efe1b5ab16ed07f88eed9_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940601605843546158))
- 先来一个常见任务，我们设计师做外包经常用，在没结款之前给甲方低分辨率和带水印的图片，现在就可以直接批量完成。
  这里我让他把所有图片宽边调整为 800 PX，然后统一加上带“内部资料”文字的水印。
  出错之后他自己开始用多模态能力检查修改后的图像了，太聪明了 
  ![[Attachments/54356ffdce9fa05d7fddcd11efb1e962_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940601824412917927))
- 然后再来一个常见的图像拼接需求，这种在媒体上发消息的时候经常需要，尤其是推特。
  将加过水印的图片用ImageMagick拼合成一张四宫格图片，中间有白色分隔
  搞得不错，这几张图比例不同，所以没有对齐，可以在命令上加上统一比例这种要求就行。 
  ![[Attachments/5897577d05dfcd7b3f5c04c4ab742271_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940601974283702697))
- 文档格式转换也是常见的需求，相当多的公司工作文档还是 word，很多时候我发过去 .md 文件那边都不知道怎么办。
  首先还是让 Gemini CLI 帮我们安装。
  这个时候我们就可以利用 Pandoc 这个项目进行各种文档格式之间的互相转换，当然批量转换也是可以的。
  可以看到转的很好，Markdown 的一些基本格式也都迁移了，不管是加粗还是无序列表有序列表。
  ![[Attachments/836484ee52393add68e6356c98f8723e_MD5.jpg]] ([View Tweet](https://twitter.com/op7418/status/1940602154731163672))
- 好了教程到这里就结束了。
  到现在你应该知道为什么我会说会有一万种用法了，你可以用 Gemini CLI 控制这些已经非常成熟的命令行项目，基本上你所有的需求都能找到对应的项目。
  不是只有 MCP 才能做 Agent，这些传统工具在有了 LLM 加持之后会更加强大。 ([View Tweet](https://twitter.com/op7418/status/1940602327309996359))
