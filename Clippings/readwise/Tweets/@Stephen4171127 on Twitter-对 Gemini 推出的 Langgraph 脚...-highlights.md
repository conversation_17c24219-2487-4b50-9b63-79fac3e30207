---
人员: 
  - "[[@Stephen4171127 on Twitter]]"
tags:
  - tweets
日期: None
时间: 2025-06-07 13:31:19.619562+00:00
链接: https://twitter.com/Stephen4171127/status/1930908862665703511
附件: https://pbs.twimg.com/profile_images/1729475561192407040/uvDi8Fi4.jpg)
---
## Document Note

## Summary

## Highlights
- 对 Gemini 推出的 langgraph 脚手架爱不释手，这个脚手架基础上能做各种这样的商用级别Agent 的开发，不管是 Manus 还是 lovart 都能做到。
  我基于该脚手架做了一个 DeepResearch 的增强版本，能够对 Query 进行拆解细分任务，对每个任务做独立的 DeepResearch，调研过程中还会进一步判断是否有必要爬取原始页面的 raw data 来辅助强化报告的生成。
  最终报告是融合了多个子任务的独立报告，合并而成最终的报告。
  这种 Deepresearch 是一种适用于不那么聪明的模型的实现，适用于本地部署的模型，比如 Qwen3
  详细的改造报告在 README 可以找到。
  前端也被我改造地更适用于查看每个步骤的中间状态。
  ![[Attachments/2cd14bc8be618c712a83c4bd0801bde5_MD5.jpg]] ([View Tweet](https://twitter.com/Stephen4171127/status/1930908862665703511))
- https://t.co/rqnE4FMHKQ ([View Tweet](https://twitter.com/Stephen4171127/status/1930908865261973610))
- 这是改造后的工作流，增加了
  planner 环节（对原始 Query 拆分任务）
  content_enhancement ( 由于 Gemini search API 返回的都是搜索摘要，当摘要不足以满足报告需求的时候，该 节点会爬取原始链接中的内容）
  finalize_answer (对每个 task 的报告进行汇总，形成最终报告） 
  ![[Attachments/51042bb980ac8fdb380c47bbe82d8068_MD5.jpg]] ([View Tweet](https://twitter.com/Stephen4171127/status/1930908867367506027))
- 这是一个 content_enhancement 的具体示例，可以参考。这样看起来会更直观。 
  ![[Attachments/ef5a5c210cd463f087e4daedec4fe240_MD5.jpg]] ([View Tweet](https://twitter.com/Stephen4171127/status/1930908870320287961))
- 部署方式还是和官方的部署方式一样，非常容易可以在本地启动起来 
  ![[Attachments/20800f3bdd50779b9118698db1b96a10_MD5.jpg]] ([View Tweet](https://twitter.com/Stephen4171127/status/1930908873432478028))
- 文档生成流程：从查询到综合研究报告
  https://t.co/UuCgtqgymX ([View Tweet](https://twitter.com/Stephen4171127/status/1930911615706071124))
