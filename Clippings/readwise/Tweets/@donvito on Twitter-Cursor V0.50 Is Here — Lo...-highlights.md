---
人员: 
  - "[[@donvito on Twitter]]"
tags:
  - tweets
日期: None
时间: 2025-05-11 06:03:42.711061+00:00
链接: https://twitter.com/donvito/status/1921137235040366605
附件: https://pbs.twimg.com/profile_images/1833137601207009280/gGSDe5DF.jpg)
---
## Document Note

## Summary

## Highlights
- cursor v0.50 is here
  lots of juicy features
  here's all the features you need to know 🧵 👇 
  ![[Attachments/5d66e758ca9d61ee1851efb2fa4e2ef6_MD5.jpg]] ([View Tweet](https://twitter.com/donvito/status/1921137235040366605))
- [cursor_ai](https://twitter.com/cursor_ai) Simpler, unified pricing for [cursor_ai](https://twitter.com/cursor_ai)
  – All model usage now on request-based pricing
  – <PERSON> Mode adopts token-based pricing (like model APIs)
  – Premium tool calls & long context mode removed ([View Tweet](https://twitter.com/donvito/status/1921137247904297291))
- Max Mode for all top models (token-based)
  max mode is ideal for harder problems which require more context, intelligence and tool use
  [cursor_ai](https://twitter.com/cursor_ai) <video controls><source src="https://video.twimg.com/amplify_video/1921137261003108352/pl/EnYO_i24HULYCM13.m3u8?tag=14" type="application/x-mpegURL"><source src="https://video.twimg.com/amplify_video/1921137261003108352/vid/avc1/412x270/z1WfrOlDygpXQMIv.mp4?tag=14" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1921137261003108352/vid/avc1/550x360/FrYvlEZDBqcFjPgJ.mp4?tag=14" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1921137261003108352/vid/avc1/1102x720/On21fFtr4CHBOfDs.mp4?tag=14" type="video/mp4">Your browser does not support the video tag.</video> ([View Tweet](https://twitter.com/donvito/status/1921137310982459591))
- Background Agent (Preview)
  "a remote, asynchronous agent that runs outside the main Cursor editor in a remote containerized environment. It allows you to offload long-running or complex task"
  works best for tasks that require less human interaction like fixing simple bugs, small features, or long changes
  more information about it here
  https://t.co/pqQtCuTNR9
  @cursor_ai<video controls><source src="https://video.twimg.com/amplify_video/1921137324391567360/pl/InEZ8PDFW64OjbiV.m3u8?tag=14" type="application/x-mpegURL"><source src="https://video.twimg.com/amplify_video/1921137324391567360/vid/avc1/422x270/zvcx6EnGr77YD1GL.mp4?tag=14" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1921137324391567360/vid/avc1/562x360/_eAuT8-jkcle_uUh.mp4?tag=14" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1921137324391567360/vid/avc1/1124x720/FGb4-PLuarkq1T8e.mp4?tag=14" type="video/mp4">Your browser does not support the video tag.</video> ([View Tweet](https://twitter.com/donvito/status/1921137376426111077))
- Include your entire codebase in context
  use @ folders to add your entire codebase into context
  [cursor_ai](https://twitter.com/cursor_ai) <video controls><source src="https://video.twimg.com/amplify_video/1921137389835350018/pl/nbk2jKerFaBEveWV.m3u8?tag=14" type="application/x-mpegURL"><source src="https://video.twimg.com/amplify_video/1921137389835350018/vid/avc1/422x270/RGHKnw0xBh85T3QA.mp4?tag=14" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1921137389835350018/vid/avc1/562x360/NpEDtsu0eJz9Dai7.mp4?tag=14" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1921137389835350018/vid/avc1/1124x720/4SOzid8flXm96a4E.mp4?tag=14" type="video/mp4">Your browser does not support the video tag.</video> ([View Tweet](https://twitter.com/donvito/status/1921137396944695360))
- Inline Edit(Cmd/Ctrl+K) Refresh
  Supports full file edits. Full file edits makes it easy to do scope changes to a file without using agent
  You can also send the code block to the agent for multi-file edits
  [cursor_ai](https://twitter.com/cursor_ai) <video controls><source src="https://video.twimg.com/amplify_video/1921137409493987328/pl/ktbQtTZRrr0gsUJ0.m3u8?tag=14" type="application/x-mpegURL"><source src="https://video.twimg.com/amplify_video/1921137409493987328/vid/avc1/422x270/elXsecePZduVWluf.mp4?tag=14" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1921137409493987328/vid/avc1/562x360/SFnxsfADoe9cj89t.mp4?tag=14" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1921137409493987328/vid/avc1/1126x720/dVrzFcTmOJwMVVGe.mp4?tag=14" type="video/mp4">Your browser does not support the video tag.</video> ([View Tweet](https://twitter.com/donvito/status/1921137460807159926))
- Fast edits for long files with Agent
  Agent can now find the exact place where edits should occur and change only that part
  [cursor_ai](https://twitter.com/cursor_ai) 
  ![[Attachments/f13300a2761dc6f19784a697339e9025_MD5.jpg]] ([View Tweet](https://twitter.com/donvito/status/1921137481837408337))
- Workspaces
  Work across multiple codebases in one session 
  ![[Attachments/83b19751d8dac2bb7c411e73ee1a0903_MD5.jpg]] ([View Tweet](https://twitter.com/donvito/status/1921137499386376513))
- [cursor_ai](https://twitter.com/cursor_ai) Exporting Chat
  You can now export chats to a markdown file. This is useful when you want to get advise from other AI about your code using the conversation you already have with [cursor_ai](https://twitter.com/cursor_ai) <video controls><source src="https://video.twimg.com/amplify_video/1921137513395351554/pl/TawUy5sOmmC5kFvF.m3u8?tag=14" type="application/x-mpegURL"><source src="https://video.twimg.com/amplify_video/1921137513395351554/vid/avc1/422x270/aUmYm3FsY1KtQia0.mp4?tag=14" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1921137513395351554/vid/avc1/562x360/-3UIl0pZ5ZzhQihw.mp4?tag=14" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1921137513395351554/vid/avc1/1124x720/vqlJdrFH9F-1sO_D.mp4?tag=14" type="video/mp4">Your browser does not support the video tag.</video> ([View Tweet](https://twitter.com/donvito/status/1921137564813324779))
- Duplicate Chat
  Useful when you want to fork a chat and explore different options
  [cursor_ai](https://twitter.com/cursor_ai) <video controls><source src="https://video.twimg.com/amplify_video/1921137577996021760/pl/pks6sQCyZG8CMDWZ.m3u8?tag=14" type="application/x-mpegURL"><source src="https://video.twimg.com/amplify_video/1921137577996021760/vid/avc1/492x270/5dIAM87hZA0QmZQ7.mp4?tag=14" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1921137577996021760/vid/avc1/656x360/9fwVL9QsjR9-dwPa.mp4?tag=14" type="video/mp4"><source src="https://video.twimg.com/amplify_video/1921137577996021760/vid/avc1/1312x720/866Zv-dtT5sSKFKJ.mp4?tag=14" type="video/mp4">Your browser does not support the video tag.</video> ([View Tweet](https://twitter.com/donvito/status/1921137629376246043))
- like this update?
  support me by reposting this thread 👇
  you can also follow me [donvito](https://twitter.com/donvito) for more AI ([View Tweet](https://twitter.com/donvito/status/1921137641481007300))
- https://t.co/Na9EoXnrUi ([View Tweet](https://twitter.com/donvito/status/1921138009337925981))
- FYI, this is dropping in cursor 0.50 as well — added after I posted this thread
  Tab to edit other files — perfect for refactoring
  Tab suggestions also now have syntax highlighting! 
  ![[Attachments/f45cb8d39c1918fa7da9081c2de8bc69_MD5.jpg]] ([View Tweet](https://twitter.com/donvito/status/1921424930685530290))
