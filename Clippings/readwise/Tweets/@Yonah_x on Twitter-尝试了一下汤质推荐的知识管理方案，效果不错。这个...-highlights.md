---
人员: 
  - "[[@Yonah_x on Twitter]]"
tags:
  - tweets
日期: 2025-05-17
时间: 2025-05-18 00:55:37.574187+00:00
链接: https://twitter.com/Yonah_x/status/1923714920639529164
附件: https://pbs.twimg.com/profile_images/1655216038819278848/2pI0hAsf.jpg)
---
## Document Note

## Summary

## Highlights
- 尝试了一下汤质推荐的知识管理方案，效果不错。这个方案的核心在于把所有的内容集中管理之后，通过 embedding 向量进行相关推荐。
  我把Cubox，flomo，微信读书，ChatGPT 聊天记录的内容都统一放到 Obsidian 之后，我打开任意一个文档，它在侧边都会有联想到的相关片段，这样就会产生意想不到的一些联系。
  比如我随手打开一篇 23 年收藏的《ChatGPT哲学之谜》，在左边关联出了最近跟 ChatGPT 聊的内容，Flomo 记得相关笔记，微信读书《关于说话的一切》的划线内容等。
  有语义联想功能的笔记软件跟没有这个功能的软件完全是两个时代的产物，大概相当于有脑子和没脑子的区别，大脑非常重要的一个能力就是联想，从这个意义上说，有联想能力的知识库才是人的第二大脑，不然就只是一个储藏室而已。
  最后分享一下我实践的一些经验，整个过程不用花一分钱：
  1. 相关插件：Cubox 导入搜索同名插件，flomo 导入用“Flomo Importer”，微信读书导入用“Weread”，语义关联用“Smart Connections”
  2. Smart Connection embedding 配置：我用的 macbook air m4，可以直接用本地 embedding 模型，我选的是Jina-v2-base-zh-8k，默认的模型效果很差。
  ![[Attachments/e5c300b63651fc75f4d99a4e8a52def2_MD5.jpg]]
  ![[Attachments/abadb9d2285696f147b19cf0328ac052_MD5.jpg]]
  ![[Attachments/4c5ec422bab9ada868e91f81802a82f0_MD5.jpg]] ([View Tweet](https://twitter.com/Yonah_x/status/1923714920639529164))
- 用 twillot 把 X 的书签导出成 markdown，再导入 Obsidian 之后，打开了一篇关于 Qwen 《WorldPM》的推文，它准确的关联到了我用 Gemini 做的解读上面，效果拔群，有了 embedding，从此告别人肉标记双链和标签。 
  ![[Attachments/ebb9584a309f892d91f154881e248487_MD5.jpg]] ([View Tweet](https://twitter.com/Yonah_x/status/1923752942051655780))
- 导入一堆 X 的书签和推文之后，现在Macbook air m4正在全力索引中，已经连续跑了半小时了，没有风扇的 air 居然维持在 70 度左右，看来 embedding 任务的负载对于它来说没什么压力。 
  ![[Attachments/950854deb00cb7b4e897b216611f59fd_MD5.jpg]] 
  ![[Attachments/12271406335451f334cca61f7465f12c_MD5.jpg]] ([View Tweet](https://twitter.com/Yonah_x/status/1923755526598410504))
