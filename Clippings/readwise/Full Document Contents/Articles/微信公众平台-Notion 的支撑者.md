---
人员: 
  - "[[微信公众平台]]"
tags:
  - articles
日期: 2020-06-14
时间: None
链接: https://mp.weixin.qq.com/s?__biz=MzA4MjkwMDcyOQ==&mid=2647743399&idx=1&sn=8684046cb4a74b5dd7651693b4f8398c&chksm=87db5831b0acd127bbc1f2cf549c4cb80c45067ffdf5b5e7d426b280c0602a8c99a33098bd26&mpshare=1&scene=1&srcid=0517rQtkwHrw9hb86BH3Xggm&sharer_sharetime=1652717400073&sharer_shareid=4377c7cd40ae1b88114a1857cc5c2eed#rd
附件: http://mmbiz.qpic.cn/mmbiz_jpg/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqEusVib9PAeK1HOrsrlCj6Ho4nTZtQydCcHf97TsEHVvEoOFJLc6kW49g/0?wx_fmt=jpeg)
---
## Document Note

## Summary

Notion 背后的 3 位先锋， 28 位员工， 9 位投资人。

## Full Document
#### 前言

**2018** 年，就职于字节跳动效率工程团队时，首次了解 Notion，那时他刚在文档中引入 database，在默默壮大中，成为当年 Product Hunt 的年度 App。 惊讶于协作工具的新形态，以及背后的自由度和丰富度，于是开启一段好奇心驱动的兔子洞探索，有感于其 CEO（ Ivan Zhao/赵伊 ）的 2 句话，探究了 Notion 背后的先锋人物的思想，开始了一段令我感动的沉浸之旅。

> - tools for thought；   
> 
> 
> - 我并没有创新什么，我只是在复刻历史； 
> 
> 

当然，我也发现中美 2 地都是同时洞察到文档乃至协同工具领域将会出现重大变化，不管是软件周期的预示还是创作者群体/maker们的洞察，都暗示着这一轮周期的来临。当你嬴面较大的时候，就应该 All In，毕竟人生太短，机遇难求。这是我的感慨，感慨常发自于出局者或者局外人。

如今， Notion 已经成为硅谷炙手可热的初创团队，NYT 今年4月报道其融资 5000 万美元估值 20 亿美元[1]。Notion 的使用者（也是最大的支撑者）也在不断泛化，从硅谷泛化到全球，从常青藤的精英泛化到中国的大学生。于是我想借着机会聊一下 Notion 的支撑者们。

**本文分4部分**

* Notion 背后的先锋
* Notion 的 28 位员工
* Notion 的 9 位投资人
* 对同行的启示

❧

#### Notion 背后的先锋

**2016** 年在一次 designernews.co 社区对 Ivan Zhao/赵伊（以下简称 Zhao ） 的采访[2]中， Zhao 提到，Old Office 是演化而来的，不是最佳设计。Notion 是 post-MS Office，是抛开 Office 的历史包袱来设计。而他设计 Notion 的灵感来源于另一段历史，由美国早期计算机先锋们的想法构成的。他坦诚并谦虚的说到，我并没有创新什么，我只是在复刻历史。

我们打开 notion.so 的 About 页面可以看到，提到 3 个人物的 3 句话，他们分别是 Alan Kay、Doug Engelbart 与 Ted Nelson。而他们 3 人的思想也是「 tools for thought 」思想链里的紧密链接的一环，还是同时代的人物。

> - augment our collective intellect  
> [Doug Engelbart]   
> 
> 
> - amplify imagination   
>  [Alan Kay]   
> 
> 
> - expand our thoughts far beyond text on paper   
> [Ted Nelson] 
> 
> 

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqE3C7GDUh3arnwtnGzxficnq2NZWLTEo1fNGwysgMibOibPfO4ibrdjWy0yw/640?wx_fmt=png)
左→右：Doug Engelbart → Alan Kay → Ted Nelson

##### Doug Engelbart

说起他，除了著名的鼠标之父类介绍外，我更喜欢说他的 3 个故事，以描绘他对我强烈的启迪。

**1950** **年的推理**

Doug 在 25 岁时求婚成功，作为一个在大萧条中长大的人，他从小被灌输了 3 个目标：「上大学，获得一份稳定的工作，结婚」， 现在他都做到了。好像人生没有值得做的事情了。他估计自己一生中还有大约 550 万分钟的工作时间。25岁的他开始思考他需要把这些时间投资到什么事情上去？

> 在决策过程的早期，我就不把金钱当作一个目标。 我是在类似「钱够生活所用就 OK 」 这样的环境下长大的，我从来不认识有钱人。 
> 
> 但到了1950年，在我看来，世界变化之巨犹如我们所面对的问题，以至于我决定寻找一个能为人类带来最大回报的人生目标。
> 
> — Doug Engelbart
> 
> 

在之后持续的几个月里，他推断出这样的想法。

1. 他想将事业专注在**让世界变得更好**上面；
2. 任何让世界变得更好的都是集体**协作**的结果；
3. 利用人类集体的智慧来找到有效的解决方法是问题的关键；
4. 若能大幅度的提高集体智慧（augment our collective intellect），就能推进每一个重要问题的解决；
5. 计算机可能作为大幅度的提高人类这一能力的工具。

**1962** **年的论文**

在 1962 年他首次以成型的论文记录了他在 1950 年前后的思考，并在之后的1968 年举行第一次公开演示以向世人表达他的愿景。

> -通过提高人的智力，我的意思是提高人解决复杂问题的能力，根据自己的需求去理解问题，并推断出解决问题的方法…… 我们说的不是单独的在某个情况下才能使用的技巧，而是指将预感的、实验性的、无形的、和人们对状态的感知与技术、符号、方法、以及有力的电子助力相结合，在这一结合的领域上的新的生活方式 。
> 
> -一方面，人类系统包括范式、组织、程序、习俗、方法、语言、态度、技能、知识、训练等等，所有这些都存在于人类基本的感知和运动能力之中。
> 
> - 另一方面，有工具系统，其中包括媒体，计算机，通信系统等。它们共同构成了增强系统。工具和人类系统共同进化，以制造更好的工具和系统，从而使我们自力更生。
> 
> —《 Augmenting Human Intellect》
> 
> 

我很喜欢 Bret Victor 在 Doug 逝世后介绍他时[3]说到观点，关于 Doug 我们问的最不重要的问题：「他建造了什么?」 。而我们要问的最重要的问题是：「他想创造什么样的世界?」（这些都在 1962paper.org 他的那篇论文里） 。

**1968** **年的演示**

1968 年秋天的计算机大会上 Doug 演示 NLS（oN-Line System）来证明计算机可以用来增强人类智力。NLS 其中包括图形界面、文字处理，超级链接，多人协同的文字处理，以及与相隔 30 英里外的同事进行视频会议（今天这个演示被称为《The Mother of All Demos》，成为了60年代自由主义和黑客文化的顶峰。关于演示的详细资料，可以阅读 1968demo.org ）。

**the features of NLS**

- The computer mouse

- 2-dimensional display editing

- In-file object addressing, linking

-Formatting directives

- Distributed client-server architecture

- Uniform command syntax

- Protocols for virtual terminals

- Remote procedure call protocols

- Compilable "Command Meta Language"

**- Hypermedia 超媒体**

- Integrated hypermedia email

- Hypermedia publishing

**- Outline processing** 

**- Flexible view control** 

**- Cross-file editing** 

**- Document version control** 

- Shared-screen teleconferencing

- Computer-aided meetings

- Context-sensitive help

- Universal "user interface" front-end module

- Multi-tool integration

- Grammar-driven command language interpreter

我们看到 NLS 包含的大部分正在协同工具里演化着，但是 Bret Victor 提醒[3]我们 「不能引用现在来理解过去」。Bret 拿 「Shared-screen teleconferencing」举例，Doug 和我们现在理解的是不同的，可以说是「同音异义词」 ，而这中间的理解的差异正是 Notion 所要去实现。

![图片](https://mmbiz.qpic.cn/mmbiz_jpg/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqEHSoqQ2FkpMvKxIJRnHg2Rruic8A4GOtmyryYcsf4A3QaZiaGkemKJUZg/640?wx_fmt=jpeg)
在 Notion 的办公室，我们会看到书架旁摆着设计师们为 Doug 画的素描。

Doug 在 2013 年过世，他既不富有，也不出名，也没有权势，不过这些从来都不是他的目标。他一直以来所渴望的是一个准备好的世界，为此他想方设法给予各种帮助。

具有讽刺意味的是，他位于加州库比蒂诺的 Tymshare 办公室（收购 Doug 数字资产的公司 ）距离苹果公司总部只有几个街区之遥。而苹果公司这个上万亿美元的生意正是由图标、鼠标、窗口、位图屏幕以及其他 Doug 发明的创意构成的。

> 他扩充人类智力的愿景其实就是给心灵新建一个房子。如果你愿意的话，也可以是一座大教堂。而建大教堂需要时间... 他建造了很好的工具，在1968年demo之后，其他人都想借用他的 demo 以及思想，他自由无私的分享。通过分享，他希望人们帮助他一起建造大教堂，让他早日来到世间。... 我们都是站在一条大鲸鱼上，捕捞着小鱼。  
> 
> 
> — Paul Saffo 给 Doug Engelbart 的悼词
> 
> 

Alan Kay

**2013** 年，Bret Victor 在博客中总结了这一年他惊为天人的阅读，其中提到 Alan Kay。在 Bret Victor 眼中， Alan Kay 是当代最伟大的思想家[4]，但是人们却并没有真正了解他的思想（Alan Kay 给 Bret Victor 的评价也挺高，当今世界上最伟大的用户界面设计头脑之一）。

> 当作者比读者在一个更广泛的背景下思考时，误解就产生了。一个读者可能会在战术（短暂执行的角度）层面思考：「今天我如何才能把工作做得更好？」 ， 而作者却在战略层面（长久的发展角度）思考：「我们如何能创造一个更好的明天？」当真正的进步需要放弃今天的世界并且重新开始的时候，这种误解会变得更加严重。  
> ![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqE7u4nZKuMlDUTeHQONib7cxRHQlGCKewKr8yeCWQYeTwt8bDc2vibfWJQ/640?wx_fmt=png)
> 
> 我们就好像是在树枝上爬的蚂蚁，大部分蚂蚁很高兴在分支上，很高兴的向前走。
> 
> ![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqEnd4cD3k5cRFuvSE2Ar7Yd9pHvrotw4Hvd5ria0ksO4Xiao0eKhdp9bew/640?wx_fmt=png)
> 
> 但是， 有一些特殊的蚂蚁，不知为什么，他们能看到更大的图画。他们可以看到这个分枝是个死胡同。他们可以看到，如果我们真的要向前走的话，我们必须原路返回很长一段。他们通常很难去和那些只能看见自己分支的那些蚂蚁解释。对他们而言，前面的这条路看似乎可以永远走下去。
> 
> Alan Kay 就是那只看到更大的图画的蚂蚁。
> 
> —— Bret Victor 
> 
> 

Alan 并不符合人们对傲慢自大、反社会的黑客或者象牙塔里的计算机科学家的印象。他穿着跑鞋和灯芯绒服饰，留着细小的小胡子，一头蓬松的白发。他是如此的难以想象，以至于你可以在他工作的地方的大厅里碰到他而不会注意到他，即使他是老板。

Alan 很难接受教育。刚开始的时候，他比他所有的同学和大多数老师都了解得更多，而且他不介意大声地展示自己的才能——这种才能让他被赶出了教室，在操场上挨了打。

所以成为另一位硅谷百万富翁，或者接受麻省理工学院的教席，对他的吸引力不及将这种力量投入到每一个被赶出教室的聪明孩子手里。数以百万计的便携式，负担得起的「想象力放大器（imagination Amplifier）」落入手中的八岁的儿童就是他的愿景。

而这个「想象力放大器（imagination Amplifier）」的关键在于构造一个全新的动态的媒介（medium）。Alan 看待计算机的方式是迥异于我们的。他**把计算机当成一个媒介**(Computer-as-medium)，而不是把计算机当成技术(Computer-as-technology) 。

Bret Victor 用了印刷术的类比向我们解释了这个观点。印刷术的发明和传播被广泛认为是第二个千年最具影响力的事件之一，它彻底改变了人们构想和描述他们所生活的世界的方式，并开启了现代化时期。

印刷机的文化重要性与技术（油墨和金属字体）没有太大关系，而是印刷机如何作为一种媒介，以特定的方式放大人类的思想。

印刷品直接促成了一个受过教育和文化修养的社会的出现，(例如)使社会自我管理的想法成为可能。美国宪法只能存在于一个有文化的印刷文化中，在那里(例如)联邦党人和反联邦党人的文章观点可以在报纸上辩论。

当你在看 Alan Kay 的东西时，试着不要去想计算机技术，而是去想一个不同的社会，这个社会人们可以在计算机媒介提供的新的维度下进行自如的思考和辩论。 不要去想「写代码 coding」 (那些是墨水和金属类型的问题，已经过去了)，也不要去想「软件开发者 software developers」（中世纪抄写员只有在非文化社会中才合理）。 而是去思考 modeling phenomena、modeling situations、 simulating models 以及 gaining a common-sense intuition for nonlinear dynamic processes。在这个新社会中，每个受教育的人都可以做这些事。正如我们今天在书写的媒介下阅读或书写复杂的逻辑论证一样简单和自然。

「阅读」 曾经是那些少数神职人员（牧师、僧侣）的特权，他们负责给大众传达不容置疑的神圣真理。而今天，阅读成为了每个人做的事。想象一个世界里，科学不再是少数人的特权，向大众传达不容置疑的真理，而是每个人做的事情。而这个世界就是 Alan Kay 想要创造的世界。

##### Ted Nelson

Ted 为我们熟知的是，他是 Hyperlink（超链接）和 Hypertext （超文本）词语的发明人。然后 Doug 才是实际上这个概念的最早工程实现者。Ted 是文科类型的人，Doug 是工程类型的人。Doug 更关心的是构建解决问题的工具箱和工作坊，而不是推测这种工具可能创造出什么样的文学形式。

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86UuSfJmKdarjJ4NfZp971UYluohNVLIHfEImw2jzTTEyZr5wGEavRhgIaxnYE28BZ0lBgYnfaxRVA/640?wx_fmt=png)
Ted 对这种二分法深感遗憾，因为这种二分法让他长期远离计算机，困扰他一生。Ted 没有能力创造一些东西，即使他能够清楚地想象出来，这在软件世界中并不少见。这个问题是如此普遍，以至于计算机编程的一个非官方规则( Babbage 定律)是: 「任何大型编程项目所需的时间总是你估计的两倍」 。

Ted 一生绝大部分时间都在想「给世界一个更好的表达思考和想法的方法，expand our thoughts far beyond text on paper」 。 他眼中的东西叫做 Xanadu Project，在过去四十多年里，Ted 仿佛生活在平行宇宙中，堂吉诃德般地建造着他心目中的数字世外桃源。

在 Xanadu Project 中 Ted 坚持：

上都计划的评论标注

* 我们必须能够标记任何东西
* 在任何东西上加笔记
* 在任何东西上加注释或加边注
* 用可视化的方式显示相关联的内容

上都计划的超文本链接

* 被引用方的授权
* 不会失效的链接
* 更简易和宽松的版权协议
* 双向链接
* 相连文档之间的并排对照
* 深度版本管理
* 增量出版

![图片](https://mmbiz.qpic.cn/mmbiz_jpg/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqE49ZvH1FWtpRr2QuNib6dq8wqbQuqStsxtzSqfeUnh9HCxsrMxsW5ueQ/640?wx_fmt=jpeg)
在 Ivan Zhao 的书架上，右侧一本泛黄的 Ted 著《Computer Lib / Dream Machine 》

国外媒体喜欢用一副悲情臭老头的语调描述他，大都是着眼于嘲笑他眼高手低以及直言不讳的臭脾气，他得罪了所有在学术、商业和军事计算领域能够帮助他的人，看他对教育制度的疯狂评价。

> 和 Alan 一样，Ted 讨厌教育制度。
> 
> 「 我一辈子都讨厌学校，从一年级到高中，每一分钟都讨厌。我从来没有见过像我这样讨厌学校的人，尽管我认为其他辍学生也是这样。... 在媒体中，一旦你建立了前提，你就几乎锁定了轨道。一旦你有了课程，就有了我们现在所知道的教育系统。换句话说学校是一个雇佣教师的系统，因此每个教师都必须为他一直在做的事情找到一个借口，就像警察需要罪犯一样。」  
> 
> 
> ——Ted Nelson 
> 
> 

但是，我却了解到另一个 Ted，从他在 Doug 逝世纪念活动上带着强烈的悲悯给朋友致辞纪念中感受到**一种真挚**。

> - 我不仅仅觉得失去了最好的朋友。
> 
> - 我觉得我失去了我最好的星球。  
> 
> 
> ——Ted Nelson 
> 
> 

也从 Alan Kay 的口中感受到**一种纯粹**。

> 「 两只眼睛的人构思出了一个辉煌的乐曲，告诉大家如果我们做某件事，那么生活就会多么的深刻和丰富。但是普通的世界将这个辉煌的音乐过滤和简化。最终如果我们听得到一个拨号声就已经算幸运了，盲人大众看不到这个想法、而独眼的人只窥见一瞥，但他们认为他们瞥见的是全部。
> 
> 在我们的时代，如果这些人认为从这一瞥中可以赚到钱，某些事情就会发生，他们想要将其卖给盲人大众市场，他们还会将瞥见的想法稀释。
> 
> 可能会有负责教育的人来帮助盲人学习如何看，这正是科学为人类所做的事，而学习如何去看是一个累人的工作，所以大部分人不感兴趣尤其是做市场的人。
> 
> 重要的是让有两只眼睛的人成为布道者，Ted Nelson和我以及我们的共同英雄 Doug Engelbart 一生都在不知疲倦的指出，在这个世界里，国王不但没有穿衣服，而且他到手机接收不到真正的音乐（Alan 一生最爱书和音乐）」
> 
> —— Alan Kay
> 
> 

Ted Nelson 在他的自传里说，有句谚语叫做 「 In the country of the blind, the one-eyed man is king」。那若是在独眼龙的世界呢？没有人会相信有人能看出立体感。所以在独眼龙的世界，有两只眼睛的人要小心了。

Alan Kay 同样引用了那句谚语「In the country of the blind, the one-eyed man is king , In the country of the blind, the one-eyed people run things. and the two-eyed people are in for a rough time」。「 我们的文明很大程度上归功于那些给人启迪的并遭受苦难的极少部分拥有两只眼睛的人，Ted Nelson 就是其中之一，我们应该感激他。 」

❧

增强人类智力这对大多数人来说没想过，不知道怎么样想的事情，却在百年来驱动人类的天才先锋长老冒险家在工具的路途上研究。

他们可以花费他们近半生的时间，虽遥远但并不放弃。就像植物一样，缓慢而坚定的生长着。类似这种长期的愿景才能驱使一个人或者团队走得更远和更深。Notion 的愿景也是增强人类的智力这个领域，Ivan Zhao/赵伊 一直以来社交媒体签名都是 tools for thought。其实除了字面意思之外，他还指的是一本同名科技思想史的书。

书的作者也是来自那个时代天才、先锋、长老以及冒险家聚集的施乐实验室。《tools for thought》 开篇指出计算机革命（作为增强人类智力的初衷）远未达成目标。

计算机这个硬件的是智力增强技术的产物，来源的背景计算航海天文中的数据，但是却是一开始就是被设计来作为扩大思维和交流的工具。书中着重挖掘了智力增强的思想链，介绍了思想链背后的一系列希腊哲学家、英国逻辑学家、匈牙利数学家和美国发明家的故事。

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86UuSfJmKdarjJ4NfZp971UY4wANPLv6xEojUkrKTuBWBQ6FACCapwl9CzScKJ4rxgfyPFK57IhbIA/640?wx_fmt=png)

> 在 Babbage 和 Boole 之间，他们是一体2面，这2种动机在几个世纪里面指引或者内驱世人去想象并成功塑造出了计算机。 
> 
> 一方是科学家工程师，总是渴望有一个设备来分离他们的创造力和事务工作，解放他们的思想去追求更有趣的问题，繁琐的计算交给机器。 
> 
> 另一方是有个更抽象更恢弘的欲望，用一套数学符号系统去捕捉人类理性思考的过程。
> 
> —— 《tools for thought》
> 
> 

人们可以用计算机来扩大思维和交流，作为智力工作和社会活动的工具，这个想法并不是主流计算机工业或正统计算机科学的发明，甚至也不是自制计算机专家的发明。他们的工作植根于古老的、同样古怪的、同样有远见的工作。 除非你知道思维放大技术从何而来，否则你无法真正猜测它将走向何方。

Notion 是人类智力增强计划在现代的一种表现，是 Ivan Zhao/赵伊 阅读学习智力增强的思想链上的历史人物以及他们遗存的产物。我们同时也看到了欧美的年轻人都不同程度的在实践这种愿景，除了 Notion 之外，还有如 Roam Search、Workflowy、Liquid、Obsidian、coda、Airtable 等。

❧

#### Notion 的员工

我关注了 Ivan Zhao/赵伊 的 twitter [5]，线上探访了他的27位关注者。27 位 Notion 员工，查看了他们的 LinkedIn ，了解其职业经历，也看了他们多姿多彩的个人主页，看了他们的文章和眼中的世界。

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqEuaS77XsSHfvbS5nyhLo1wwWPicVeNgG7H5otHl9e99BJAbFz5fYuPBg/640?wx_fmt=png)
- 在多样性上，Notion 团队的都有接近1/3的女性或者少数族裔的雇员；

- 能观察到他们的用人策略：能力全栈、B端经验以及5-10年丰厚经验；

- Stripe（简直像硅谷初创企业的黄埔军校）的关系带来一批员工；

##### Leader

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86UuSfJmKdarjJ4NfZp971UYWqtAV4bg37Bic81tDzrp5YmoSPJiboYH9ZEBHwWotvicvjxbQcZ7vwOTQ/640?wx_fmt=png)
**CEO** ：Ivan Zhao/赵伊

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86UuSfJmKdarjJ4NfZp971UYfKHib20HmB1LvHPBiczhk6LmCIqGqMRqRRFYakM6gHS8hmLSpxVgEXVA/640?wx_fmt=png)
**Ivan Zhao/赵伊🇨🇦** 1987年出生于中国新疆，幼时随父母迁居北京读小学，小学时候就开始接触编程。后举家移民加拿大，在加拿大上的高中和大学。

大学时主修认知科学，曾经开发来一款基于维基百科的知识探索付费游戏 3 Degree 并成为 AppStore 教育类畅销 App。大学时候主修的认知科学课程让他接触到了前面提到的3个计算机历史上的先锋人物。

毕业后 Ivan Zhao/赵伊在 Inkling 从事教育产品相关的设计工作，认识了他的天使投资人 Matt Macinnis（Inkling 的 CEO，前苹果公司全球教育市场的高级经理）。

**联合创始人** ：Simon Last

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86UuSfJmKdarjJ4NfZp971UYQxdjWhthicqwDkkpyaciclfuk7oa8I5eR1ekcHMac5QDSpIYEiccz9r0A/640?wx_fmt=png)
**Simon Last** 有一张长脸，是一个设计开发兼具的全栈工程师，马里兰大学计算机专业毕业。在 Nebula 和 Space Telescope Science Institute 做过短暂的实习软件工程师。和 Zhao 一样喜欢摄影和数字艺术，在做 Notion 之前做过很多好玩的实验包括游戏，大都是给儿童和青少年学习编程创造的。

**COO**：Akshay Kothari

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqEWGwFWCvFCIEEOIrdJDvGOWNmhELwBL9u0haKqibDEONpZucWNlkkr9w/640?wx_fmt=png)
现年 33 岁，印度裔，也是 Notion 的早期投资人。拥有10年的产品管理经验，2018年年底加入 Notion 之前是 LinkedIn 国际业务产品负责人。

出生在印度的第七大城市 Ahmedabad，考上斯坦福大学读电气工程学位，期间创业开发了新闻阅读业务 Pulse。3年时间积攒了3000万的用户，被LinkedIn 以 9000 万美元收购。加入 LinkedIn 之后不久回到印度，一直在印度本地开拓市场，5 年后升为 LinkedIn 国际业务的产品总负责人。

**Head of Engineering**：Michael Manapat

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqE5VqHAYFQYCo3XTHqibYWFV7qBZVUQQpNDJ7A6fdLmewicSXib9CORGeQA/640?wx_fmt=png)
亚裔面孔，2020 年初加入Notion，10年工程经验。职业生涯从哈佛大学毕业之后加入 Google → Stripe 。Stripe 从事 6 年的机器学习工作，在 Stipe 主导了一门支付行业的欺诈检测的机器学习课程。（想起 Peter Thiel 利用当年在 PayPal 积攒到的支付欺诈解决方案，开设了一家大数据服务公司 Palantir Technologies ，首单客户就是美国 CIA）

**Head of Customer Success**：David Apple

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqElMTdQJiaO6uvMESlrDaq4EHwRYnnzFtoYsqrSyibuV43jPng86MYjVqQ/640?wx_fmt=png)
法裔，2019年加入Notion，一年后成为 Customer Success 负责人。有17年的销售经验，入职 Notion 之前是著名的线上调查问卷公司 typeform 美国区的总经理和主管 Customer Success 的 VP（副总裁）。

**Head of Growth**：Jamie Quint

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqECHNHAiaeOx2VQXjzYEiaFGCJF2gWX2mc2czBDwELgJib1G899ica71icXicQ/640?wx_fmt=png)
12年的增长经验，曾经2次拿下 Y Combinator 的年度冠军。2016年开始服务于 Reddit，负责增长。期间同时为多家初创公司提供增长咨询服务。与知名投资人兼增长专家 Andrew Chen 熟识。

**Head of Platform & Partnerships**：Cristina Cordova

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqEgkaMtSibvVet6FX65FTrV2jFvfjxoxLpiaXTMhQOaX7RFUhal9vLZ8ibg/640?wx_fmt=png)
拥有10年平台伙伴关系经验，与 COO （Akshay Kothari）相识，其职业的初期参与Akshay Kothari 的创业公司 Pulse 负责商务。其后在 Stripe 任职长达7年，成长为 Stripe 的商务负责人。

Notion 的几位 leader 都出身于较好的科技公司，拥有10年的创业管理经验，带有更多元的视角，加入 Notion 之前多服务的市场多与企业服务有瓜葛。在 30岁左右选择 Notion 这一更具可能性，有更多赢面的业务。

##### Designer

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86UuSfJmKdarjJ4NfZp971UYrXQ29PAaqa1rln1whDCP0wBPWjFWInMeAhhLmEzG01iaz2uUOIiaqjcA/640?wx_fmt=png)
**CEO 与联合创始人**：Ivan Zhao/赵伊 & Simon Last 都有设计背景，Notion 标志性的插画风格出自 roman \_m之手。除了创始人之外，又多了 2 位设计师：Ryo Lu 和 Cory Etzkorn。

**Ryo Lu** 🇨🇦，华裔，，有一头标志性的黄发。其也是来自于之前多次提到的 Stripe，此外还有短暂的 Asana 经历，在没来美国之前就职于国内初创支付解决方案公司 Ping++。

**Cory Etzkorn** 是一个玩瑜伽，玩音乐的全栈设计师。之前在《华尔街日报》做过设计工作，在 Fuzzco 和 Ghostly Ferns 做前端开发。更早之前创业做了一款音乐社交 App：Hendrix。

##### Engineer

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqE6bdL0D1Ka4TVw6mbhibU1hUb8tQ9sNrw561H7tk36ebaDl0OOrRStbQ/640?wx_fmt=png)
我们可以看到有3位女性工程师，并且多位都是10年+的经验。这里重点说一下一位有趣的工程师，也是第一位加入 Notion 的工程师。

**Chet Corcos**

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqEPw9iauEqZiaY3KibqoN50oGmOlkiaslkQDLPLKJBicnM0HqIqn0V1yqgyLA/640?wx_fmt=png)
曾在 SpaceX 实习过并为雷达设计了一个控制系统，7年的工程经验，早在2017年就加入了 Notion 团队。在加入 Notion 之前，是 Bret Victor 的粉丝，带有类似 tools for thought 的愿景，受 Bret Victor 的启发要开发 Explorable Explanations 的编辑器。在和朋友分享的时候被告知了 Notion 正在做一样的事情，于是加入了 Notion。

##### Marketing

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqE9GG5Cu2IS8XzP4ibpviaKS1h83EjgwvynaAibpMaJx79Sib1GPR2Ja3dyQ/640?wx_fmt=png)
我们看到市场团队的多位员工都是7年+的经验，并来自于类 SaaS 服务的公司。这里介绍一位在以色列参军过的25岁小伙子 Ben Lang。

**Ben Lang**

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqEIpYIYIcoJIaaeia8daeY3Kvm0zoYJ7ibiblOiawibp0AWAIcW5CNzsGOggg/640?wx_fmt=png)
他最有趣的点是创立国际鹰嘴豆日（5月13日），每年的这一天一起吃鹰嘴豆，以色列人善于烹调鹰嘴豆。他高中毕业之后就搬到以色列，在以色列国防军的一个情报单位服役。他同时最早做了notionpages.com 一个民间 Notion 模版集合的网站。也是十分启发于 Doug Engelbart 的智力放大的愿景，之后逐步加入 Notion 的。

❧

#### Notion 的投资人

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqEYtOZcKbzgplbtlpKrdRe2f4WmMVdKMa8oMq7b41N5ZCTMlWchBB3tA/640?wx_fmt=png)
这是 Notion 网站公布的早期8位投资人，不过这里肯定不能少掉的一位就是，**Ivan Zhao/赵伊 的妈妈**。2016 年，Zhao 从妈妈那里借了 15 万美元以维持运转，彼时，Notion 3年前融资的 200 万美元快花光了。

8位投资人的背景和豪华的业绩，侧面反映了那些嗅觉敏感的人感知的趋势大概是什么。

* Shana Fisher：纽约风投大牛，投过 Pinterest, Stripe, FiftyThree。
* Naval Ravikant：股权投资界的鼻祖，投过 Twitter, Uber, Yammer。
* Ram Shriram：Sherpalo Ventures 合伙人，Google 董事。
* Phin Barnes：First Round Capital 的合伙人。
* Aydin Senkut：Felicis Ventures 的创始人，1999年任职Google，第一任产品经理，帮助推出了谷歌前10 个海外网站。经典案例Fitbit 和 Shopify。
* Matt Macinnis：Ivan Zhao 前老板，Inkling的 CEO，苹果前全球教育市场的高级经理。
* Elad Gil：Twitter 前 VP，投过 Airbnb, Stripe, Square, Pinterest。
* Mike Vernal：红杉资本合伙人，facebook 负责平台关系的前VP。

##### 投资人的视角

投资人 Ram Shriram 于 2013 年投资了 Notion，他说「我们使用的许多产品都是 1990 年代设计的」。

在70年代，我们开始数字化文档。我们把纸片变成了文字处理器(WordStar) ，把会计账簿变成了电子表格(VisiCalc) ，把教授的幻灯片变成了演示文稿(Harvard Graphics)。

在接下来的40年里，数字世界完全改变了。操作系统发生了根本性的变化。数据库已经超越了关系。电视从广播到有线电视再到在线视频。搜索引擎和社交网络取而代之。

令人惊讶的是，文件，在基本和结构层面上，并没有改变。但是我们使用文档的方式已经完全改变了。我们不再试图将物理类比数字化。我们使用文档作为管理团队的工具。

软件产业 60 年，而商业软件的历史只有40年。我们看软件产业的周期 [6]历史，互联网与移动互联网快接近末期，新的周期已经来到。或许 Notion 就是这个周期里面的一个顺应周期的东西。

![图片](https://mmbiz.qpic.cn/mmbiz_png/8qCNluNm86WkhLmKzNkaUlNXIN2jjxqEGvOZeC8jszUMsGy2SgLoUomkR8Mkr2O3kysAHULbtS9gWmtDe60FHg/640?wx_fmt=png)
❧

#### 对同行的启示

对国内产品的一些启示。

* 做协作工具的想走远，得有类似「早知道就好了」 的强大的愿景；
* 全球化的配置，组织的多元与专业度上向硅谷一线科技公司对齐；
* 如果创新没头绪，请去读 Doug、Alan 及 Ted；

**The Computer Revolution** **Hasn’t Happened Yet.**

**❧**

##### References

`[1]` NYT 报道融资新闻: *dwz.date/barN*  

`[2]` Design News 采访: dwz.date/batv  

`[3]` 读Engelbart ：worrydream.com/Engelbart/  

`[4]` 读Alan Kay：worrydream.com/Links2013/  

`[5]`Ivan Zhao：twitter.com/ivanhzhao  

`[6]` 软件的下一轮周期：dwz.date/bat2  

`[7]` Shuo Yang：medium.com/@yang140
