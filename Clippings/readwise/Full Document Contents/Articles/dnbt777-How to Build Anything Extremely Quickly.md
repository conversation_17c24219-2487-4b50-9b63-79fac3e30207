---
人员: 
  - "[[dnbt777]]"
tags:
  - articles
日期: 2024-06-07
时间: None
相关:
  - "[[mvp]]"
  - "[[alpha]]"
  - "[[color]]"
  - "[[paper]]"
  - "[[topic]]"
  - "[[wisdom]]"
  - "[[写作]]"
  - "[[outline]]"
  - "[[storage]]"
  - "[[utility]]"
  - "[[writing]]"
  - "[[momentum]]"
  - "[[planning]]"
  - "[[practice]]"
  - "[[sections]]"
  - "[[speed up]]"
  - "[[speedrun]]"
  - "[[speedups]]"
  - "[[algorithm]]"
  - "[[algorithms]]"
  - "[[brainstorm]]"
  - "[[components]]"
  - "[[efficiency]]"
  - "[[productive]]"
  - "[[user input]]"
  - "[[whiteboard]]"
  - "[[loading-bar]]"
  - "[[data storage]]"
  - "[[fundamentals]]"
  - "[[web scraping]]"
  - "[[border radius]]"
  - "[[data analysis]]"
  - "[[data cleaning]]"
  - "[[data pipeline]]"
  - "[[collected data]]"
  - "[[simulation data]]"
  - "[[outline speedrunning]]"
  - "[[tool measurement data]]"
  - "[[performance-based field]]"
  - "[[do not perfect as you go]]"
  - "[[生产力与自我提升]]"
  - "[[data collecting component]]"
  - "[[data pipeline application]]"

链接: https://learnhowtolearn.org/how-to-build-extremely-quickly/
附件: https://7vaf67.p3cdn1.secureserver.net/wp-content/uploads/2024/06/image-3.png)
---
## Document Note

# 知识点笔记
  ## 1. 大纲
  - **标题**：How to Build Anything Extremely Quickly
  - **作者**：dnbt777
  - **来源**：learnhowtolearn.org
  - **主要内容**：
  1. 引入：介绍“轮廓速度跑”方法
  2. 轮廓速度跑算法
  3. 重要性和基本原理
  4. 常见错误：加载条写作
  5. 实例分析
  - 写作
  - 编程
  6. 结论：构建以学习为目标
  ## 2. 主要观点
  - **轮廓速度跑**：通过递归性地制定项目大纲并快速填充内容，可以显著提升工作效率，达到传统方法的10倍速度提升。
  - **避免完美主义**：在完成整个项目之前，不要过于追求完美，这样可以减轻压力，提升创造性思维。
  - **基础的重要性**：掌握基础技能（如大纲制定）是提升效率的关键，许多人低估了这些基础的重要性。
  - **改进算法**：提升工作速度的最佳方式是优化工作方法而非单纯加大努力。
  ## 3. 关键概念
  - **轮廓速度跑（Outline Speedrunning）**：一种项目管理和执行方法，通过创建和细化大纲来加快工作进程。
  - **递归性大纲**：不断将项目分解为更小的部分，直到每个部分足够小，易于处理。
  - **加载条写作**：一种传统的写作方式，从头到尾逐句写作，效率低下。
  - **基本技能**：指在任意领域中，基础的知识和技能，这些是进一步提升的基石。
  ## 4. 相关案例
  - **写作**：
  1. 确定主题
  2. 快速制定大纲
  3. 递归分解每一部分
  4. 快速填充内容
  5. 进行最后的修饰和完善
  - **编程**：
  1. 确定程序的主要功能
  2. 按功能分解程序
  3
  构思如风行， 
  快速构建梦想， 
  时间无所惧。
  Building with great speed, 
  Ideas take flight and soar, 
  Dreams in hand, we create.
  秋风轻拂， 
  枫叶飘落如梦， 
  静夜思念。
  1. How does the practice of outline speedrunning specifically enhance both productivity and creativity in the building process?
  2. What are the potential pitfalls or challenges one might encounter when implementing outline speedrunning for various types of projects?
  3. In what ways can the principles of outline speedrunning be adapted to different fields beyond writing and programming, such as art or project management?

## Summary

本文讨论了如何快速构建项目，提出了一种称为“轮廓速度跑”的方法。这种方法旨在通过递归地制定项目轮廓并迅速填充内容来提高效率。文章的主要步骤包括：首先制定项目的轮廓，然后对每个项目逐层细化，直到每个部分足够小，最后快速填充这些部分，最后再进行完善和修正。强调在填充过程中不要追求完美，这是常见的误区。

轮廓速度跑的关键要素是：递归性和不完美原则。作者指出，良好的规划是成功的基础，而轮廓则是规划的重要组成部分。许多人可能低估了这些基本原则的重要性，实际上，许多高效能的提升来自于对基本原则的重新评估。

在文中，作者通过自己的经历说明了轮廓速度跑的有效性，特别是在写作和编程方面。与传统的“加载条写作”相比，轮廓速度跑能显著提高工作效率，使创作过程更加轻松愉快。

总结来说，轮廓速度跑是一种高效的项目构建方法，强调快速推动项目进度和后期再优化的理念，能够帮助人们在创作和开发中获得更好的体验和成果。

---

**[[问题]] 1：** 轮廓速度跑的基本步骤是什么？

[[答案]]：基本步骤包括：制定项目轮廓、递归细化每个部分、快速填充内容、最后进行完善。

**[[问题]] 2：** 在轮廓速度跑中，为什么不应该在填充过程中追求完美？

[[答案]]：因为在填充过程中追求完美会导致效率低下，影响整体进度，应该等到完成后再进行优化。

**[[问题]] 3：** 轮廓速度跑相比于传统的“加载条写作”有什么优势？

[[答案]]：轮廓速度跑能显著提高写作和编程的效率，使创作过程更加轻松愉快，减少压力，提升作品质量。

## Full Document
![](https://7vaf67.p3cdn1.secureserver.net/wp-content/uploads/2024/06/image-3.jpg)
**Do “outline speedrunning”:** **Recursively outline an MVP, speedrun filling it in**, **and only then go back and perfect.**

This is a ~10x speed up over the ‘loading-bar’ style (more on that below)

Don’t just read this article and move on. Go out and do this for the very next thing you make so you can get in the habit of doing it.

###### Outline speedrunning algorithm:

1. Make an outline of the project

2. For each item in the outline, make an outline. Do this recursively until the items are small

3. Fill in each item as fast as possible

* You’ll get more momentum by speedrunning it, which feels great, and will make you even more productive

* **DO NOT PERFECT AS YOU GO**. This is a huge and common mistake.

4. Finally, once completely done, go back and perfect

* Color the title text, figure out if buttons should have 5% or 6% border radius, etc

* Since you’re done, you’ll be less stressed, have a much clearer mind, and design your project better
* *And hey, you’ll enjoy the whole process more, and end up making more things over the long run, causing you to learn/grow more*

CRITICAL requirements for outline speedrunning:

* outline recursively
* speedrun
* **DO NOT PERFECT ANYTHING UNTIL DONE**

###### Outlining is a fundamental of building

Outline speedrunning may seem basic. That’s because it is. *Planning* is a fundamental of *doing*, and *outlining* is a fundamental of *planning*.

**Much of becoming really efficient is about getting extremely cracked at the fundamentals (many of which you probably mistakenly dismiss).**

*This is recursive btw, because fundamentals typically have fundamentals.*

*its conceptual building blocks all the way* down
I knew about outlining since I was little but didn’t do it until I was 20. After I started it took time to get cracked at outlining and refine it into outline speedrunning.

**There is immense amounts of wisdom/utility/alpha in reevaluating any fundamentals that your mind dismisses as unimportant. Much improvement in any performance-based field comes from fixing these misevaluations.**

You may already do outline speedrunning. If you’re not, it’s a 10x speedup over the classic ‘loading bar’ style.

*Generally, the best speedups come from *improving your algorithm*s, rather than ramming your head into the task harder*.

###### Examples

###### Write large docs faster

*My attempt at speedrunning each method for a minute or so. left: loading-bar. right: outline speedrunning*.
**Loading-bar writing:**

Common mistake: ‘loading bar writing’ – starting at the beginning of the document and writing sentence by sentence, like a loading bar going through the document.

I wrote like this until I was ~20. It made me hate writing.

Now writing is easy and quick. My writing (and at the time, grades) improved substantially because I could allocate more time towards quality with a clearer, less stressed mind.

**Outline speedrunning writing:**

1 Get topic to write about

* (optional) brainstorm the general plot of the paper

2 quickly write the outline (sections)

3 repeat 2 for each section recursively, until the lowest-level sections are small enough to not need outlines

4 speedrun (without caring about quality AT ALL) filling in each outline (starting at the lowest level) until the whole doc is filled out

5 Enjoy the feeling of being 90% done while you go back and perfect the doc, color the title text, add pictures, etc (the fun part!)

Do this to effortlessly improve both speed and quality of writing.

###### Program faster

Going from loading-bar to outline speedrunning significantly speeds up programming

###### **Outline speedrunning** for **programming:**

1 Figure out what the main purpose/function of your program is.

2 Break the program into parts grouped by functionality.

3 repeat steps 1-2 for each part, unless it is very small.

4 implement the components starting with the lowest-level

Example (simplified version of a data pipeline I built):

Idea: Data pipeline application

1 Main purpose: collect tons of data and analyze/store it

2 Breaking it down into components:

* data collecting component
* data cleaning/formatting component
* data analysis component
* data storage component

![](https://7vaf67.p3cdn1.secureserver.net/wp-content/uploads/2024/06/image.png)
3 repeat step 2 for each part. Breaking down the data collecting component into subcomponents:

* Component: list of data collection methods (web scraping, simulation data, tool measurement data, etc)
* Component to get user input for choice of data collection method
* Component to execute chosen data collection methods
* Component to send collected data to storage

![](https://7vaf67.p3cdn1.secureserver.net/wp-content/uploads/2024/06/image-1.png)3 repeat step 2 for each other component and subcomponent, unless they are small
![](https://7vaf67.p3cdn1.secureserver.net/wp-content/uploads/2024/06/image-2.png)
This can be done on a whiteboard, drawn on paper, in your head (can be subpar) or outlined like an essay. The most efficient medium depends on your project. You’ll get a feel for what works best by just practicing this.

*Build to learn*
