---
人员: 
  - "[[shaonan]]"
tags:
  - articles
日期: 2022-09-09
时间: None
链接: https://mp.weixin.qq.com/s?__biz=MzkyNjM1ODA2MA==&mid=2247484677&idx=1&sn=320388ccc1c2dbedcc3cb5eda360e365&chksm=c239ca46f54e43503b0b4853e54bb4d880ebc85b809e910d8463bf5e1334ba3a77295594853d&mpshare=1&scene=1&srcid=0909hCAqUGwLceNVhiEu0iUJ&sharer_sharetime=1662705005662&sharer_shareid=4377c7cd40ae1b88114a1857cc5c2eed#rd
附件: https://mmbiz.qpic.cn/mmbiz_jpg/1p2RY49Bj1r976FZ9NCNVNJXSZgiaqJUGFdYSzxKtDcebfHc2jjeGJCWEJyV3gK1n6rLQacCXAD5XiaTaibWFYGnQ/0?wx_fmt=jpeg)
---
## Document Note

## Summary

我们大部分时间都盯着眼前的东西，做出决策。但是很少思考，我们在做出选择的时候，是否考虑给自己增加选项，而不是选出当下最好的那个。 by Aaron Swartz

## Full Document
这是一个系列的文章「原始神经 | Raw Nerve」，这是一系列关于在生活中变得更好的作品。

作者 [亚伦·斯沃茨](https://mp.weixin.qq.com/s?__biz=MjM5NzI0Mjg0MA==&mid=2652373907&idx=1&sn=12a81c8ad70e6b00e1e81623b1172bcb&scene=21#wechat_redirect) 写下这个系列的动机来自于一个思考：

> 我们大部分时间都盯着眼前的东西，做出决策。但是很少思考，我们在做出选择的时候，**是否考虑给自己增加选项，而不是选出当下最好的那个。**
> 
> 

全文共七篇，这次编辑了有启发的三篇，后续会逐渐全部翻译至专题。

![图片](https://mmbiz.qpic.cn/mmbiz_jpg/1p2RY49Bj1r976FZ9NCNVNJXSZgiaqJUGxlG8ZmUjJBnYpVW2aBlKVyFDhxTpfSUMURTJBwCKdap5m8eicYicFiaZw/640?wx_fmt=jpeg&wxfrom=5&wx_lazy=1&wx_co=1)
互联网之子：Aaron Swartz

##### 倚靠在痛苦中

> 在这篇文章中，对我有两个启发。一个是关于心理上的痛苦，因为不像生理上的痛苦可以感知和有明确的获得，许多时候面对痛苦总是容易逃避。另一个是很喜欢作者说的**「倚靠」**的概念，不是去逃避或者靠意志力抵抗，而是和痛苦在一起，将其从无法面对的事情，变成锻炼自己的事情 —— 而自己在此之前给自己或者别人的建议都是「抵抗」，实际上这种纯粹的意志力是无法解决问题的，也会让自己在其中筋疲力尽。
> 
> 

**重要的不是治愈，而是带着病痛活下去。**

锻炼总是让人痛苦，所以我们总是间断；当你好不容易完成一次锻炼后，第二天依旧很痛苦。但为何我们还是坚持下来了，因为长期来看，痛苦能让我们变得更强大。而之前痛苦的情况，也会随着锻炼而减轻。

**心理上的痛苦也一样**，只不过许多人没有意识到，每次遇到类似的痛苦就像把手放在炙热的火炉一样，赶紧退缩然后拒绝思考。

但这些痛苦的思考，往往是对我们最重要的事情之一：我们决定要做什么、我们最关心的关系、对未来的重大决定、面临的危险风险等等。虽然我们知道这些事情重要，但是总是愿意把头埋在土里 —— 但问题在于，如果我们从不去考虑他们，我们就永远无法胜任这些事情。

试着换个思路，**痛苦不是需要推迟和避免的可怕事情，而是一个信号，表明你正在变得更强大** —— 一种品尝和享受的东西。这是让你变得更好的原因。

把每个痛苦都当做一次变强壮的机会，所以当你开始感觉到心理上的痛苦来临时，不要退缩并畏缩 —— 倚靠在痛苦中。

但许多痛楚实在是难以坚持，该如何解决呢？答案并不是意志力，而是**降低碰触的深度和提高碰触的频次**。许多职场中的人一旦遇到负面反馈，就会退回到原地，然后试图等着一个完美的版本再提交，但是这样的痛苦只会更加强烈。更好地方法是，快速的迭代并获得反馈，这样虽然可能还是许多负面反馈，但是至少糟糕程度会越来越轻。

诀窍是不逃避，这样整个人生观就会发生许多改变，这样整个世界到处都会成为了你的试炼场，让你变得更强大。

所以**从小处着手总是好的**，不要一下子试图解决巨大的问题，而是坐下来，承认思考这些事情是痛苦的，然后开始慢慢地**拆解**，你会看到自己越来越胜任这件事情。

**不要逃避痛苦或者避免痛苦，而是倚靠在其中。**

![图片](https://mmbiz.qpic.cn/mmbiz_jpg/1p2RY49Bj1r976FZ9NCNVNJXSZgiaqJUGR652ib8ha5Jia7BIJ9McxicybjRFyLACRhGFRAZ4gpziaMIpT9tib470tlg/640?wx_fmt=jpeg)
##### 面对现实

> 之前问 @fonter ，我一直想把李光耀的专题给完善了，但是每次都觉得难以下手；他的建议是：不要试图一次完成所有，毕竟那是一个伟人；相反，从对你最有启发的方面去写，记得多少写多少就好了。**许多时候拖延并不是自己的意志力问题，而是选择了一个看似「困难」的目标**，然后由于足够困难，所以总是花时间在准备中，而始终处于未完成……
> 
> 

另，**「顿悟成瘾」**这个词绝了，关键不在于「顿悟」，而在于之后你对自己的行为做了什么样的改变，哪怕是微小的改变，这才是重要的。

看起来雄心勃勃的人经常陷入一个陷阱，即看不上小的问题，总是希望能在「改变世界」的赛道上一鸣惊人，所以他们不会在一周两周内动手，而是不断地「思考并等待机会」，从外界的角度来看，他们似乎总是在等待一些事情的到来。

但从另一个角度来看，选择非常困难的事情和选择非常容易的事情，本质上是一样的 —— 选择简单的目标时，你知道总能搞定；**而选择困难的目标时，你知道自己永远不会失败**（因为事情还没发展到那一步呢，再等等）。

其实重要的的是做一些务实的小挑战，如果你的创业公司最终能赚到一百万美元，那么他能从赚十美元开始么？与其将所有的成功都推到无限期的未来，不如看看现在能否通过一个小的测试。

**重要的是让这些小事在真实的世界中发生。**

要小心 Chris Macleod说的「顿悟成瘾」：**每当他们偶然发现一些改变生活的方法时，都会感觉到精力充沛，而不去推进任何现实世界的变化，然后当他们回到现实生活中，发现一切照旧，然后继续开始思考如何解决，之后继续因为新的顿悟而激动。**

选择简单的事情，你不会有成就；

选择困难的事情，你不会有失败；

选择艰巨的事情，可能会失败，但尝试去成功。

##### 修复机器，而不是人

> 我曾经常在团队大喊大叫，因为问题总是一而再再而三出现，那时候习惯性归罪于人不行，缺少主动性，不敢承担责任云云。
> 
> 

但现在再回看，当时精力往往都放在了产品上，忽略了这个产品背后「系统」的设计。将人骂一顿，是一种智力上的偷懒，也犯了基本归因谬误 —— 并不是有人想搞砸，而是规则和流程不清晰导致他们处境艰难。

许多产品设计不好，并不仅仅是负责的产品经理能力不行，而是设计产品背后的那套系统的设计师不行。反之，如果需要设计一个好的产品，那么对背后系统的投资，也不可或缺 —— 虽然可能投入回报周期极长。

通用汽车在弗里蒙特的工厂是一场灾难，管理层和工人矛盾极大。一方面是工人不遵守规矩，各种摸鱼；而管理层也一直用各种惩罚手段，这更加激化工人的反抗之心，对产品更加漠不关心。

1982 年，通用汽车关闭了这个工厂。两年后丰田接手，奇怪的是不满和缺勤消失了，工人们开始喜欢上班，因为在丰田，劳资双方认为自己在一个团队中，**当工人陷入困境时，经理没有对他们大喊大叫，而是问他们如何提供帮助并征求建议**。

![图片](https://mmbiz.qpic.cn/mmbiz_png/1p2RY49Bj1r976FZ9NCNVNJXSZgiaqJUGFnGpHoJBjiclpO6eHJGUI5RGr9vyjGD3SicNHkopgco3f7QMYRHVdxHw/640?wx_fmt=png)
**一个组织不仅仅是一堆人，还是一套结构。**如果你只把人扔进仓库，里面有一堆零件和一本手册，那么肯定是一个灾难；相反，应该精心设计一个结构，零件在传送带上前进，每个人完成自己的步骤。

**当系统不起作用时，仅仅对其中的人大喊大叫是没有意义的 —— 对着齿轮大喊大叫并不能修理好机器。**

一个人的目标也是需要由组织完成的，但这并不意味着事必躬亲 —— 有些你擅长的事情固然自己做就好，但是如果你不擅长的，应该向后退一步，设计好系统，然后让系统发挥作用。比如你要建造一个树屋，但自己又不擅长建筑，那就应该设计好整体的目标，然后去找擅长建筑的人，把自己从建筑师的角色上解雇掉，而是看这个系统如何运转（比如如何设计报酬），因为你的目标是一个树屋，是不是自己建造的重要吗？

我们之所以经常对着人大喊大叫，是因为基本归因谬误（by Lee Ross，1977） 。我们习惯性的将汽车邋遢的司机认为是糟糕的人，而不去考虑他的匆忙性；看到警察殴打抗议者，认为是可怕的人，而不是可怕的训练 —— **我们习惯于将人们的行为归因于他们的个性，而不是他们的处境。**

所以，当有人搞砸事情的时候，我们习惯于大吼大叫，但实际上并不解决问题 —— 因为没有人愿意搞砸。所以有效的不是大喊大叫，而是改变周围的环境和系统，让这些出错的事情不再出错。

当你对某人感到不满的时候，并不要去大吼大叫，试图控制他们的行为，这只会让他们更加抵触。相反，应该聆听原因并去改变环境，这样他们才能真正的改变。

##### References

`[1]` **亚伦·斯沃茨:** *http://www.aaronsw.com/*  

`[2]` **原始神经**: *http://www.aaronsw.com/weblog/rawnerve*  

`[3]` **退后一步**: *http://www.aaronsw.com/weblog/stepback*  

`[4]` **相信你可以改变**: *http://www.aaronsw.com/weblog/dweck*  

`[5]` **客观看待自己**: *http://www.aaronsw.com/weblog/semmelweis*  

`[6]` **倚靠在痛苦中**: *http://www.aaronsw.com/weblog/dalio*  

`[7]` **面对现实**: *http://www.aaronsw.com/weblog/anders*  

`[8]` **珍惜错误**: *http://www.aaronsw.com/weblog/geremiah*  

`[9]` **修复机器，而不是人**: *http://www.aaronsw.com/weblog/nummi*

![图片](https://mmbiz.qpic.cn/mmbiz_jpg/1p2RY49Bj1p4zib7ZYG06Dmy2C4oqgMGZxJRHT99f6iaFugTKUAiaXSyCwHeuEojQYS2WznSEuS8By1nGI4j6WLbA/640?wx_fmt=jpeg)
