---
人员: 
  - "[[flomoapp.com]]"
tags:
  - articles
日期: None
时间: None
链接: https://help.flomoapp.com/thinking/reading.html
附件:
---
## Document Note

## Summary

## Full Document
**思考一个问题：我们为何写读书笔记，究竟为了什么。**

就像我们之前提到的，输出并不是一定是写文章，而是做出更好的决策。同样，对于写读书笔记来说，重要的是在我们已有的知识体系中增加了新的知识块和连接，而非是写出了高赞的书评、或者复杂的脑图，甚至华丽的视觉笔记。

记笔记，是为了更好地思考，而不是更好地炫耀。

#### [#](https://help.flomoapp.com/thinking/reading.html#做笔记-是为了更好地理解) 做笔记，是为了更好地理解

![Untitled](https://imgproxy.readwise.io/?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/book01.png%21webp&hash=5d56f8e157919db572489b38b96d713f)

我们经常提到书中有知识，其实并不是。比如《概率论与数理统计》就对我来说，就只是信息，纵使里面有再多的金句划线，我也**不理解**其中的意义，所以也就无法转化为可以做出决策的知识。从这个角度来看，我们不应该关注读了多少本书、写了多少条笔记，而是应该关注：

**我从这本书中收获了哪些知识点，并且和哪些已有的知识点建立了新的连接。**

比如我之前在产品设计中非常不喜欢引入各种「物质激励」，所以在 flomo 中你很难看到签到打卡送空间什么的东西，但这仅仅是出于个人好恶。而在读《内在动机》这本书的时候，其中关于奖励的部分，很好地从理性角度解答了我的困惑，并且还给出了一些解决方案，比如其中提到的「深度连接」，就让我们更加坚定地在 flomo101 和共建者社群中投入精力。

![Untitled](https://imgproxy.readwise.io/?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/book02.png%21webp&hash=25bdcefdc79572d4f0f411f995fef1db)
许多连接并不一定要在产品内，更多的是在思维上。

#### [#](https://help.flomoapp.com/thinking/reading.html#做笔记的误区) 做笔记的误区

在没有弄明白为何要记读书笔记的时候，其实我们已经记了很多年读书笔记。这些行为总是不假思索的进行，但当你跳脱出来看得时候，会发现许多都是无用功。

![Untitled](https://imgproxy.readwise.io/?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/book03.png%21webp&hash=a6c26bc89dc8435318fd00f818b5808d)
* **大量的划线和摘录，却没有自己的思考。** 所谓的读书笔记，就是一本书上的金句集合，加上现在读书 App 中的热门划线引导，更加让这个过程毫无摩擦。
* **记录散乱，没有归于一处。** 有些是在纸质书上的划线，有些是在微信读书的标记，有些是在 Kindle 中的评论，这些笔记依附于书籍，却没有和自己的知识体系汇总在一起。想要再次回忆起，只有再读的时候，才能记起，可惜的是概率很低。（针对这个问题，文末有彩蛋）
* **追求各种结构化的表现形式。** 许多所谓的读书笔记，只是一个大纲脑图，密密麻麻的结构，看起来很有工作量，但其实只是把目录又复制了一遍。书中的结构依旧，并没有和自己的知识体系发生一点关系。
* **所有笔记只是用书名做分类。** 书名是一种最无效的分类方式，只能代表你阅读过这本书。一本书中往往会涉及到许多知识块，而这些知识块会分属于不同的领域，我们在记录的时候应当将其和我们已有的知识体系连接，才能更深入地理解和记忆。而只用书名归类，相当于我们买了家具，但不放在家里，而转手放到了一个仓库中归档。
* **不断记录，从不复盘。** 许多人迷恋读书的数量和笔记的数量，希望能在单位时间内最大化输入。但最终能被我们记住或理解的东西，才能为我们产生价值。如果只有输入，没有复盘，对于许多普通人来说，就像一个漏水的木桶，看似非常努力，实则一无所获。

这里并非是为了批判，而是一个提醒，让我们能停下来反思「读书笔记」四个字，对我们的意义是什么。

#### [#](https://help.flomoapp.com/thinking/reading.html#如何用-flomo-做读书笔记) 如何用 flomo 做读书笔记

假如我们要绘制一副场景，那么肯定不是上来就深入到细节过程中。相反应该从整体鸟瞰结构，然后再逐步深入细节。

![Untitled](https://imgproxy.readwise.io/?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/book04.png%21webp&hash=2a6fd8f0a26fd8d64fdf0c2eb558aa34)

![](https://pbs.twimg.com/profile_images/1240081455071424512/mnmLdrIs.jpg)

[slow D](https://twitter.com/slow_haru)
[@slow\_haru](https://twitter.com/slow_haru)

Painting process

![](https://pbs.twimg.com/media/DyiOXJBU8AAOSi3.jpg)![](https://pbs.twimg.com/media/DyiOXJCV4AAwA2Y.jpg)![](https://pbs.twimg.com/media/DyiOXJBUwAEcPqk.jpg)

[Posted Feb 4, 2019 at 4:31AM](https://twitter.com/slow_haru/status/1092279448794087425)

读一本书也一样。拿到一本新书，第一遍阅读的时候，不必非常仔细地阅读和记笔记，反而应该快速地略读，并对感兴趣或者不理解的地方进行一些标注 —— 因为这是让我们对整本书「鸟瞰」的过程。不必担心错过什么或者没读懂的接下来怎么办，换个角度来想：一本书里面有一个知识点能被记下来，就已经很值得了。

一如《卡片笔记写作法》中所言，在记读书笔记的时候，尽量避免划线摘录，**而是要用自己的话写出来，给自己看。**不必担心词句的华丽与否，不必担心是否有所遗漏，这个过程就像是照镜子一样，应该关注的是**自己大脑的对于内容的反射**，这才是你思考的精华，而不是照搬书中的内容。

如若你不是做学术研究，大可不必去不断地区分何谓「永久笔记」、何谓「闪念笔记」。就像你想做一道番茄炒蛋盖浇饭，你的目的是吃到符合自己口味的饭而已，至于是颠勺三下，还是如何起锅，米饭软硬，都应该以符合自己的预期为主，而非刻板照抄过程 —— 如人饮水，冷暖自知。

下图是我在读《超文本与超链接》这本书时候的笔记，其实这些文字横跨两三章，但是在我的知识体系里认为他们可以放在一起，并且由于当时刚看完《玄奘之路》的纪录片，就想到万维网和佛教之间的发展历史 —— 这就是自己写给自己的好处，许多连接不必在产品中体现，而印在了大脑中 —— 这才是真正的目的。

这里要注意的是，读书笔记的每张卡片内容，应当尽量精简，尽量避免复杂的排版（当然 flomo 也不支持 😁）或过多的层级，这样才能在回顾或复盘的时候快速回忆，并和别的知识点建立联系 —— 我们大脑中最小的单元是一个知识点，而不是一本书那么大的知识坨坨。

##### [#](https://help.flomoapp.com/thinking/reading.html#利用标签和批注来建立知识之间的关联) 利用标签和批注来建立知识之间的关联

flomo 的标签体系有两个特点，一个是可以在一条 memo 上添加多个，另一个是能建立多层级标签。

我的读书笔记中，往往会有至少两个标签：

* 一个是作为资源标记 `#Books/书名` ，这是为了方便后续按照书籍查找
* 一个是作为领域标记 `#Area/领域/子领域` ，这是为了和已有知识进行关联

当然如果有一些其他标签，我会不吝惜的都打上去，因为知识本身就是网状的，而非树状的，没必要做到 MECE（彼此独立，互无遗漏） —— 毕竟大多数人不会在家里用图书馆分类学放餐具。

相对于标签来说，批注功能更适合单独两个 MEMO 之间的连接。在记笔记的过程中，如果促进了其他的思路，或者回忆起过往的某个标签，可以利用批注（或者复制已有 MEMO 链接）来建立连接，这样既能保证 MEMO 的简洁，不至于写下太多不同主题的内容；又能让这些小知识点之间保有更紧密的联系。

为何要强调和已有知识关联呢？因为这是区别「记忆」和「理解」的一种手段：

* 记忆，意味着背诵某些事实的能力，理解水平较低，比如一字不差地背诵一首苏轼的古诗。
* 理解，意味着能用更多熟悉的例子说明这个意义的概念，理解水平较高，比如能阐述当时苏轼在何时何地因何而有此等感慨，其中有哪些明喻暗喻等。

如果我们只按照书籍分类来记录知识点，那么他就孤零零的漂浮在 flomo 中。而一旦和已有的知识领域进行了关联，说明你通过有意义的联系，将新老知识点串在了一起，那么你的知识网密度就又提高了一步。

关于领域的概念，在 I.A.R.P 标签管理办法中提过，在这里不赘述，有兴趣的可以点击查看。

读了记不住，或者记了总忘记，这是许多人诟病读书笔记无用的原因。但我们并非机器，遗忘是很正常的事情，这时候回顾就很重要，因为既可以加强你的记忆，也可以让你观察到自己观点的进化。

flomo 有两种回顾方式，一种是基于手机端桌面小组件的随机展示回顾，一种是基于微信的定时回顾。这恰好对应了两种不同的回顾类型。因为非虚构类的书，往往有两类：

* 一类是结构简单，但需要记忆的内容多，比如许多教科书，入门书都是类似。
* 另一类是内容需要我们理解，自己寻找答案，比如许多社会学的书籍。

对于前者来说，最重要的是高频的重复，就像我们记单词一样。这时候推荐使用桌面小组件，设置多一些的条目，每次打开手机都看一眼，增强印象。

而第二种需要思考的内容，适合使用微信回顾，给自己留好时间，让内容在指定的时间出现，然后利用批注功能，把当下的思考和过往的记录连接起来，让自己真正的理解。

**塑造我们的，不是我们读过多少书，而是在遗忘之后，剩余的那一小部分。**

我们读书，是为了获取知识；而知识不仅仅是记忆，还有理解。知识和理解是同一事物的不同组成部分，知识是大脑中表示概念的集合，而理解是他们之间形成的联系。知识和理解，是相互关联相互依存的。

所以在做读书笔记的时候，不要在意排版，不要着急输出，也不必担心遗漏 ——

**因为重要的不是记录，而是更好地思考。**
