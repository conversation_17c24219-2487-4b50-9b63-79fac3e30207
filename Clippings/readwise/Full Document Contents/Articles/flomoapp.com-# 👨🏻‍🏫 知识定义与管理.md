---
人员: 
  - "[[flomoapp.com]]"
tags:
  - articles
日期: None
时间: None
链接: https://help.flomoapp.com/thinking/knowledge.html
附件: https://help.flomoapp.com/favicon.ico)
---
## Document Note

## Summary

## Full Document
知识是用于生产的信息（有意义的信息） —— 1998年，世界银行《1998年世界发展报告－知识促进发展》

**知识是人类特有的资源。书上没有知识，书上只有信息；知识是在特殊的工作和行动中运用信息的能力**。技术是在工作中运用自然科学，是知识的一种。 —— 1964年，德鲁克《成果管理》

#### [#](https://help.flomoapp.com/thinking/knowledge.html#知识的特点-难以被管理) 知识的特点（难以被管理）

1. 代表某种权力与优势，人们并不是随时愿意共享所有的知识与经验，需要设计机制来启发
2. 具有流动性，组织内部人员会流动，个人则会遗忘
3. 碎片化，比较散乱、经常遗漏并且需要不停更新
4. 无法快速评估其价值以及利益回报

#### [#](https://help.flomoapp.com/thinking/knowledge.html#什么是知识管理) 什么是知识管理

知识管理的经典单行定义是由 Tom Davenport 在早期提出的(Davenport，1994) : **「知识管理是获取、分配和有效利用知识的过程。」**

一套能将获取到的信息转化为知识的系统。能持续地、普遍地收集「显性与隐性知识」，进一步转化成「企业文化」或「个人品牌」，让人们更能注重在「信息的交换」，透过互动的信息，「再创造」知识的价值。

重要的是：

* 
* 
* 

彼得 · 德鲁克在 1965 年预言：知识将取代土地、劳动、资本与机器设备，成为最重要的生产因素。他认为，21 世纪的组织，**最有价值的资产是组织内的知识工作者和他们的生产力。**

在信息时代里，知识已成为最主要的财富来源，而知识工作者就是最有生命力的资产，组织和个人的最重要任务就是对知识进行管理。**知识管理将使组织和个人具有更强的竞争实力，并做出更好地决策。**

目前**管理科学关注的是企业和管理者层面的知识工作，而不是个人层面的知识工作。** 当经济产出转移到知识型工作时，这个领域的焦点仍然是管理者和公司，因为个人工作者从来不是那么重要。这可能是为什么知识工作的核心实践经常是临时的，因为我们不知道如何衡量知识工作者的生产力。

但随着基础设施的完备（API 化的世界），个人创作者经济的兴起。我们在一个组织中的时间越来越短，但我们的寿命却越来越长，我们大多数人都需要知识管理（即对知识进行投资，以积累知识的复利）。

1. 定期投资 —— 养成坚持学习的习惯
2. 长期投资，多元化是关键 —— 专注眼下的事情，但也要注意更广泛的范围
3. 管理风险 —— 不要把「鸡蛋都放在一个篮子里」，也要避免低风险低回报
4. 设法低买高卖，获取最大利润 —— 学习新兴技术或者适用面逐渐广的技术
5. 周期性的评估和平衡 —— 定期评估哪些是值得学习和关注的

德鲁克《知识社会》一书中，他将知识分为：

* 综合性知识：即我们传统而言的智慧，主要在于培养有教养、道德的人，目的是为了自知
* 专属知识：即行动中可以证实对行动结果有效的信息，主要在于工作研究、工作分析和工作管理上

举个简单的例子，如果你被迫流落荒岛，大概率希望和贝爷一起生存，而不是和苏格拉底或者孔子，因为后者虽然有道德和修养，但是前者关于生存的专属知识能解决我们吃饭和睡觉的具体问题。正是由于这种视角的转化，才构建了工业革命之后的新型社会，赋予了我们知识和专家的权利。

##### [#](https://help.flomoapp.com/thinking/knowledge.html#掌握根据知识的性质划分的四种类型的概念) **掌握根据知识的性质划分的四种类型的概念**

* 事实知识：知道是什么的知识，是关于历史事实、经验总结、统计数据的知识。Know what
* 原理知识：知道为什么的知识，是关于事物的原理和客观规律方面的知识，通常属于科学范畴。Know—why
* 技能知识：知道怎么做的知识，是关于技艺、技巧、诀窍和能力方面的知识，属于技术范畴。Know—how
* 人力知识：知道是谁的知识，是关于谁知道什么以及谁知道如何做什么的知识，包含了某种特定的社会关系以及社会分工、知道者的特长与水平，属于经验和判断的范畴。Know who

* 显性知识(Explicit Knowledge)：也称编码知识，指可以用语言、文字、数字等方式进行表达从而易于沟通和共享的知识。如以文件、手册、报告、地图等方式呈现的知识。Know-how/know-who
* 隐性知识(Tacit Knowledge)：也称未编码知识，指难以表达、高度个性化、难以沟通和共享的知识。如经营者或员工的经验、技术诀窍、直觉等。Know-what/know-why
	+ 技能类隐性：非正式的、难以表达的技能、技巧、经验和诀窍等；
	+ 认知类隐性：洞察力、直觉、感悟、价值观、心智模式、团队的默契和组织文化等。
