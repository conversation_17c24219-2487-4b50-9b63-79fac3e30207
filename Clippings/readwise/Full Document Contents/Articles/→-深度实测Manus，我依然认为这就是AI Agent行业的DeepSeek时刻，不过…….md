---
人员: 
  - "[[→]]"
tags:
  - articles
日期: 2025-03-05
时间: None
链接: https://mp.weixin.qq.com/s/wza4Oyp9AhX-cMj--NscBA?search_click_id=1935027558829713737-1741305281672-3084826261
附件: https://mmbiz.qpic.cn/mmbiz_jpg/7smcUtbXojvZHV8Lw7vx9u3fNbpJkV2SWF0koibDfG7DV9Va9ewmgAWsicfrDialHlVOLpLLJNVbicozblF7sIXlZA/0?wx_fmt=jpeg)
---
## Document Note

## Summary

给AI配台电脑，我要出去玩了。

## Full Document
![Image](https://mmbiz.qpic.cn/mmbiz_gif/7smcUtbXojvFT9Hga8ONibpN5W1IV6ibpDZqCTZQpnmw8S0XlDRXYngyF5tQILNXW9CsPw9UQibdEyKRdcqE7KcVw/640?wx_fmt=gif)
文 ｜ 阑夕

Manus刷屏一天，从开始的一夜成名，到中间的一码难求，再到质疑它的宣发一掷千金，整个过程里，FOMO情绪和直觉警惕交缠不休，是很有意思的传播学样本。

其实AI行业这几年来一直都是「炸裂驱动型」的资讯模式，了解的都已经祛魅了，不了解的却还会少见多怪，**但是有一说一，天天这么炸裂下来，客观上也会存在真的炸裂蒙混其中。**

而我对Manus的评价就是，它确实属于真·炸裂的那一桌，称得上AI Agent行业的DeepSeek时刻，不过有个补丁，结尾时我再叠上。

先看Manus的一个演示效果：

让它开发一款文字互动游戏，可以扮演谷歌公司的CEO，通过体验公司历史上的重要决策，既能获得游戏的乐趣，也可以顺便了解公司的文化。

![Image](https://mmbiz.qpic.cn/mmbiz_gif/7smcUtbXojvZHV8Lw7vx9u3fNbpJkV2ShZZxcgbUJkM0KicibPu6LeHrwvYFwicqDTmaHsia0H5ibVrH4HJgHibFficbg/640?wx_fmt=gif&from=appmsg)
用了差不多一个小时，Manus把谷歌CEO模拟器的网页游戏开发好了，完成度很高，点击开始游戏，还会让你自选难度，接着就会面对谷歌发展史上的每一次转变节点，你的选择会决定公司资源的变化，并影响最终的游戏结局。

在一个小时里，用一句话，做一个游戏出来，这就是AI Agent的能力。

它和传统的对话式AI不同，不再只是提供信息层的答案，而是能够操作电脑完成更加具体的工作任务，包括但不限于写程序、做网页、整报告、筛简历等等等等，它能够完全自主的解决过程中遇到的各种困难，并交付工作结果，当然也有例外，这个例外我们后面再说。

目前主流的AI Agent服务不多，而且普遍很贵，比如ChatGPT Operator需要200美元一个月Pro会员才能使用，还有主打编程市场的AI工程师产品Devin，每个月的费用更是要500美元。

Manus的开发商是中国大模型团队Monica，目前是免费测试阶段，单任务成本压缩到了2美元，是OpenAI的1/10，同时在基准测试的排行榜上已经超过OpenAI拿下了全球最强。

我在拿了邀请码后，已经在几个小时之内耗尽了Manus的单日计算资源，确实非常兴奋，效果也非常震撼。

展示几个实测案例吧：

首先我让它帮我做一张linktree风格的个人主页，Manus把这个任务拆成了8个步骤，先在全网搜集我的资料信息，包括我在各个平台的链接以及代表作，然后基于linktree的设计风格开始编写网页代码，半个小时之后，它交付了这么一个作品给我。

![Image](https://mmbiz.qpic.cn/mmbiz_gif/7smcUtbXojvZHV8Lw7vx9u3fNbpJkV2SHGGglJ9dBpE61yjNr4msshoYpWKibYlAITOyzAosMJp7ZibFM6lB50bA/640?wx_fmt=gif&from=appmsg)
简单，但是完美符合要求，交互也都没问题，写轮眼级的复制效果，如果想做得更美观，还可以继续写提示词让它修改。

第二个测试，是我用Manus帮一个工程师群友解决实际问题，他在工厂里负责维护的阿特拉斯机械臂出了点小问题，找售后的话费用要花几千块钱，不如自己想办法找补，他又懒得看文档，于是直接给了我一段话，让Manus看看怎么处理。

![Image](https://mmbiz.qpic.cn/mmbiz_png/7smcUtbXojvZHV8Lw7vx9u3fNbpJkV2St00vNBIZvLuL3yrcVGb3HkLGxibKPMbeJAvvaxJKS7oC7uGZhANSJCw/640?wx_fmt=png&from=appmsg)
注意啊，这个需求理论上普通的对话式AI也能接住，但会需要更多的交互流程，比如你得把文档喂给它，一步一步的得到答案，但是Manus不需要这些，它会自己去阿特拉斯官网下载文档，读完之后找到解决问题所需的关键内容，仔细分析，创建程序，最后的代码我发给了朋友，有点小瑕疵但手工修改之后完全可用，直接省掉了一次售后呼叫的次数。

第三个测试，是我的微博读者提议，让Manus去做一个国家的极简编年史，我增加了漫画表选和网页设计的要求，最后交付的作品配色有点难绷——AI没有审美，这点必须反复强调——但是这时Manus的服务器已经宕机了，暂时没法修改，所以我也就把半成品展示出来吧。

![Image](https://mmbiz.qpic.cn/mmbiz_gif/7smcUtbXojvZHV8Lw7vx9u3fNbpJkV2SIaJILUpZbyb3vV7c5arU9HhZzt0YqaSWicsib1pueeibHg1ntwKApPGqg/640?wx_fmt=gif&from=appmsg)
可以看到，Manus将英国的历史分成了10个不同的时代，并基于时代风貌绘制了SVG图片，最后呈现在HTML的网页端，可以说是人机协同的样板间了，无论是作为课外教案还是作品预览，都有极其便捷的上手门槛。

最后一个案例，是我让Manus做一款消消乐游戏，但是图标得用原神的角色，它先是开始研究消消乐的游戏机制和实现方法，接着试图搜集原神的图片素材，这个时候就出现例外了，它第一次发出了接管请求，原因也很让人无语，它的运行逻辑被一个网盘给堵住了，没法注册账号，所以下载不了资源，想让我帮它去下载。

看来再强大的AI，也会被网盘的会员拦在门外。

本着尽可能让AI Agent独立完成工作的原则，我没有这么做，而是稍微改了一下需求，让Manus改用科技公司的logo来做游戏图标，因为开放版权的SVG素材全网都是，所以这下Manus跑起来就没什么问题了，很快就做完了一个带积分的消消乐游戏，玩起来也算顺畅。

![Image](https://mmbiz.qpic.cn/mmbiz_gif/7smcUtbXojvZHV8Lw7vx9u3fNbpJkV2SXHlB2Rj370TsDMU3XmAussEFLe7ENQibREMZpPzcL9ic98yLAuONTozA/640?wx_fmt=gif&from=appmsg)
不过也能看到，在解决这类相对复杂的问题时，Manus在细节方面的缺失还是有的，这也和人类（我）参与过少有关，比如对屏幕的适配问题，需要给它更多的说明，Manus的修改响应也不慢，但因为同样遇到了服务器宕机的麻烦，这个任务暂时没有继续精进下去。

我觉得这几个实测例子已经可以非常清晰的表明，AI Agent在现阶段的能力和不足，Manus已经不是那种只能操作浏览器的产品了，它本身具有沙盒环境，能在完成工作之前自行进行测试，验收合格再做交付，但也限于互联网的数据边界，如果网络上的资源不够，它是没有办法生产资源自给自足的。

我还做了一些偏文书类的测试，也可以用来对比AI Agent的特点：

比如我让Manus根据B站最热门的10个星见雅（游戏角色）视频，给出她的操作技巧。

![Image](https://mmbiz.qpic.cn/mmbiz_png/7smcUtbXojvZHV8Lw7vx9u3fNbpJkV2St4N22dnPnsg3Q175PFV3FSo4DKoFpPj5RlibR37pWjvRAeicJZS1fybw/640?wx_fmt=png&from=appmsg)
Manus是真的足足看完了10个视频——花了一个多小时的时间——再去把各个UP主的小作文精炼成了我要的材料，而且相当准确，同样的任务如果交给联网的大模型去做，虽然也能完成，但幻觉的产生概率很高，在「老实」程度上不及AI Agent靠谱。

再如让Manus去研究PolyMarket的套利可能，虽然我确实有那么一丝期待，想得到一个稳赚不赔的投资指南——别笑——Manus倒是兢兢业业的做足了功课，列出了四个套利机会，让我只要在PolyMarket看到符合条件的项目出现，就能无脑按规则下注。

![Image](https://mmbiz.qpic.cn/mmbiz_png/7smcUtbXojvZHV8Lw7vx9u3fNbpJkV2SDD0T3JxdicZOzYdgJoY5PZUYg07J6YOXnhGiaYxJGFqBjMfWNwrkH3Jg/640?wx_fmt=png&from=appmsg)
从回放来看，Manus每次都是从最基础的信息开始切入，先了解PolyMarket是什么，再分析预测市场的游戏玩法，接着结合平台规则构建风险策略，标准的实习生作风，任劳任怨，踏实耐用。

对了，回放这个设计，在我看来也是Manus的亮点之一，它有点像推理模型暴露思维链的选择，很多时候，AI的思考过程要比答案供给更能给人启发，Manus的每一个任务都有回放功能，且可被分享出去，它在解决问题的途中所展现出来的手段，完全称得上是另一种形式的智能资产，可以扮演人类的老师。

**所以话说回来，我评价Manus是AI Agent行业的DeepSeek时刻，这里需要打一个补丁，是DeepSeek-V2时刻**，2024年5月，DeepSeek开源V2版本的模型，这是它第一次出圈，因为价格非常便宜，但是因为模型本身的能力一般，所以当时很多人只是觉得DeepSeek要来打价格战了，感到意外但不重视，热度也没有持续太久。

直到DeepSeek-V3和R1的连续发布，大家这才发现事情完全不一样，一夜之间整个大模型市场的成本逻辑都被颠覆了。

> 最初，没有人在意这场灾难，这不过是一场山火，一次旱灾，一个物种的灭绝，一座城市的消失，直到这场灾难和每个人息息相关。——「流浪地球」

我的意思是，AI技术的发展是连续性的，而在这条跌宕起伏的曲线上，每一次的信号强度都决定了后面的突破深度，就像DeepSeek没有V2就不会有V3，更不会有R1，我对Manus的看法没有变化，在把AI Agent服务从专业场景带向通用场景的历史转折点，它就是开山立派的创始品牌。

从用例来看，作为AI Agent的功能性非常强大，对于拆解任务的熟练度很高，CoA（代理链）的观测感觉和看CoT（思维链）很像，能「看到」AI在多个方案里评估并寻求最优解。

理论上应该是内置了海量的CoA来做承接，就和DeepSeek这类推理模型也是提前消化了足够丰富的CoT之后才会推向大众市场，尽可能的覆盖到了主流需求，从官网的Use Case就能看到。

有什么问题可以在评论区留言，或者说出你们想让Manus完成哪些任务，我可以帮着测试。

![Image](https://mmbiz.qpic.cn/mmbiz_gif/7smcUtbXojvFT9Hga8ONibpN5W1IV6ibpDsWpYKSJu5RQIeVAO5CAlyZK9S8jDgmVsQnnRtkapnrc1ruwKia1SqwA/640?wx_fmt=gif)
