---
人员: 
  - "[[假香蕉人的创业回忆录]]"
tags:
  - articles
日期: 2012-11-10
时间: None
链接: https://fakebanana2023.wordpress.com/page/3/
附件: https://s0.wp.com/i/blank.jpg)
---
## Document Note

## Summary

## Full Document
* 10年Q4我已经在那家香港贸易公司呆了将近4个月，当初有一个机会参加SAP的产品推荐会，虽然那时候公司并没有代理SAP的产品，但因为我在美国时有SAP的经验，所以一直很好奇他们在国内的发展。产品介绍会结束后，我跑到SAP展台问了德国老外一个问题：“SAP通常为欧美成熟类企业提供标准化的内部流程管理系统，但中国属于发展中国家，很多企业虽然规模大但内部管理极其混乱（当时我用的词其实是灵敏度高，算是一件事儿的正反面吧），SAP如何看待这个问题？”。这里我就不提老外的回复了，因为重点不是他，而是在旁边观察的一个人，我的下一任老板，SAP的产品推荐会就是他的会展公司所举办。还记得他跟我讲的第一句话：“你是哪家公司的？” 那时候谁能想到，他将成为我回国后的第一任导师，重要人物起个名儿：叶老板（因为他在KTV最喜欢唱叶启田的《爱拼才会赢》）。叶老板还没等我回答他的问题，就递了张名片，然后说：“你英文不错而且能问出这种问题应该有技术背景，有空来我们公司坐坐。”当时也没太当回事儿，因为会展公司跟IT离的太远，根本想不出为什么叶老板要找我。但我也没放过那个机会，几天后我便上门拜访。现在都还记得第一次去他公司，我到的早在他办公室等他，叶老板进门后跟我快速打了个招呼，就忙着点燃了两根香对着关公像拜了拜，然后问我：你读过《易经》吗？我个米国人，连《易经》是啥都不知道。结果哥们跟我讲了几分钟的《易经》，我tm一句没听懂。讲着讲着，叶老板突然切换频率：“你来我公司吧，我有个项目很适合你。” 他说自己除了会展公司之外，还有一个初创型科技公司，两个公司北上两地200来号人，他也正好在给科技公司物色一个项目经理。而且他自己也是美国毕业的海归，90年代在一家国际IT公司做大中华区老大，在国内做了10多年的生意。我听后感觉机会不错，主要是叶老板在国内有实战经验，可以从他身上学到很多东西。我还没来得及回复，他继续说：“回国没多久吧？晚上带你认识几个盆友。” 说完就去开会了，让秘书发给我晚上的地址。那时候我一脸的懵逼，15分钟。。。这tm是个面试？后面才知道其实晚上那场才是真正的面试。现在往回看，叶老板的魅力很大一部分来自于他的聊天方式，看似很跳跃没条理，但其实节奏把握的很好。年轻时我认为聊生意就是一口气把所有需要决策的点都探讨完，但有阅历后我越来越发现聊生意就是聊感情，和调情很像：挑逗几句，碰一碰她的胳膊，说个笑话，再拉一拉她的手，一步一步走进你的目标，很轻松对方也没压力。这种聊天方式后期我也慢慢学会：聊聊八卦，再说一说提案要点，给客户一个机会吐槽他老板，再聊一聊返点，不慌不忙就把生意给做了。

 继续说叶老板的故事，那天晚上的地点是金钱豹，一个台湾餐饮集团开的连锁，11年那会儿在北上很火很时尚。和叶老板一起的几位兄弟都是台湾人，同时也都很有特点，挨个起个名。第一位陈Sir，会展公司总经理，50来岁，嘴里总是嚼着槟榔，当时还玩儿我叫我尝尝，那味儿也是够恶心的。后面同事跟我八卦他的故事，这位老哥活的无比潇洒，在台湾有老婆孩子，有一次去云南旅游认识了个当地的姑娘，然后就辞职3年在云南和那姑娘一起生活。估计后面没钱了，就再回北京在叶老板的公司上班，貌似年轻时是个警察，所以大家都叫他陈Sir。第二位科技公司总经理老杨，在软件开发领域有几十年经验的老技术人，先不多说老杨的故事，后面我才知道叶老板找我就是来对付他的。第三位销售老大蔡总，那会儿他也刚去叶老板公司，之前在台湾做生意失败，欠了一屁股的债，老婆孩子离开了他，然后独自一人跑到北京赚钱。最后一位Paul，和我岁数和背景最接近，30来岁，美国MIS专业，和我一样十来岁去的美国，05年左右回国在叶老板公司工作了一年，之后去了一家大型4A广告公司做业务负责人，成为了叶老板的客户。人都到齐后，金钱豹北京总经理还特意跑到我们桌和叶老板打招呼，一口的闽南话。感觉那会儿在北京的台湾帮很小很紧密，互相都认识，而且生意上也都相互照顾。用餐时，叶老板透露了他对公司整体业务的展望，那时候他的客户横跨2/3个行业，而且都是国际大品牌，一个展会下来几百万，一年做十几个，公司流水应该在5000万-1个亿之间。之所以叶老板要做一家科技公司，是因为当时办会展的时候有很多相关的技术需求，比如活动签到系统，CRM系统，活动网站。。。等等。一开始叶老板把这些业务外包，但后来发现这些项目也可以赚很多钱，所以找了老杨组了个40来人的技术团队。一段时间后，他发现大部分的客户（市场营销部）关注数据都非常片面。用他话来说就是比较虚的东西：比如多少人去了展会，多少人来看了网站，多少人给企业提供了sales lead，没有一个人提到底给企业赚了多少钱。而他认为营销的唯一目的就是为了销售，而当时没有一套系统能把销售链每个环节的数据都打通，最后对不同推广渠道进行ROI分析。所以他想做一套系统，一方面能更好的辅助公司现有业务，另一方面成型后想把它做成SaaS模式卖给市场营销部。当时听完叶老板的Idea后，我隐隐约约觉得这是一件很大的工程，但并没有意识到在10年那会儿他的想法有多么的前沿。做过网络营销的小伙伴也许能听懂他说的就是Performance Marketing（效果营销）的概念，现在不管是抖音，天猫还是微信，玩儿的都是这概念，在一个生态圈（如腾讯系）内打通数据，做到从流量端到销售端每个环节的分析。叶老板说完，公司的两位高管也开始发言。陈Sir比较容易兴奋，上来就说要是有这么个东西估计能卖很多钱。老杨立马泼冷水说这个系统的复杂程度很高，而且不同的数据源结构不一样，很难做到统一。叶老板问我的想法，在咨询公司时我接触过一些网络分析工具，如Google Analytics，所以我按照谷歌的逻辑简单陈述了目前能做到的和不能做到的功能。并且告诉叶老板其实有一个开源版的工具可以拿来改版，不用从0开始。叶老板听完很满意，说这就是你最重要的一个命题。现在回想，这段谈话才是真正的面试，虽然老杨说的也有道理，但如果我同意了老杨的说法，估计叶老板不会用我，因为他需要的是一个能帮他实现愿景的人，而不是一个天天对他说No的人。这点我再展开说一下，因为IT行业里很多人都有这臭毛病，客户/老板一提需求，IT人员就各种这个也不行那个也不行，以为这样表现得很专业，但这种行为等于在自掘坟墓。我在美国做咨询时对接过很多程序员，华人程序员更是有上面提到的问题，但印度程序员很不一样，基本上就是提什么需求他们都说Ok。感觉他们的做法就是不管能不能做到，接了再说，真做不到再和你商量其他方案。我认为这也是为什么美国科技公司大量的一把手和高管都是印度人，因为他们知道如何管理客户的期望，而不是做一个专业的Naysayer。

 吃完饭，叶老板叫我和Paul一起上他的车去“娱乐”一下。\*\*\*想看小黄文的请注意，要开始了\*\*\* 到达地点后，我发现这是个我熟悉的地方，就在团结湖盈科中心旁边的凯富酒店，大门口牌子写着：“Comfort Suit & Inn”。当初我心想这不是个美国的连锁酒店吗？来这儿干嘛？难道叶老板直接给安排了个女的？一上来就这么刺激吗？虽然心里打鼓但也有点小兴奋，毕竟在美国那么多年，对国内娱乐场所的认知还停留在成龙和Chris Tucker《Rush Hour 2》里选妃的那一幕。结果一进门，两排穿旗袍个子高挑的妹子鞠躬：”欢迎光临“，接着一位穿职业装的妈咪走过来：叶老板来啦，今天要给您预留多少多少号吗？然后就把我们带进了KTV包间。话说当时凯富酒店应该算档次比较高的KTV，而且低层是KTV，高层是客房，旁边就有个温泉俱乐部，简直是男人天堂。当时做这生意的老板头脑真好，应该赚翻了，不知道扫黄那会儿进去没。现在凯富酒店的KTV应该还在，至少我20年去北京出差时还在运营，但估计已经转”素“了。我记得是好像是12年年初那会儿，先是天上人间暂停运营，一开始人都说是新公安局局长扫黄，新上任三把火，过几个月那些场所就又会陆陆续续开门，估计之前几十年都是这种操作。但12年那次行动不同，因为不久后京城（至少三环内）所有的娱乐场所便全体阵亡。继续讲故事，进入包间后妈咪就叫了一排姑娘进来让她们报名字和家乡，在北上这种场所基本上不会有本地姑娘，都是什么东北，福蓝，湖建，鹤南滴。我和Paul都选了姑娘后，一个个子很高很漂亮的姑娘进来坐在了叶老板旁边，她应该是那里的金牌妹子，因为每个姑娘身上胸牌都不一样，越亮越贵，而她的胸牌tmd闪瞎眼。姑娘们入座后开始K歌环节，我那会儿对唱歌没兴趣，中文歌也不熟，所以只能看英文歌单，而且我在美国听金属根本不碰流行音乐，所以当时勉强选了首I Believe I Can Fly，那种场合唱这首歌也是挺逗的，脑子里全是《Space Jam》里那些动画人物。叶老板来了首《爱bia才会nia》，这首歌我现在还时不时的会找回来听，仿佛它能瞬间把我带回那个疯狂的时代。噢噢，回归重点讲姑娘，其实大部分的细节我已经都忘了，只记得20来岁饥渴的很，基本上手进去她衣服就一晚上再没拿出来。唱完歌开始玩儿骰子和猜拳，叶老板很喜欢这环节，我完全不感兴趣，至今不会玩儿。也许我目的性太强，总觉得这种建立感情的前戏很浪费时间，又不是要娶回家，最多一晚上，有些小姐也不出台（带走），所以大部分时间我tm连话都不想说。叶老板貌似也看出来了这点，玩儿一阵子后过来跟我说：“转移战场吧？”

 下个目的地，三元桥附近的一个桑拿，场地很大，一层洗浴，二层按摩屋。一进门小哥就上来：哥，今天做什么服务啊？叶老板回了句两位全套，然后与我和Paul道别，说结账时报他的名字，估计他那个年纪对纯粹的性已经没感觉，所以我从未见过他去桑拿。我和Paul在楼下澡堂泡了会儿，这段谈话我现在依然得清清楚楚。Paul跟我的经历很相似，也是1.5代移民，他提到在美国时一直找不到归属感。ABC不一样，因为他们一出生就被灌输是美国人，而且许多早期的一代移民为了孩子能更好融入，刻意弱化了本国文化。留学生的那一波人，也很清楚的知道自己就是华人，美国属于异国他乡。而我们这种1.5代属于中中间间最迷茫的一波，在文化认同还没完全固化时被丢到世界的另一边，今天是中国人，明天突然就要当美国人了。这种突变对于一个十几来岁的孩子非常残酷，两种不同的文化很难在同一个人身上并存，所以大部分的1.5代都拼了命的往一个极端靠，要么彻底放弃原生文化，做个香蕉人（大部分的1.5代正是如此），要么否定和妖魔化自己在美国长大的经历，回国寻根并试图找回自己失去的那部分文化认同。Paul就属于少数的这个极端，他认为自己骨子里是个华人，当时他说的一句话我依然印象很深：“Asian People Should Live in Asia”，回国后没有肤色的干扰他觉得一切都正常了，没有人会对他说你是与众不同的。也许对于我和Paul这种1.5代来说，做一个普通人才是最大的奢侈。我对他的感受很有共鸣，因为我自己长大过程中也有很长一段时间不接受自己是华人。经过一段时间的自我探索后，我才学会了不走极端，并且接受自己的多元文化经历。当时为了纪念这种觉悟我还在身上印了个鹰和龙的纹身。这点也恰恰成为了我在国内做生意的优势，需要和老外打交道时做只鹰，和中国人打交道时做条龙（比谷爱凌都tm灵活）。严肃的东西有点多，赶紧拉回来继续讲妹子，我和Paul聊完就上楼了，还记得当时专门有个哥们儿就站在楼梯旁。他唯一工作就是当有人上楼时喊：“楼上贵宾一位”，这样楼上的哥们就知道要“上菜”了。我俩各进各屋，过了会儿一个穿着性感内衣的妹子就进来，如果你不满意可以说换一个，或者直接点号，这种场所的服务基本上都是莞式，不懂的兄弟网上查去我就不描述了。相对来说，北京的桑拿最简陋，上海有隔着玻璃的选妃环节，而且还有韩国和俄罗斯妹子。总而言之，现在北上的场所几乎全军覆没，广深我不清楚，但7/8年前东莞被端后估计也整治过，这年头貌似只能去小地方找乐子了。\*\*\*小黄文结束\*\*\*（自己读了遍，发现这小黄文真不合格，md大半段都是严肃的话题）

 正式入职叶老板公司后，第一个周末便参加了新员工培训，叶老板亲自上阵来了段激动人心的演讲。大部分的内容我已经忘了，只记得开头第一句：你想做一只狼还是羊？后面就一股《华尔街之狼》的风范，整体表达的意思就是：社会中的羊都是穷B和Loser，因为他们没野心，懒惰，活该，赖不了别人，只有选择做一只狼才能改变命运。培训完，晚上所有新员工和公司高管喝酒，高管坐一桌，新员工需要围着桌子敬酒走一圈，自选每次敬酒是一整杯啤酒还是一小杯白酒，那次直接喝断片儿。进公司后，做的第一个项目就是Paul给叶老板的生意，估计这也是为什么一开始他把我俩撮合在一块儿，希望我和他能快速建立友谊。当初我的职责和在咨询公司很像，我负责对接Paul收集需求，然后写Use Case给老杨团队的程序员。做了一个月后，我便发现了个问题：老杨的背景是企业级软件开发，他非常注重代码架构，对他的程序员要求也很高，每个项目都是从底层开始写（用的C#），也不用任何CMS和现成的开源代码。但Paul的项目大部分都是官网/活动网站这种，并不需要搞得那么复杂的。因此每次Paul都跟我吐槽老杨的开发时间太长。我夹在中间也很难受，毕竟技术团队不是自己的，我也管不了他们的研发周期。有一次Paul火了跑到公司破口大骂，叶老板给了我个眼神：一副你的项目你自己解决的意思。为了让Paul消消气，我拉着他去喝酒，然后给他看了我的个人网站，同时与他说其实他的项目多半用Wordpress和PHP就够了，哪儿还需要上C#啊？他也同意这种更敏捷的方法，所以他给叶老板提议，从老杨那边切出一个小团队专门做这种轻量级的项目，而这个小团队的负责人自然就是我。有了Paul的支持，叶老板给了我4个Headcount（人头数），我规划为：1个HTML/CSS，1个SQL，2个PHP。后端重新外聘，前端从老杨团队挖墙脚，数据库我招了个在工体喝酒认识的ABC。这哥们是我回国后唯一的知心好友，同样是个重要人物，起个名叫Jack。Jack美国中西部大学CS毕业，回国前在美国一家通信公司工作，同时还搞了个副业在网上倒卖Dell的旧电脑。回国后原本打算上清华的硕士，我认识他后就跟他灌输读书在国内没用，还不如出来混多一些实战经验。有了招聘权我就开始忽悠他一起来叶老板公司，一开始他比较挺犹豫，结果我说老板还会带去KTV，这哥们眼睛一下就亮了，只能说20来岁的男性都tmd这德行。团队组建完毕后，我便开始用PHP做Paul的项目，项目时间缩短了很多，也没出现什么重大的Bug。那段时间我，Jack，Paul几乎每天都混在一起，我们仨的关系也走得越来越近。现在回想起来，很怀念那段友谊。Paul和我依然保持着联系，后续也有在一起做生意，但Jack。。。他的故事很悲惨。

 成功开启一个新模式后，老杨看我越来越不顺眼，感觉自己就像《征服》里的刘华强，一个年轻气盛的小混混挑战老江湖宋老虎。内部开会时老杨总会阴阳怪气的说些看似不是针对我的风凉话，但其实大家都心知肚明。那段时间我和他属于冷战阶段，但接下来的一件事儿就是我的卢沟桥事变。有一天蔡总（销售负责人）私下约我吃饭，我不清楚他为什么要找我，因为他的项目一直归属老杨团队，所以与我没啥交集。他跟我说他在做一个台湾婚纱品牌的客户，预算有限，而老杨给他报的工时太高，成本控不住，导致他整体的P&L（ 盈亏比 ）下降。然后蔡总就跟我商量是否把这项目划过来让我的团队负责？我听到能扩大团队的影响力，当然愿意而且根本没想太多，直接就同意了。这件事儿发生后，老杨就开始使坏，每次开项目进度会时，只要出现一点点问题，他就会无限放大问题带来的负面影响，而且还提出一些其他解决方案。搞得我也没办法，只能会议上答应尽快解决，然后把压力转移给团队。那时候长陪着团队加班到凌晨，所以在团队管理上也成长了很多。4个人的团队，前端和其中一个后端都很给力，虽然之前对开源CMS没太多经验，他们也一直在自学，毫无怨言。特别是那个前端，当时国内的浏览器五花八门，不像现在基本上都统一用Chrome或者Edge。而且国内还有一个历史问题，盗版的Windows只能用IE6，所以当时那个前端每次都得多浏览器测试兼容性，一个好了另外一个又不行了，有时候后端一点问题没有，前端tmd调一周。这前端哥们儿后面去了新浪微博，早期员工还发股票，兑现后能赚大几百万，算是混得很不错的。另外一个后端被我开了，因为态度问题，不愿意学习而且频繁对我说No。他是我回国后第一个开掉的人，那时候小公司的IT权限管理很差，服务器密码基本上直接贴桌上，所以开人都是没有任何通知，不然怕哥们儿临走删库。还记得把他开掉的那天，中午吃饭时还说说笑笑，但其实我已经决定了他的命运。吃完饭哥们儿还收了个快递，是他新买的HTC手机，估计花了半个月的工资。然后哥们儿就被HR叫去谈话，回到座位时哥们儿发了几秒呆，然后喊了声：操！看都没看我就拎包走人了。那段时间Jack也成长的飞快，他的性格比我还内向，而且说话比较腼腆，再加上是个ABC中文不流利，所以做汇报时老是结结巴巴。当初我希望他能从研发中跳出来多接触商务端，所以就让他帮我分担了一部分对接Paul的工作。毕竟Paul也能讲英文而且互相熟悉，对接起来很自然。别外，那段时间我和Jack也一起开发了一个新的社交圈：Toastmasters 演讲俱乐部，这是个国际非盈利组织，在很多地方都有，目的就是让大家练习公开演讲。我们去的俱乐部中英文双语，我和Jack的主要目的就是练习中文演讲，让我们能更有信心的在客户和众人面前用中文提案。当然那时候也有其他目的，就是认识一些高质量的妹子，当初我和Jack混了很长一段时间的酒吧，觉得那里的人层次不齐，遇到了不少拜金女和文化层级很低的女性。这种演讲俱乐部里大部分都是年轻的职业人士，而且大家都有自我提升的意识，自然就筛选掉了一批人。这个社交圈对我和Jack的影响很深，我们俩都在这里遇到了自己人生的另一半。

 继续讲蔡总的项目，陆续做了两个月差不多完工，结果上线的前一天发现数据库有个严重的Stored Procedure Bug。不知道为啥在每晚自动同步会员数据流程中要卡一个小时，导致网站无法加载和数据错乱。当时这个问题我和Jack绞尽脑汁也解决不了，只能硬着头皮上线，晚上到了同步时间爬起来手动搞。当时那个Bug只有我和Jack知道，怕说出来又被老杨捶。后续的一整周都没睡好觉，一到晚上就爬起来手动同步，然后在Stackoverflow上查询解决问题的方法。最后还是Jack想出个主意：当时也有类似像Fiverr这样的网站叫Scriptlance（现在已经被Freelancer.com收购），哥们儿在上面找了个俄罗斯人帮我们解决了Bug。那件事儿后，我就决定Jack脑子挺活的，和大部分ABC不一样，而且当时他也受叶老板公司“狼性文化”的影响，一心想着赚钱。现在回想，他绝对属于比较典型的闷骚型理科男，表面淡定但内心狂野，和我一样。。。项目成功结束后，蔡总还特意把我俩带去KTV庆祝，档次明显没有叶老板去的高，但饥不择食。那天晚上蔡总喝多了，跟我俩感慨了几句。他说自己原来一直做小本生意，家庭也很幸福。结果有一次接到了个大型房地产项目，自己没啥经验但又不舍得放弃那个机会，然后就去贷款。后来项目烂尾，自己投入全打水漂。之后他就开始酗酒，不久后他老婆就带着孩子离开了，说到他女儿时泪流满面，声中充满了愧疚。最后他说了一句话我现在都还记得：“人生也许就那么一次改变命运的机会，有可能一步登天，也有可能一落千丈。” 那时候无法感同身受，但现在能体会他当初想表达的心路历程：从振奋，畏惧，绝望，再到忏悔，以及对命运喜怒无常的敬畏。蔡总项目结束后，我的团队一战成名，叶老板在内部会议上大肆表扬，还把我提升到项目总监，Jack提升到项目经理，团队整体Headcount增加到10个。升职的同时也意味着更大的职责，一方面叶老板要求团队立马开始设计之前他愿景中的BI系统，另一方面要求我维护现有客户关系。那会儿分了我两个客户：Paul和一个韩国的家电公司，那个客户是个40来岁的韩国老娘们儿，她的故事后面说。

 提拔我后，叶老板特意单独跟我吃了次饭，跟我强调作为总监需要走出纯技术的思考模式，开始往商务靠拢，而商务的核心就是如何把控客户关系。那顿饭我至今难忘，因为他给我上了堂宝贵的课，教会了我如何在不同的场合和客户拉近关系。这节课对我这种低情商的理科生来说无比重要，相当于一个新手指南。后期自己做生意时叶老板的教导已完全“入脑，入心，入魂”，成为自己的一部分。我总结下当时的要点，也结合一些后来自己的经验。和客户见面的场合分两大类，公开空间：办公室，咖啡厅。私密空间：餐厅包间，KTV。前者用来增加信任，后者用来增加感情。不管多么贪的客户，他的第一目的永远是保自己的脑袋，所以增加信任的目的是要说服他你很专业，跟你的公司合作项目不会做砸。在这种公开场合，必定会有其他人，他的同事，他的老板，即便私下和客户很熟也要装的公事公办。在公开空间的终极目标就是作秀，通过展现专业程度帮你的客户扫除内部所有阻止和你合作的障碍。一个很重要的里程碑就是客户敢在内部挺你的公司。因为对于一个大企业的打工人来说，和一家供应商走的太近存在很大的风险，即便这个人100%干净，内部也会传各种风言风语。因此一旦他Put his neck on the line，就是一条船上的人了。所以不要一上去就傻不愣登的跟客户提返点，在没有信任基础时他根本不会搭理你。与客户有信任基础后再邀请去私密场合，在这里尽量减少探讨工作，唯一例外就是一些不适合在公开场合探讨的话题，比如内部政治和返点。谈论内部政治的目的是打探赢得项目的概率，以及谁是最终决策者和其他利益相关者。这点很重要！塞钱tmd要塞对人，很多客户喜欢吹牛逼说自己权力多大，供应商上当给钱后发现哥们就是个小罗罗。还有客户专门到处骗吃骗喝，拿了钱不办事儿，所以这个环节很重要。返点最好在提交报价时再谈，这样能以个人利益为诱让客户在价格上放水，提高项目整体利润。这些之外就尽量只谈轻松话题了，这些话题的目的是让客户爽，他对什么来劲儿就说啥，就当和ChatGPT说话，你只负责提示，让他吹牛逼吹个爽。用个相声的比喻，你是于谦他是郭德纲，捧着他就对了。说到KTV这种私密场合，很多年轻的兄弟可能不知道到底在里面应该怎么Act，最重要的是想清楚你是去干嘛的，是tmd去谈生意的！别tmd精虫上头光顾着自己。下面是一些基本的行为准则，拿走不谢！

 1. 座位上有讲究，你永远和客户坐在一起，妹子坐你们俩外侧，这样你能和他对话，他也能摸得着妹子。别tmd傻了吧唧的男女男女那样坐，包间内本来声音就大坐那么远屁都听不到还得喊

 2. 选妹子尽量选北方姑娘或川妹，妹子的目的是活跃气氛和帮你挡酒，北方姑娘酒量普遍比南方姑娘好而且更热情。妹子报老家的时候听好了，别一个劲儿的盯着脸蛋儿看。别外，选妹子让客户先选还要说嘛？前两轮你别选，如果客户一直换这哥们儿必定是个事儿逼难伺候的主，项目上做好准备

 3. 在包间内走动起来，如果客户带了同事说明是他的亲信，过去认识下喝几杯，我有个生意持续做了很久就是因为原本客户的小兄弟接替了他。聊完把自己的妹子留给他，让哥们换换手感。然后自己再去和下一个兄弟聊，聊完把自己的妹子再叫过去陪新的兄弟，Rinse and Repeat一直到认识全屋子的人。记住妹子是你的工具，别像个色呸似的坐那儿一直猥亵她

 4. 不爱唱歌的至少学几首流行歌曲，唱歌是个活跃气氛的工具，之前有几个嗓门儿好的兄弟还真开口跪。当时我回国学的第一首流行歌就是刀郎的《2001年的第一场雪》，然后就一直很喜欢他的歌儿，很不符合ABC的形象，但有时候反差越大越有意思越有话题

 5. 妈咪是包间内你最好的朋友，有啥不懂的问她。不知道需要给谁小费和给多少问她，不知道妹子出不出台问她，要换妹子问她，总而言之问她就对了

 拉回来继续讲做项目的经历，团队扩大后我就不再花时间在日常对接上，Jack基本上能把95%的问题挡掉。我便开始规划叶老板的BI系统，那时候的想法是拿开源版的Google Analytics做基础，然后通过Cookie和用户ID的映射关系做到从流量（那时候大部分的引流形式都是Banner）到站内，最终串联销售数据（通过用户ID和电话号码的映射关系）做到完整的销售连分析。当初选的开源版系统文档有限，由一个波兰的小团队维护。所以还给了波兰人一些咨询费让他们提供更完整的接口文档。这个波兰团队的老板和我岁数相似，合作的过程中觉得这个人非常专业，所以一直在LinkedIn上保持联系。后期他做了一系列互联网广告相关的应用，虽然规模小但算是当地活的非常好的公司。18年我自己开公司做欧洲路演时，还和他在科隆喝酒，多年的网友见面哥们儿还特够意思，带了我去当地的娱乐场所。开发几个月后，BETA版本出炉，叶老板还专门为此产品给团队配了个漂亮的台湾女销售，跟我说：用好她，她是你的敲门砖。这妹子说话嗲嗲的，在我们团队唯一的女性，简直就是个团宠。那段时间的乐趣就是早上大伙儿下注，赌这妹子是不是又穿嗨丝短裙。回到讲产品，在给客户推荐的过程中遇到了几个问题：第一大部分的客户没有这个意识，一开始要花很长时间和客户解释叶老板的理念，而且有时候讲完了客户还是一头雾水，只能说的确有点超前了。第二即便听懂了大部分的客户也没有这个意愿，那个年代市场部的日子非常好过，因为整体经济往上走，不管做什么销售都会涨，所以大企业的老板不太在意营销预算怎么花。只有在缩紧裤腰带的大环境下，大家才会关注ROI。第三市场营销预算里水分极大，一层层的外包，再去掉其中的返点，有时候正真花在刀刃上的钱就剩下个凤毛麟角。因此那时候给市场部推荐一个分析系统等于加了层监控，反而把自己往坑里带。推了一段时间后毫无进展，但叶老板是个有抱负的人，他认为他的方向绝对正确，所以一直不肯放弃。事实证明他方向的确没错，后续几年大数据概念爆发，线下销售逐渐转线上导致数据更容易被追踪，互联网行业走向垄断形成闭环（消费者的完整销售轨迹可被监测，从接触到了解到最终购买产品都在BAT三家生态内完成），中国经济发展放缓导致大家更注重优化营销预算。。。这些发展都意味着企业越来越需要一个一体化的BI系统。现在回想，做生意就是这么有趣，踩对点，又没踩对时间，踩对时间，又没资源，有时候还真得天时地利人和，一点都不能缺。

 在叶老板公司呆了整整一年，一直到11年年底离职。这么好的老板加上自己也混的风生水起，为什么离开？这种非理性的决策只有一个答案：脑子被门夹了。。。好吧，是恋爱了。回到之前说过的ToastMaster俱乐部，我和Jack都在那里认识了我们的女朋友（和现在的老婆），下班后我们长一起去星光天地Double Date。她是我的第一个女友，也是我第一次真正的恋爱，那时候她有个机会去香港发展，我不想和她分开就果断辞职一起去。当爱情来的太凶猛时什么理性和利益都抛之脑后。上面说了那么多KTV和桑拿，现在又说恋爱了是不是觉得我是个伪君子？虽然我也不算什么好人，但我认为一个男人成熟的标志就是能区分开自己的女人和周边那些花花草草，前者是你的人生搭档，其余的都是你的工具。和叶老板提离职时他非常惊讶，但完全没劝我（估计知道我这种果断的性格劝了也没用），只说想好了就去做，开弓没有回头箭，但他需要我帮他找一个接班人。一开始我推荐把Jack提上来，但叶老板比较犹豫，说他的性格不适合做领导，所以就开始外聘。当时我和叶老板面试了很多人，其中大部分也都是海归，但没有一个能让叶老板满意，唯一他看上的哥们儿最后没来。我和那兄弟也只有一面之缘但一直保持着联系，因为那哥们儿太有个性了。澳洲海归，和我差不多大，IT背景但能说会道，应该算是候选人里唯一完全听懂了叶老板愿景的，但聊到最后哥们直接说其实他不是来应聘的，他是个自由职业开发者。他在LinkedIn上看了我和叶老板的背景后觉得我俩很有趣，纯粹的想认识一下。后续他去上海成为了一个连续创业者，上海封控那段时间我俩长通电话，当时他还给他们小区的邻居群做了个团购的微信应用，是个闲不下来的技术控。最后我和叶老板面试几周下来，一个合适的都没找到。同时我也一直在鼓励Jack主动去和叶老板谈接替我的位置，而且我给他出了个主意：当时组里的两个客户一个是Paul，关系很稳而且也是叶老板的徒弟。另外一个韩国的客户比较麻烦，我和Jack说你把这个客户搞定证明给叶老板你有维护客户关系的能力。那个韩国女客户，40来岁，单身，3门语言流利切换（中英韩），讲话很犀利，当时他们的官网和会员数据库都由我们开发和维护。姐们儿长会提一些尖锐的问题，和很多不合理的需求，而且动不动就跑叶老板那儿告状，估计那会儿他也烦的很。我在一线城市接触过好几个这种40来岁单身的女性，实话说没一个正常的，各个都是控制狂 + 虐待狂。而且有些也不收钱，纯为了虐你而虐你，享受那种高人一等的感觉，有时候觉得那些对单身都市女性的偏见还真有点道理。结果过了段时间，Jack还真把她给搞定，我不知道他具体用了什么方法（别想多，不是那个意思），但他对上点年纪的女性的确有一套，因为几年后陈Sir辞职，接替北京总经理岗位的是一个离了婚的50来岁姐们儿，当时Jack同样也把她搞得服服的。现在回想，我认为跟Jack的性格脱不开关系。打个游戏的比喻，我做生意基本上走战士路线，攻击性强，一路硬刚。后续我在香港还认识个小兄弟点的是盗贼的天赋树，这哥们儿各种不走寻常路，通过无厘头的方式解决问题。Jack更像个法师，会打圆场会哄人开心，虽然腼腆一些但他的ABC光环让他显得更友好，用现在的话来说可能比较符合“小奶狗”的形象，招老女人喜欢。都说性格决定命运，后面也正是Jack这种圆滑的性格一步步将他推向了深渊。

 搞定韩国老娘们儿后，叶老板便同意了提拨Jack，但他要求我继续给公司做一段时间的顾问，远程指导他。临走的那一周，我，Jack，叶老板吃了顿饭。吃到一半，一开始在KTV陪叶老板的金牌妹子带着她老公来找他。当时我还挺纳闷咋回事儿，后来才知道她们应该是来求叶老板帮忙的。具体什么个事儿我也不清楚，只记得那女的老公一脸憔悴的跟叶老板说话，我猜是问他借钱。叶老板当时一脸的不耐烦，感觉听都没在听那哥们儿讲话，然后点了根雪茄，用英文对我和Jack说：Some people just don’t know when to stop，然后笑着把她俩打发走了。回来后他对我说的一句话我依然记得：“你我都不适合做打工人，不甘心做一只羊，你早晚会把命运抓在自己手里。” 现在回想起来那段时光，感觉那家公司就像我的第二个家庭：叶老板我们的父亲，Paul和Jack我的兄弟。别外那时候我一直想不清楚为什么叶老板这样的大人物愿意花时间在我们这种小屁孩儿身上。作为他的员工，他完全没必要花钱带我们和他一起娱乐，我们当时的重要程度也没有那么高，完全可以被替代。这点是我后续做了更长时间的领导，和自己有了孩子后才明白的。西方有个说法：It’s lonely at the top，叶老板那时候海归不多，再加上拥有的财富和社会地位，和生意上错中复杂的利益关系，想想都窒息，因此我觉得他的内心无比孤单。记得有一次，叶老板带我和Jack在个餐厅吃饭，我喝高了突然在餐厅地上做起了俯卧撑，嘴上英文喊着：This is all I’m fucking good for！（现在就不解释这句话的涵义了，有机会写写我在美国张大的经历时再说）叶老板看了我一眼，便毫不犹豫地加入了我LOL。Jack就坐那儿一脸不可思地看着，估计心里想这俩2货。。。那次我看到了叶老板很少见的一面，他脸上的愉悦和放肆的表情，仿佛那一刻他可以完全做自己，不用端着，不用有一个“老板样”。有了自己的孩子后才意识到养孩子等于自己再活一遍，或许叶老板带着我们混也只是想再年轻一回。

 就这样我结束了在北京的两年（但我还是会回来的，好了不剧透了）。原本回国创业的雄心壮志被现实打了个响亮的巴掌。但养兵千日用兵一时，而我这一养就养了5年，直到2016年才遇到了蔡总提到改变命运的那一刻。这故事还得慢慢讲。。。
