---
人员: 
  - "[[回到Axton]]"
tags:
  - articles
日期: 2022-12-26
时间: 2024-08-11 01:47:18.896903+00:00
相关:
  - "[[book]]"
  - "[[dataview]]"
  - "[[gmail]]"
  - "[[google]]"
  - "[[json]]"
  - "[[live query]]"
  - "[[logseq]]"
  - "[[markdown]]"
  - "[[notion]]"
  - "[[obsidian]]"
  - "[[quick add]]"
  - "[[roam]]"
  - "[[supertag]]"
  - "[[tana]]"
  - "[[today]]"
  - "[[workflowy]]"
  - "[[yaml]]"
  - "[[书籍]]"
  - "[[双链]]"
  - "[[标签]]"
  - "[[生产力与自我提升]]"

链接: https://mp.weixin.qq.com/s/twj8S44z94W4iKvamZlDow
附件: https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaxGia85hcrc83kZO1F22HRCoMXbMkUee0ezDbVo3Eb90hrowkzmqicZ5ia2QSdMU1x3WGFsEzibdXRjQ/0?wx_fmt=jpeg)
---
## Document Note

## Summary

Tana 是一款创新的笔记应用，结合了 Workflowy、Roam 和 Notion 的优点，提供了快速记录和无压整理的体验。其主要特征包括统一的细粒度笔记结构、Supertag（超级标签）功能以及 Live Query（实时查询）。

首先，Tana 的笔记采用节点形式，没有文档或页面的传统概念，所有操作在不同界面中保持一致，简化了用户的操作流程。其次，Supertag 允许用户为标签添加描述性字段，像一个数据库一样管理信息，支持标签的继承，使得笔记管理更加灵活。最后，Live Query 让用户能够以多种方式展示笔记，类似智能文件夹的功能。

尽管 Tana 在功能上令人惊艳，但仍有不足之处，如编辑器体验和数据备份功能的改进空间。总体而言，Tana 的设计理念和创新功能让其在众多笔记软件中脱颖而出。

---

**[[问题]] 1：** Tana 如何实现笔记的统一性和灵活性？

[[答案]]：Tana 通过采用节点形式的笔记结构，消除了文档和页面的概念，使得所有操作在不同界面中一致，简化了操作流程，从而实现笔记的统一性和灵活性。

**[[问题]] 2：** Supertag 的主要功能是什么？

[[答案]]：Supertag 允许用户为普通标签添加描述性字段，能够管理更复杂的信息关系，并支持标签的继承，提供灵活的笔记管理方式。

**[[问题]] 3：** Tana 的 Live Query 有何作用？

[[答案]]：Live Query 允许用户将笔记以各种样式展示，类似智能文件夹，可以根据条件进行筛选，并直接在查询结果中对笔记进行操作，提升了信息管理的效率。

## Full Document
试用 Tana 已经 12 天了，我的感触就是太神了。Tana 主页说「你可以取消一半的软件订阅」那是真的没说错，至少我是再也不去纠结 Roam Research、Notion、Obsidian、Logseq 等等选哪个了。如果说这两年笔记软件最大的创新就是 Roam 的双链，那么 Tana 的 Supertag 算得上是又一大创新。

![[Attachments/e97050340513f28e7cbf8a8d98f1e5fe_MD5.webp]]
Tana 是什么？简单说就是 Workflowy + Roam + Notion，它集成了这三大软件的核心特色，**Workflowy 的大纲、Roam 的双链以及 Notion 的数据库**。Tana 通过统一性和灵活性两大特征真正做到了笔记软件的快速记录和无压整理。

今天我就来给大家分享一下它的三大亮点以及目前存在的问题，文章最后有我制作的一个很简单的数据管理 Supertag 免费分享给大家，方便初入门的朋友对 Supertag 以及继承逻辑快速建立一个感性的认识。

**▍****主流笔记软件的标配**

首先，Tana 具有当前主流笔记软件的标配，比如每日记录。点击 Today 或者按快捷键就能进入今日日记，日历在侧边栏上是以日周年的结构显示的。

![[Attachments/4d778f6dce981c614d40804fd2795fce_MD5.png]]
还有双链，输入 @ 就可以创建链接，如果在一个新的节点上，使用 @ 来创建链接，就会创建一个到原节点的引用；如果是输入一些内容之后再创建链接，这就是行内链接。

![[Attachments/0191eb26a8b604d59a3cb9028da1674f_MD5.png]]
然后就是标签/Tag，虽然它看起来就像是一个普通的标签，但实际上却已经脱胎换骨了——这就是 Tana 的独门秘籍 Supertag，咱们后面再讲 。

以上就是主流笔记软件的标配，每日记录、双链以及标签。下面咱们看看 Tana 的三大亮点到底是什么。

**▍****完全统一的细粒度**

Tana 给我留下最深的第一印象是完全统一的细粒度。

目前我知道的能做到这一点的除了 Tana 只有 Workflowy 和 RemNote。Tana 中所有的笔记都是节点/node，没有文档、页面或者块的概念，而且无论在任何界面下的操作都是一样的。比如在快速添加/Quick Add 里就像在正常的界面一样，可以操作所有 node 相关的功能，包括添加标签、添加双链等等。不是简单地给你个文本框先凑合输入些文字。

统一的笔记粒度带来了一致性的体验，首先我们不用考虑自己到底是该创建个 block 还是新建 page，也不用在嵌入时想用小括号还是用中括号——更重要的是，全局统一意味着完美的筛选和过滤。

筛选跟搜索可是不一样的，我们举个例子看看他们是怎么不一样了。

**▍****搜索与筛选的区别**

先看看搜索是什么。

比如我们在 Obsidian 中点击一个标签，实际上就是在以这个标签为关键词进行条件搜索，最后给出的是搜索后的结果。我们可以看到所有符合条件的文档，但并不能在搜索结果列表中去直接操作这些文档，必须要点击进入文档之后才能进一步操作。

这不光是操作上多了一个步骤，还会出现很多无关的信息来干扰我们。

![[Attachments/21b206e5532a7cda57588ac28fdf30dc_MD5.png]]
过滤就不一样了。虽然过滤本质上也是搜索，但是它在结果呈现上不一样。过滤给出的结果是可以直接在上面进行操作的，比如下面的书籍列表就是一个标签筛选，你可以进行各种操作，包括修改、新增，同时不会感觉到过滤后的节点跟实际存在的物理节点有什么不同。

比如《整理我的书籍》这就是一个物理节点，在这个节点下操作我的书本，跟在过滤后的节点下操作书籍效果是一样的，这样日后的整理也会因此享受到很大的便利。

![[Attachments/d9dd16d2204bf92b032e50a90c58d3f6_MD5.png]]
标签筛选结果

![[Attachments/a3df714ffdbee83bcb3be391e030c9e0_MD5.png]]
物理节点

简单总结一下，过滤与搜索的区别，就类似于是智能文件夹和文档搜索的区别。这给我们带来的好处是什么呢？

**第一就是我们可以在任意的地方记录笔记**。只要标记了合适的 Tag，就可以保证以后能找到它，并且整理起来也非常轻松。比如我要做一个书籍管理，当我需要增加一本新书的时候，我并不需要找到我的书籍笔记所在的页面再去添加一条记录。我只需要在我当前打开的页面，比如日记页面直接添加就可以。这是粒度统一以及最小化带来的第一个好处。

**第二个好处就是文档组织的灵活性**。首先，大纲笔记天生就具有树形结构，如果笔记中没有文件夹我们就会觉得不踏实，而在这里，我们完全可以按照自己喜欢的文件夹结构创建同样的大纲结构。

更重要的是，Tag 以及统一的笔记粒度，可以让我们更为灵活地使用类似智能文件夹的方式组织笔记，可以对我们的笔记库进行各种切片，呈现给希望看到的结果。由于 Tag 的加持，每条笔记都可以自带描述，说明自己是什么、有什么，或者叫自带元数据、自带属性。

我们用了很大的篇幅来说粒度统一和最小化，因为这对笔记的无压记录和整理是很重要的，一个笔记软件的底层数据结构决定了它的功能走向和未来发展前景，只要涉及到双链或者说筛的选文档型笔记 app ，我一般都是不看好的。

**▍** **Supertag 超级标签**

前面已经展示了 Tana 笔记组织的灵活性，而它做到这一点的独门秘籍就是 Supertag，超级标签。

什么是超级标签？简单说就是给一个普通的标签加上了一套能够描述它自身的数据结构。

咱们不妨先来看看这个起步功能。比如我的 Book 这个标签，在其他的笔记软件中，我要标记一条笔记是关于一本书的，我们也可以打上一个 Book 标签，但也仅限于此。如果我还希望描述更多关于书籍的属性呢？

比如作者是谁、哪年出版的，那在其他笔记 app 里面一个 Tag 就做不到了，在 Obsidian 里，我们可以用 YAML 来描述，然后喜欢折腾的同学还会用 DataView 来做出或卡片或列表的展示来；类似的操作在 Notion 里，很容易就发展为建立一个数据库。

但在 Tana 里，所有这些信息和功能都集成在一个 Supertag 里，这些信息比如作者、出版年月、评级等等都被叫做 Field/字段，配置好 Supertag 之后，任何一个打上 Book 标签的笔记，都会具有「书本」所需要的所有属性。

![[Attachments/65e01c5224395638bc3d32602e9aca9a_MD5.png]]
这就是 Tag 之所以 Super 的原因吗？并不是，更厉害的还在后面呢。

**▍****Supertag 继承**

Supertag 正走在实现一个关系型数据库或者说面向对象体系的路上。

什么意思呢？假设你原来是在管理汽车，你会需要一个汽车的 Supertag，**它有两个特征：四个轱辘、一个铁盒子**。所以你会给符合这个条件的笔记打上汽车的标签。

后来有一天，你突然需要分开管理电车和油车了，**两种车都是四个轱辘一个盒子，区别是一个是电池一个是油**。

一种方式是新建一个电车的标签，包括四个轱辘一个盒子一块电池，再建一个油车的标签，包括四个轱辘一个盒子一箱油；还有一种方式也是新建一个电车的标签，但是只包括一块电池这一个属性，同样新建一个油车的标签，只包括两油这一种属性。那么他们所具有的汽车的共同属性怎么办呢？都去共享原来的汽车标签的内容就可以了。

这就会形成如下图所示的标签关系，这就叫继承。电车和油车的标签继承了汽车标签中的内容。在 Tana 中，这种继承使用 Extend/扩展来表示。

![[Attachments/1efbc6b943bc26512134c3d46fbd030f_MD5.png]]
我们可以达到同样的管理效果，但是这种方式在逻辑上更为清晰。将来在管理和整理的过程中，也会更加灵活。

**▍****Live Query**

有了统一的笔记粒度以及 Supertag 的加持，负责把笔记以各种样式展现给你的 Live Query 就来了。

我们可以把它当作智能文件夹，比如我的 Book 列表就是个 Live Query，点击 Live Query 可以看到它的条件很简单，只要有 Book 标签就行。

当然我们也可以再添加各种条件，查询结果也可以以各种样式展现，比如表格、卡片；还可以在查询结果中进行分组，展示看板视图。

![[Attachments/288d48d6ab3866040a4493c91ab26f9d_MD5.png]]
**▍****不足之处**

Tana 是一个让我惊喜的笔记 app，符合我对一个主力笔记 app 的期望，作为一个处于早期测试阶段的服务具有如此完成度已经非常不简单了。

但这里还是要简单列举几点不足之处，或者说是个期望列表吧。

##### **编辑器体验**

Tana 最新版本给 Supertag 加入了一个强悍的功能，就是递归回朔，虽然我对这个功能也是非常兴奋，但是还是希望开发团队能够尽快把编辑器的体验再做好一些，比如支持更多的格式，包括标题、高亮等等。

还有呢就是目前虽然可以分栏，但是卷滚时所有分栏一起滚动，这个就有些尴尬。毕竟对于大多数人来说，编辑器的这些体验是更容易感受到的东西。

另外相对 Roam，Tana 似乎更像 Workflowy 一些，最典型的就是它不支持 Markdown，拷贝粘贴一个 Markdown 格式的链接是不会渲染成我们所希望的效果的，我们需要拷贝 HTML 链接，或者先拷贝文字、然后再把链接粘贴到文字上去。当然这一点不能说是缺点，只能说是取舍。但是还是希望能够支持一些常用的 Markdown 语法。

##### **自动备份**

Tana 是一个云服务 app，数据存在云端。虽然有人不喜欢数据在云上，我倒是对数据在不在本地并没有太多执念，但自动备份我还是希望能有的。

目前 Tana 只支持手动导出 JSON 格式的文档。JSON 格式的文档理论上是能够保证数据不会被限制在一个 app 内的，并且能够保留完整的笔记信息，但还是希望能够同时支持自动定时导出以及能够导出更易识别的文档，比如 Markdown。

文中使用的图书管理 Supertag 免费分享给大家，有兴趣的可以在文末链接获取。你可以直接在我的共享空间中查看 Supertag 结构，也可以把它直接克隆到自己的工作空间去，然后就可以想怎么改就怎么改了。

目前 Tana 是免费的，想要试用的朋友可以在他们的官网上登记邮箱，排队等待邀请。注意 Tana 目前只支持 Google 账号登录，因此记得用 Gmail 邮箱登记。还有在使用测试版的软件的时候自己要做好数据备份。

**相关链接**

图书管理 Supertag：

https://www.axton.blog/tana-supertag-share/

Tana 官网：

https://tana.inc/

原文链接：

https://sspai.com/post/77394?utm\_source=wechat&utm\_medium=social

作者：回到Axton

责编：克莱德

******/** **更多热门文章** **/******

[![[Attachments/617e0ca27dfd00dcbcb984967c43e6fe_MD5.png]]](http://mp.weixin.qq.com/s?__biz=MzU4Mjg3MDAyMQ==&mid=2247547592&idx=1&sn=6822ce317378a3b6d5484a20f8b5d74e&chksm=fdb3cfa2cac446b4c0140c86e11c2f14da6b3743fd6b6e906049f4bda50b2cf897b06f1b5108&scene=21#wechat_redirect)

[![[Attachments/5d331d9d1affa7f3c203c5fb0442d4b0_MD5.png]]](http://mp.weixin.qq.com/s?__biz=MzU4Mjg3MDAyMQ==&mid=2247547564&idx=1&sn=f33a3f2c2b4e3ae53a18c0fcf203df5b&chksm=fdb3cfc6cac446d051e24ff89deddee8bc8af6271639a9a0f365bdb05f28007cc103d2f4d1ab&scene=21#wechat_redirect)

![[Attachments/c2c9a34921f5770ab9b9a6cae16d2ebc_MD5.png]]
![[Attachments/d18edc8e5697c131c174c22d9926beab_MD5.gif]]
