---
人员: 
  - "[[火箭君]]"
tags:
  - articles
日期: 2024-08-17
时间: 2024-08-19 02:49:14.721128+00:00
相关:
  - "[[AI]]"
  - "[[AI工具]]"
  - "[[Capacities]]"
  - "[[DeskOrg Project]]"
  - "[[DropIt]]"
  - "[[Every]]"
  - "[[Figma]]"
  - "[[<PERSON> Juggler]]"
  - "[[Folder Tidy]]"
  - "[[freshen]]"
  - "[[GPT]]"
  - "[[macOS]]"
  - "[[OpenAI]]"
  - "[[P.A.R.A. 分类法]]"
  - "[[Sparkle]]"
  - "[[yml]]"
  - "[[上传]]"
  - "[[下载]]"
  - "[[云端相册]]"
  - "[[云笔记]]"
  - "[[人工智能]]"
  - "[[分类器]]"
  - "[[切分]]"
  - "[[压缩]]"
  - "[[打印]]"
  - "[[文件分类]]"
  - "[[文件名]]"
  - "[[文件夹]]"
  - "[[文件改名]]"
  - "[[文件整理]]"
  - "[[文件移动]]"
  - "[[文件管理]]"
  - "[[文档]]"
  - "[[服务器]]"
  - "[[桌面]]"
  - "[[生产力]]"
  - "[[电子发票]]"
  - "[[科技]]"
  - "[[笔记分类]]"
  - "[[自动化]]"

链接: https://mp.weixin.qq.com/s/ipou3ELDXeBre1sqpLGVFw
附件: https://mmbiz.qpic.cn/mmbiz_jpg/hQibibdG339M1G8ib1rRbdg9EL7ArubR1eJ1PRHECB0icv8sISEjWxWCqEL1VibfyBnvz4ziaaFX1AZt1SKRKuTKVOcQ/0?wx_fmt=jpeg)
---
## Document Note

## Summary

数字信息很多时候会以「文件」的形式「沉淀」下来。文件堆放的重灾区是……

## Full Document
### Image

文件堆放的重灾区

我们的工作和生活已经越来越依赖于电子设备和数字信息。数字信息很多时候会以「文件」的形式「沉淀」下来。比如：一张电子发票 PDF， 一张照片，一段备忘文本，一份说明记录，一个视频，一个设计稿文件 …… 尽管 很多 App 或 云服务 希望接管 某类文件的存储和分类， 比方说 云笔记 App，又比方说 Figma 这样的设计工具， 还有 云端相册之类的服务， 但终究会有些文件散落在我们本地，尤其对于信奉「本地优先」的小伙伴们来说，肯定会有大量信息以文件形式存放在自己的电脑上。

随着时间的推移，电脑里的文件变得堆积如山，杂乱无章。其中的「重灾区」是「桌面（Desktop）」「文档（Documnets）」「下载（Downloads）」， 无论各位用的是 Windows 还是 macOS，这几个通用的文件夹都是世界公认的「垃圾堆放处」，最容易变成下图这样：

![[Attachments/08e686c9f140ce2f4971d5b43881ed25_MD5.png]]

今天我想先简单介绍一下海外某新款实验室性质的 AI 文件整理 App， 然后，我会让大家比较一下几款经典的早期整理工具（直到今天还很有市场）， 以及我个人的一些实践方法。

Sparkle by Every

Sparkle 是一款基于 AI 的文件分类的工具，目前仅支持 macOS。Sparkle 由 Every 提供。Every 不是那个文件搜索工具 Everything， 而是一个知名的时事通讯网站（我个人很喜欢）。

Sparkle 可以帮助我们整理 Mac 上最常用的三个文件夹：桌面、文档 和 下载。

当我们设置 Sparkle 时，可以指定一些文件夹让 AI 处理，AI 会帮我们保持这些文件夹干净；同样也可以选择「例外」文件夹，让 App 不要去乱动。

Sparkle 会将文件分类到三个大类别子文件夹：

* 最近的库 （Recents）
* AI库（AI Library）
* 手动库 （Manual Library）

![[Attachments/d3d28e874f4caced83988c822d282bcb_MD5.png]]
最近库：任何少于三天的文件都放在这里，以方便访问。

AI库：任何*超过三天的文件*都放在这里。Sparkle 会自动为它在您电脑上找到的文件创建一个自定义子文件夹结构。新文件进入时将自动排序到该子文件夹结构中。子文件夹结构会根据 AI 判断进行分类（下图），效果就见仁见智了。

![[Attachments/965769c06a27833c158368bcdfe97a76_MD5.png]]
手动库： 如果你想手动维护文件夹结构，可以将选定的文件和文件夹放入手动库，Sparkle 就不会去动它们。

这三个文件夹能让所有文件至少看起来干净整洁，通过文件夹结构，让我们在需要的时候相对轻松地找到所需的东西。

Sparkle 自称使用 OpenAI 的 GPT 来组织您的文件。为此，文件名会被发送到 Every 的服务器，然后再发送到 OpenAI 。Every 还号称只会暂时存储文件名，并在30天后删除它们，以确保用户隐私安全。

其它经典工具

##### File Juggler

这款工具可以监控PC上的任何文件夹，并按照预设规则对文件做出反应。

例如：批量命名文件，将符合条件的文件自动移动到另一个文件夹，批量删除过期的文件 …

File Juggler 功能强大，但是过于专业，这是 AI时代 之前的工具，需要复杂甚至专业的设置，但提供准确可靠的效果。

![[Attachments/9215f7bfc36ed2c8df709f7bf0701a91_MD5.png]]
##### freshen

这是一个开源项目，火箭君发现其前身是 DeskOrg Project（已经不再维护）。

freshen 是一个简单明了的 「分类器」。通过 yml 配置文件（好吧，我相信很多人听到 yml 已经没有兴趣了），将符合特定条件的文件归纳到几个指定文件夹里， 工具可以追溯多个文件夹和下级深层子文件夹。

说实话，这就是一个快捷开源脚本，对于喜欢动手且要求不复杂的人来说，简直完美。

##### Folder Tidy

这是一款 macOS 下的付费工具（USD 5）。

类似 上面的 File Juggler， Folder Tidy 提供图形化的自定义规则设定，以及为苹果设备优化的文件处理性能。有趣的是可以一键撤销分类结果。感觉挺实用的。

![[Attachments/ca4047cfafac034c47ca106f3644111e_MD5.png]]
DropIt

既然说到文件分类，火箭君印象里，肯定要提下 DropIt 这款工具。

这是一款开源免费的工具，而且功能强大。

文件分类主要通过拖动（Drag&Drop）实现。将需要的批量文件拖动到 App 上， App会按着设定规则进行分类操作。这些操作不仅仅局限于 「移动到文件夹」或「文件改名」之类，还可以是更复杂的动作，比如：压缩，打印，上传，切分 …… 简直是一个文件操作的「百宝箱」。

![[Attachments/97fca40a76e1baeb815d6d67f0644c84_MD5.png]]
最后

我们看到上述的工具 大致分为 「前AI时代」和当下的「AI时代」。

「前AI时代」的工具，也就是上面的「经典工具」，大多需要自设规则，App 帮助我们实行自动化批量处理；而「AI 时代」的工具更像是，交给 AI 就完事了，不要自己动手。

两种类型工具各有利弊， 对于结果有准确要求的场合（例如企业应用，生产力应用），必须要按照规则行事；至于自己个人信息消费级别的场合， AI 分类可能就绰绰有余了。事实上，火箭君也看到过国内一些 号称用 AI 进行文件分类的商业生产力工具，我个人不是很喜欢， 一方面有隐私或商业秘密泄露的风险，另一方面其结果也差强人意。

我以前也提起过，文件分类不是一个仅凭文件名，文件类型 之类信息就能做好的工作， 而是有很多看不见的「上下文」参与其中。这些「上下文」信息才是关键所在。比如：我桌面上有一张「001.jpg」的图片，我不需要工具自动把它放到「图片」的文件夹里面，这个毫无意义。我希望 工具能把它放到【A项目】文件夹下的【反馈截屏】这个子文件夹下。这些信息，工具可能永远无法知道，而恰恰是这些信息，给文件分类带来了巨大价值。流行的什么 P.A.R.A. 分类法，可以理解为一个简化的解决方案。

![[Attachments/d7cd1b2217346215c708f311f99427a0_MD5.png]]
我个人的实践是，「桌面」上的文件用完就删掉，或者立刻归档。「下载」文件夹里面的文件，长期扔在那里，定期删掉历史的文件，因为多数文件都是一次性用途，有长久用途的应该已经在第一时间被归档了。所以第一时间的手工粗略分类很重要， 这和以前说的 Capacities 的笔记分类有异曲同工之妙。

##### 相关工具的网址

* Sparkle https://makeitsparkle.co/
* File Juggler https://www.filejuggler.com/features
* freshen https://github.com/saleguas/freshen-file-sorter
* Folder Tidy https://www.tunabellysoftware.com/folder\_tidy
* DropIt http://www.dropitproject.com

![[Attachments/a816e73204da581daa4d24653178bd54_MD5.webp]]
