---
人员: 
  - "[[<PERSON> Who]]"
tags:
  - articles
日期: 2021-07-15
时间: None
链接: https://mp.weixin.qq.com/s/Z8qtLyJRaL5p0r0zZinyTg
附件: http://mmbiz.qpic.cn/mmbiz_png/TDibWgTpJibRVHx3WT1J18RBE4BT2vksgbLPu5XH2Dns8o0VUNt3xRBufNj2tu2VCGbvpnmyI3WBqula3zF7oQ7A/0?wx_fmt=png)
---
## Document Note

## Summary

Templater介绍- 一个Obsidian的插件，作用是模板- 本次介绍包含如下效果：- 向你的文件中插

## Full Document
***Templater介绍***

- 一个Obsidian的插件，作用是模板

- 本次介绍包含如下效果：

- 向你的文件中插入模板

- 从模板创建文件到制定位置

- 提问式输入并插入到固定文件位置或新建文件

***文章中使用的模板 github 地址***

[GitHub - chhoumann/Templater\_Templates: My templates for the Templater Obsidian.md plugin.](https://github.com/chhoumann/Templater\_Templates)

***1. 从模板创建文件到某个文件夹下***

**代码**

```
const choices = [  
  
 {option: "月复盘", path: "Templates/复盘/Review月-月复盘模板.md", folder:"复盘"},  
  
 {option: "读书笔记", path: "Templates/输入笔记/读书笔记模板.md", folder:"知识宝箱/采集库",appendLink: true},  
  
]
```

**注释**

| 字段 | 是否必须 | 类型 | 释义 |

| ---------- | -------- | --------------------- | -------------------- |

| option | 是 | 字符串 | 选项显示名称 |

| path | 是 | 字符串 | 模板文件的路径 |

| folder | 否 | 字符串/数组 | 文件保存在哪个文件夹 |

| format | 否 | 字符串 | 写入内容 |

| appendLink | 否 | 布尔型（true或false） | 如果为true，则将指向所添加文件的链接写入光标位置。 |

***2.1 提问式效果***

![图片](https://mmbiz.qpic.cn/mmbiz_png/TDibWgTpJibRVHx3WT1J18RBE4BT2vksgbLPu5XH2Dns8o0VUNt3xRBufNj2tu2VCGbvpnmyI3WBqula3zF7oQ7A/640?wx_fmt=png&tp=webp&wxfrom=5&wx_lazy=1)
**代码**

```
const choices = [  
  
 {  
  
 option: "💸 处理恐惧",   
  
 captureTo: "日常/日志/{{DATE:YYYY-MM-DD_dddd}}",   
  
 insertAfter: "# 感受",  
  
 format:`## {{VALUE:你现在有什么感觉？}}\n1. 行为目标：{{VALUE:你希望达成的行为目标是什么？}}\n2. 相反行为：{{VALUE:你正在做哪些跟目标相反的行为？}}\n3. 背后好处：{{VALUE:这些行为背后有哪些好处？}}\n4. 前提假设：{{VALUE:让这些好处成立的前提假设是什么？}}`,  
  
 },  
  
]
```

***2.2 插入到某个文件的某个地方***

![图片](https://mmbiz.qpic.cn/mmbiz_gif/TDibWgTpJibRVHx3WT1J18RBE4BT2vksgbp7WRtWcmlsGpZcjHKWIfGREpP2VuQUicQXz0YxfMXCnxBYIYXGIiblog/640?wx_fmt=gif&tp=webp&wxfrom=5&wx_lazy=1)
**代码**

```
const choices = [  
  
 {option: "✔ 任务", captureTo: "日常/日志/{{DATE:YYYY-MM-DD_dddd}}", insertAfter: "# TODO",task: true,format: "{{VALUE}}\n"},  
  
 {option: "💭 想法", captureTo: "日常/日志/{{DATE:YYYY-MM-DD_dddd}}", insertAfter: "# 思考", format: "- {{DATE:HH:mm}} {{VALUE}}\n"},  
  
]
```

**注释**

| 字段 | 是否必须 | 类型 | 释义 |

| ----------- | -------- | --------------------- | ---------------------- |

| option | 是 | 字符串 | 选项显示名称 |

| captureTo | 是 | 字符串 | 写到哪个文件 |

| insertAfter | 否 | 字符串 | 插入文件的哪一行字下面 |

| format | 否 | 字符串 | 写入内容 |

| prepend | 否 | 布尔型（true或false） | 如果为true, value将被添加到文件的底部。默认是false |
