---
人员: 
  - "[[augmentingcognition.com]]"
tags:
  - articles
日期: 2018-07-03
时间: None
链接: http://augmentingcognition.com/ltm.html
附件:
---
## Document Note

## Summary

## Full Document
**相关资源**  

[迈克尔·尼尔森在推特上](https://twitter.com/micha<PERSON>_nielsen)  

[迈克尔·尼尔森的项目公告邮件列表](http://eepurl.com/0Xxjb)  

[cognitivemedium.com](http://cognitivemedium.com)

作者：[<PERSON>](http://michaelnielsen.org)

20世纪20年代中期的一天，一位名叫Solomon Shereshevsky的莫斯科报纸记者进入了心理学家Alexander Luria的实验室。Shereshevsky在报纸上的老板注意到Shereshevsky从不需要做任何笔记，但不知何故，他仍然记得他被告知的一切，并建议他让专家检查自己的记忆力。

Luria开始测试Shereshevsky的记忆力。他从简单的测试、单词和数字的短字符串开始。<PERSON><PERSON><PERSON><PERSON>轻松地记住了这些，因此Luria逐渐增加了琴弦的长度。但无论他们呆了多久，Shereshevsky都可以背诵它们。Luria着迷地继续研究了Shereshevsky接下来30年的记忆。在一本总结他的研究\*\*亚历山大·卢里亚的书中，《记忆学家的思想》，哈佛大学出版社（1968年），卢里亚报道说：

> [我]似乎对S.的记忆*能力*或*他保留的痕迹*的*耐久性*都没有限制。实验表明，他毫不费力地重现任何冗长的一系列单词，尽管这些单词最初是在一周、一个月、一年甚至许多年前提交给他的。事实上，其中一些旨在测试他记忆的实验是在他最初回忆这些单词的会议的十五或十六年后进行的（没有给他任何警告）。然而，他们总是成功的。

这样的故事令人着迷。记忆是我们思维的基础，拥有完美记忆的概念是诱人的。与此同时，许多人对自己的记忆感到矛盾。我经常听到人们说“我的记忆力不是很好”，有时是羞怯的，有时是道歉的，有时甚至是挑衅的。

鉴于中央存储器对我们的思维有多重要，很自然地会问计算机是否可以用作工具来帮助提高我们的内存。事实证明，这个问题是高超的好主意，追求它导致了计算史上许多最重要的愿景文件。一个早期的例子是万尼瓦尔·布什1945年的提案\*\*万尼瓦尔·布什，[正如我们可能认为](https://www.theatlantic.com/magazine/archive/1945/07/as-we-may-think/303881/)的那样，大西洋（1945年）。用于机械内存扩展器，memex。布什写道：

> memex是一种设备，个人在其中存储他所有的书籍、记录和通信，并被机械化，以便以超快的速度和灵活性进行咨询。这是对他记忆的放大亲密补充。

memex的愿景启发了许多后来的计算机先驱，包括Douglas Engelbart关于增强人类智能的想法，Ted Nelson关于超文本的想法，以及间接地，Tim Berners-Lee对万维网的概念\*\*例如，见：Douglas Engelbart，[增强人类智力](http://augmentingcognition.com/assets/Engelbart1962.pdf)（1962年）；Ted Nelson，[复杂信息处理：复杂、变化和不确定的文件结构](https://dl.acm.org/citation.cfm?id=806036)（1965年）；以及Tim Berners-Lee，[信息管理：提案](https://w3.org/History/1989/proposal.html)（1989）。在他的网络提案中，Berners-Lee描述了他的雇主（粒子物理组织CERN）需要开发一种集体机构记忆，

> 一个要开发的信息池，可以随着组织及其描述的项目而增长和发展。

这些只是使用计算机增强人类记忆的众多尝试中的一小部分。从memex到网络，再到wikis toorg[-mode](https://orgmode.org/)，再到[Project Xanadu](https://en.wikipedia.org/wiki/Project_Xanadu)，再到试图[绘制一个人所想的每个想法的地图](https://users.speakeasy.net/~lion/nb/book.pdf)：记忆的增强一直是计算的极其生成性愿景。

在这篇文章中，我们研究了个人记忆系统，即旨在改善一个人长期记忆的系统。在文章的第一部分，我描述了我使用这样一个名为Anki的系统的个人经历。正如我们将看到的，Anki几乎可以记住任何事情。也就是说，Anki让记忆成为一种*选择*，而不是一个随意的事件，留给机会。我将讨论如何使用Anki来理解研究论文、书籍和其他很多东西。我将描述Anki使用的许多模式和反模式。虽然Anki是一个非常简单的程序，但可以使用Anki开发精湛的技能，这种技能旨在深入了解复杂的材料，而不仅仅是记住简单的事实。

文章的第二部分一般讨论了个人记忆系统。许多人矛盾地甚至贬低地将记忆视为一种认知技能：例如，人们经常谈论“记忆记忆”，好像它不如更高级的理解。我将反对这一观点，并证明记忆是解决问题和创造力的核心。同样在这第二部分，我们将讨论认知科学在构建个人记忆系统方面的作用，以及更广泛地说，在构建增强人类认知的系统方面的作用。在未来的一篇文章《[Toward a Young Lady's Illustrated Primer](http://augmentingcognition.com/taylip.html)》中，我将描述更多关于个人记忆系统的想法。

这篇文章的风格不同寻常。这不是一篇传统的认知科学论文，即对人类记忆及其工作原理的研究。它也不是计算机系统设计论文，尽管原型系统是我自己的主要兴趣。相反，这篇文章是对个人记忆系统如何运作的非正式、*临时*观察和经验法则的提炼。我想把这些理解为构建我自己的系统做准备。当我收集这些观察结果时，其他人似乎对它们感兴趣。您可以合理地将这篇文章视为操作指南，旨在帮助发展个人记忆系统的精湛技能。但由于写这样一本指南不是我的主要目的，它可能是一个比你更想知道的指南。

为了结束这个介绍，请说几句关于这篇文章不会涵盖的内容。我只会简要讨论可视化技术，如记忆宫殿和位点方法。这篇文章不会描述使用药物来改善记忆，也不会描述未来可能使用脑机接口来增强记忆。这些都需要单独的治疗。但是，正如我们将看到的，关于个人记忆系统，已经有一些完全基于信息结构和呈现的强大想法。

#### 第一部分：如何记住几乎任何东西：Anki系统

我将首先讲述我自己对个人记忆系统[Anki](https://apps.ankiweb.net/)的体验\*\*我与Anki完全没有关系。其他类似的系统包括[Mnemosyne](https://mnemosyne-proj.org)和[SuperMemo](https://supermemo.com)。我的有限用途表明Mnemosyne与Anki非常相似。SuperMemo只在Windows上运行，我还没有机会使用它，尽管我受到了[SuperMemo网站上](https://supermemo.com)的文章的影响。

我不会试图把我对Anki的热情隐藏在一个受人尊敬的公正外表背后：这是我生活的重要组成部分。尽管如此，它仍然有很多局限性，我会在文章中提到其中一些。如上所述，这些材料非常个人化，是我自己的观察和非正式经验法则的集合。这些经验法则可能不适用于其他人；事实上，我可能误解了它们对我的适用程度。这当然不是对Anki用法进行适当控制的研究！尽管如此，我仍然相信收集这些个人经历是有价值的，即使它们是轶事和印象派的。我不是记忆认知科学的专家，如果能纠正任何错误或误解，我将不胜感激。

乍一看，Anki似乎只不过是一个计算机化抽认卡程序。您输入一个问题：

以及相应的答案：

稍后，您将被要求查看卡片：即显示问题，并询问您是否知道答案。

Anki比传统抽认卡更好的是，它管理审查时间表。如果你能正确回答一个问题，评论之间的时间间隔会逐渐扩大。因此，评论之间的一天间隔变成了两天，然后是六天，然后是两周，以此类。这个想法是，信息正变得越来越牢固地嵌入你的记忆中，因此需要减少审查。但是，如果您错过了答案，时间表会重置，并且您必须再次建立评论之间的时间间隔。

虽然计算机管理评论之间的间隔显然很有用，但可能看起来没什么大不了的。妙语是，事实证明，这是一种更有效的记忆信息的方式。

效率高多少？

为了回答这个问题，让我们做一些粗略的时间估计。平均而言，我大约需要8秒钟来查看一张卡片。假设我正在使用传统的抽认卡，并每周查看一次（比如）。如果我想在接下来的20年里记住一些东西，我需要20年乘以每年52周乘以每张卡8秒。每张卡的总审查时间刚刚超过2小时。

相比之下，Anki不断扩大的审查间隔迅速超过一个月，然后超过一年。事实上，对于我个人的一套Anki卡，评论之间的平均间隔目前为1.2年，并且还在上升。在下面的[附录中](http://augmentingcognition.com/ltm.html#Anki_analysis)，我估计，对于一张普通卡，在整个20年里，我只需要4到7分钟的总审查时间。这些估计允许偶尔失败的审查，重置时间间隔。在传统抽认卡所需的2个多小时中，节省了20多倍。

因此，我有两个经验法则。首先，如果在未来记住一个事实似乎值得我花10分钟的时间，那么我会这样做\*\*我首先在Gwern Branwen对间隔重复的评论中看到了沿着这些思路的分析：Gwern Branwen，[间隔重复](https://www.gwern.net/Spaced-repetition)。他的数字比我的略多一点乐观——他得出了5分钟的经验法则，而不是10分钟——但大体上是一致的。Branwen的分析反过来是基于以下分析：Piotr Wozniak，[学习中间隔重复的理论方面](http://super-memory.com/articles/theory.htm)。其次，并取代第一个，如果一个事实似乎引人注目，那么它就会进入Anki，无论它是否值得我未来10分钟的时间。例外的原因是，我们知道的许多最重要的事情都是我们不确定是否重要的事情，但我们的直觉告诉我们这些事情很重要。这并不意味着我们应该记住一切。但值得培养记忆的品味。

Anki带来的最大变化是，这意味着记忆不再是一个偶然的事件，只能任其偶然。相反，它保证我会以最小的努力记住一些东西。也就是说，*Anki将记忆作为一种选择*。

Anki可以用来做什么？我在生活的各个方面都使用Anki。在专业方面，我用它来从论文和书籍中学习；从谈话和会议中学习；帮助回忆在谈话中学到的有趣事情；并记住在日常工作中所做的关键观察。就我个人而言，我用它来记住与我的家庭和社会生活相关的各种事实；关于我的城市和旅行；以及关于我的爱好。在文章的后面部分，我描述了一些有用的Anki使用模式，以及要避免的反模式。

在大约2年半的常规使用中，我用Anki创建了10,000多张卡片。这包括7个月的休息时间，当时我很少做新卡。当我跟上我的卡片审查时，每天大约需要15到20分钟。如果它通常上升到20分钟以上，这通常意味着我添加卡片的速度太快，需要放慢速度。或者，这有时意味着我的卡片审查落后了（我稍后会讨论）。

在实践层面上，我使用桌面Anki客户端输入新卡，移动客户端\*\*桌面客户端是免费的，但在撰写本文时，移动客户端是25美元。许多人认为这“太贵了”。就我个人而言，我发现价值超过25美元有几个数量级。对我来说，Mobile Anki肯定比在价格适中的餐厅吃一顿饭更有价值。我边走边拿早上的咖啡，一边排队等候，在运输途中，等等，查看我的Anki卡。只要我的头脑一开始就相当放松，我发现评论体验是冥想的。另一方面，如果我的头脑不放松，我发现复习更困难，而Anki会让我的头脑跳来跳去。

我在开始使用Anki时遇到了麻烦。几个熟人强烈推荐它（或类似的系统），多年来，我多次尝试使用它，每次都很快放弃。回想起来，如果你想养成一种习惯，需要克服这些障碍。

Anki最终让我“接受”一个习惯，把它变成一种习惯，是一个我开玩笑的项目。多年来，我一直对从未真正学习过Unix命令行感到沮丧。我只学过最基本的命令。学习命令行对于编程的人来说是一种超能力，所以熟悉似乎是非常可取的。因此，为了好玩，我想知道是否有可能使用Anki从根本上完全记住一本关于Unix命令行的（短）书。

曾经是！

我选择了O'Reilly Media的《Macintosh终端袖珍指南》，作者是Daniel Barrett。我不是说我真的记住了这本书的整个文本\*\*我后来用查尔斯·狄更斯的《双城记》做了一个实验，看看是否真的可以记住整个文本。几周后，我得出结论，这是可能的，但不值得花时间。所以我删除了所有的卡片。删除后发生了一件有趣的事情：这本书的前几句话在我的记忆中逐渐腐烂，我现在只有碎片。我偶尔会想知道完整地背诵一本好书会有什么影响；如果它极大地影响了我自己的语言和写作，我不会感到惊讶。但我确实记住了书中的许多概念知识，以及书中大多数命令的名称、语法和选项。例外是我没有参考框架来想象使用的东西。但我确实记住了大多数我能想象使用的东西。最后，我大概涵盖了这本书的60%到70%，跳过或略读那些似乎与我无关的作品。尽管如此，我对命令行的了解还是大大增加了。

选择这个相当可笑的目标，尽管非常有用，却让我对Anki充满信心。这很令人兴奋，很明显，Anki会让我轻松学习以前对我来说相当乏味和困难的东西。反过来，这种信心使建立Anki的习惯变得容易得多。与此同时，该项目还帮助我学习了Anki界面，并让我尝试了提出问题的不同方式。也就是说，它帮助我培养了熟练使用Anki所需的技能。

##### 使用Anki彻底阅读一个陌生领域的研究论文

我发现Anki在阅读研究论文时有很大的帮助，特别是在我专业知识之外的领域。作为这如何运作的一个例子，我将描述我阅读2016年论文的经验\*\*David Silver、Aja Huang、Chris J。Maddison，Arthur Guez*等人*，[通过深度神经网络和树搜索掌握围棋游戏](http://augmentingcognition.com/assets/Silver2016a.pdf)，Nature（2016）。描述了AlphaGo，谷歌DeepMind的计算机系统，击败了Go游戏中一些世界上最强大的玩家。

在AlphaGo击败历史上最强的人类围棋选手之一Lee Sedol的比赛后，我向[Quanta杂志](https://www.quantamagazine.org/)建议我写一篇关于该系统的文章\*\*Michael Nielsen，[AlphaGo真的这么大交易吗？](https://www.quantamagazine.org/is-alphago-really-such-a-big-deal-20160329/)，Quanta（2016）。AlphaGo在当时是一个热门的媒体话题，故事中最常见的角度是人类的兴趣，将AlphaGo视为长期人与机器叙事的一部分，其中填充了一些技术细节，主要是颜色。

我想从不同的角度出发。在20世纪90年代和21世纪初，我相信人类或更好的一般人工智能是遥远而未有的。原因是，在那段时间里，研究人员在构建系统方面进展缓慢，以进行直观的模式匹配，这种模式匹配是人类视觉和听觉的基础，以及玩围棋等游戏。尽管人工智能研究人员付出了巨大努力，但人类发现许多模式匹配的壮举对机器来说仍然是不可能的。

虽然我们在这组问题上的进展非常缓慢，但2011年左右，在深度神经网络的进步的推动下，进展开始加快。例如，在某些有限的任务中，机器视觉系统迅速从可怕发展到与人类相媲美。当AlphaGo发布时，说我们不知道如何构建计算机系统来进行直观的模式匹配已经不正确了。虽然我们还没有解决这个问题，但我们正在取得快速进展。AlphaGo是这个故事的重要组成部分，我希望我的文章能探索构建计算机系统以捕捉人类直觉的概念。

虽然我很兴奋，但写这样一篇文章会很困难。这需要比典型的新闻文章更深入地了解AlphaGo的技术细节。幸运的是，我对神经网络了解相当多——我写了一本关于神经网络的书\*\*迈克尔·A。尼尔森[，《神经网络和深度学习》，](http://neuralnetworksanddeeplearning.com)决心出版社（2015年）。但我对围棋游戏一无所知，也对AlphaGo使用的许多想法一无所知，这些想法基于一个被称为强化学习的领域。我需要从头开始学习这些材料，为了写一篇好文章，我需要真正了解基础技术材料。

这是我是如何去做的。

我从AlphaGo[纸](http://augmentingcognition.com/assets/Silver2016a.pdf)本身开始。我很快就开始读它，几乎略读了一下。我不是在寻求一个全面的理解。相反，我在做两件事。第一，我试图简单地确定论文中最重要的想法。我需要学习的关键技术叫什么名字？其次，有一种胡说法的过程，寻找我很容易理解的基本事实，这显然会让我受益。基本术语、围棋规则等。

以下是我在这个阶段对Anki提出的那种问题的一些例子：“围棋盘的大小是多少？”;“谁在围棋中先打？”;“AlphaGo从多少个人类游戏位置中学习了？”;“AlphaGo从哪里获得其训练数据？”;“AlphaGo使用的两种主要类型的神经网络的名称是什么？”

如您所见，这些都是基本问题。它们是在最初通过报纸时很容易被捡到的东西，偶尔会离题搜索谷歌和维基百科，等等。此外，虽然这些事实很容易孤立地理解，但它们似乎也有助于更深入地了解论文中的其他材料。

我以这种方式快速地在纸上进行了几次检查，每次都越来越深。在这个阶段，我并没有试图获得对AlphaGo的完全理解。相反，我试图建立我的背景理解。在任何时候，如果有些事情不容易理解，我并不担心，我只是继续前进。但当我重复通过时，容易理解的事情范围越来越大。我发现自己添加了关于AlphaGo神经网络输入的功能类型、关于网络结构的基本事实等的问题。

在报纸上读了五六遍之后，我回去尝试了一次彻底的阅读。这次的目的是详细了解AlphaGo。到目前为止，我了解了大部分背景背景，进行彻底阅读相对容易，当然比进入报纸冷淡要容易得多。不要误会我的意思：它仍然具有挑战性。但这比其他地方要容易得多。

在对AlphaGo论文进行了一次彻底的通过后，我以类似的方式进行了第二次彻底的通过。然而，更多的就位了。此时，我对AlphaGo系统相当了解。我向Anki提出的许多问题都是高水平的，有时处于原始研究方向的边缘。我当然非常了解AlphaGo，以至于我有信心写出我文章中关于它的章节。（在实践中，我的文章覆盖了几个系统，而不仅仅是AlphaGo，我也必须使用类似的过程来了解这些系统，尽管我没有深入。）我在写文章时继续添加问题，最后总共添加了数百个问题。但到目前为止，最艰苦的工作已经完成了。

当然，与其使用Anki，我本可以记下传统的笔记，使用类似的过程来建立对论文的理解。但使用Anki给了我信心，从长远来看，我会保留很多理解。大约一年后，DeepMind发布了描述后续系统的论文，称为AlphaGo Zero和AlphaZero\*\* For AlphaGo Zero，请参阅：David Silver，Julian Schrittwieser，Karen Simonyan，Ioannis Antonoglou*等人*，[掌握人类不知情的围棋游戏](http://augmentingcognition.com/assets/Silver2017a.pdf)，《自然》（2017）。对于AlphaZero，请参阅：David Silver、Thomas Hubert、Julian Schrittwieser、Ioannis Antonoglou*等人*，[使用通用强化学习算法](https://arxiv.org/abs/1712.01815)（2017）[通过自玩来掌握国际象棋和象棋](https://arxiv.org/abs/1712.01815)。尽管在这段时间里，我对AlphaGo或强化学习考虑得很少，但我发现我可以轻松地阅读这些后续论文。虽然我没有试图像最初的AlphaGo论文那样彻底地理解这些论文，但我发现我可以在不到一个小时的时间内很好地理解这些论文。我保留了我之前的大部分理解！

相比之下，如果在我最初阅读AlphaGo论文时使用传统的笔记，我的理解会更快地消失，阅读后来的论文需要更长的时间。因此，以这种方式使用Anki会给你信心，你会长期保持理解。这种信心反过来又使最初的理解行为更加愉快，因为你相信你正在长期学习一些东西，而不是你在一天或一周内会忘记的东西。

好的，但是一个人用它做什么呢？...[N]如果我拥有所有这些力量——一个永远不会忘记的机械傀儡，永远不会让我忘记我选择的任何东西——我选择记住什么？——[Gwern Branwen](https://www.gwern.net/Spaced-repetition) 

整个过程花了我几天时间，持续了几周。那是很多工作。然而，回报是，我在现代深度强化学习中获得了相当不错的基础。这是一个极其重要的领域，在机器人学中具有很大的作用，许多研究人员认为它将在实现通用人工智能方面发挥重要作用。几天后，我从对深度强化学习一无所知，变成了对该领域的一篇关键论文的持久理解，这篇论文利用了整个领域使用的许多技术。当然，我离成为专家还有很长的路要走。关于AlphaGo有很多我不了解的重要细节，我必须做更多的工作才能在该地区建立自己的系统。但这种基本的理解是建立更深入专业知识的良好基础。

值得注意的是，我正在阅读AlphaGo论文，以支持我自己的一个创意项目，即为《广达》杂志写一篇文章。这很重要：我发现Anki在为一些个人创意项目服务时效果要好得多。

相反，很容易用Anki来储存知识，以应对未来的某个日子，去想“哦，我应该了解非洲的地理，或者了解第二次世界大战，或者[...]”。对我来说，这些目标在智力上很有吸引力，但我并没有在情感上投入。我已经试过很多次了。它往往会产生冷酷而毫无生气的Anki问题，这些问题在以后的审查中，我发现这些问题很难联系起来，而且很难真正、深刻地内化答案。问题在于，不知何故，在最初的想法中，我“应该”了解这些事情：从智力上讲，这似乎是一个好主意，但我几乎没有情感承诺。

以最不守纪律、最不敬和最原始的方式，努力研究你最感兴趣的东西。——理查德·费曼

相比之下，当我阅读以支持一些创意项目时，我会问更好的Anki问题。我发现在情感上更容易与问题和答案联系起来。我只是更关心他们，这很重要。因此，虽然使用Anki卡来学习以准备一些（可能是假设的）未来使用是诱人的，但最好找到一种方法来使用Anki作为一些创意项目的一部分。

##### 使用Anki对论文进行浅读

我基于Anki的大部分阅读都比我阅读的AlphaGo论文要浅得多。我通常要花10到60分钟，而不是花几天时间写论文，有时写一篇非常好的论文，时间会更长。以下是一些我发现在浅读中有用的一些模式的笔记。

如上所述，我通常将阅读作为某个项目背景研究的一部分。我会找到一篇新文章（或一组文章），通常会花几分钟来评估它。这篇文章似乎可能包含与我的项目相关的实质性见解或挑衅——新问题、新想法、新方法、新结果？如果是这样，我会读一读。

这并不意味着阅读报纸上的每一个字。相反，我将向Anki添加关于论文的核心主张、核心问题和核心想法的问题。从摘要、引言、结论、图形和图形标题中提取Anki问题特别有帮助。通常，我会从论文中提取5到20个Anki问题。提取少于5个问题通常是一个坏主意——这样做往往会让论文在我的记忆中成为一种孤立的孤儿。后来我发现很难感觉到与这些问题的联系。换句话说：如果一篇论文太无趣了，以至于无法添加5个关于它的好问题，通常最好根本不添加任何问题。

这个过程的一个失败模式是如果你Ankify\*\*，即进入Anki。Ankification*等*形式也很有用，具有误导性工作。许多论文都包含错误或误导性的陈述，如果你记住这些项目，你就会主动让自己更愚蠢。

如何避免误导性工作？

举个例子，让我描述一下我最近读到的一篇论文，作者是经济学家Benjamin Jones和Bruce Weinberg\*\*Benjamin F. Jones和Bruce A.Weinberg，[科学创造力中的年龄动态](http://augmentingcognition.com/assets/Jones2011a.pdf)，美国国家科学院院刊（2011年）。这篇论文研究了科学家做出最大发现的年龄。

我一开始就应该说：我没有理由认为这篇论文具有误导性！但它也值得谨慎。作为这种谨慎的一个例子，我向Anki添加的问题之一是：“琼斯2011声称物理学诺贝尔奖获得者在1980-2011年做出获奖发现的平均年龄是多少？”（答案：48）。另一个变体问题是：“哪篇论文声称物理学诺贝尔奖获得者在1980-2011年期间平均年龄为48岁？”（答案：Jones 2011）。等等。

这些问题符合基本主张的资格：我们现在知道这是琼斯2011年的主张，我们依靠琼斯和温伯格数据分析的质量。事实上，我还没有仔细研究该分析，以至于认为这些诺贝尔奖得主的平均年龄是48岁。但事实是，他们的报纸声称是48。这些都是不同的事情，后者最好用Ankify。

如果我特别关注分析的质量，我可能会添加一个或多个问题，即是什么让这种工作变得困难，例如：“正如《琼斯2011》所讨论的，在确定诺贝尔奖得主发现时的年龄，挑战是什么？”好的答案包括：很难弄清楚哪篇论文包含了诺贝尔奖得主的作品；论文的出版有时被推迟了几年；有时工作分散在多篇论文上；等等。想到这些挑战让我想起，如果琼斯和温伯格马马虎虎，或者只是犯了一个可以理解的错误，他们的数字可能会偏离。现在，碰巧的是，对于这篇论文，我不太担心这些问题。所以我没有回答任何这样的问题。但在构思问题时值得小心，这样你就不会误导自己。

阅读论文时的另一个有用模式是令人振奋的数字。例如，这是琼斯2011年的一张图表，显示了物理学家在40岁（蓝线）和30岁（黑线）时做出获奖发现的概率：

我有一个Anki的问题，它简单地说：“将琼斯2011年由物理学家在30岁和40岁之前做出获奖发现的概率曲线可视化。”答案是上面显示的图像，如果我的心理图像大致沿着这些思路，我认为自己是成功的。我可以通过添加以下问题来加深我对图表的参与：：“在琼斯2011年的物理获奖发现图表中，到40岁取得巨大成就的峰值概率是多少[即上图中蓝线的最高点]？”（答案：约0.8。）事实上，人们可以很容易地添加关于这个图表的几十个有趣的问题。我没有这样做，因为与这些问题相关的时间承诺。但我确实发现图表的宽泛形状令人着迷，了解图表的存在也很有用，如果我想要更多细节，可以在哪里查阅它。

我在上面说过，我通常花10到60分钟来打印一篇论文，持续时间取决于我对从论文中获得的价值的判断。然而，如果我学到了很多东西，并发现它很有趣，我会继续阅读和Ankifying。非常好的资源值得投入时间。但大多数论文都不符合这种模式，你很快就会饱和。如果您觉得您可以轻松地找到更有价值的东西来阅读，请切换。值得刻意练习这种开关，以避免在阅读中养成适得其反的完成主义习惯。几乎总是可以更深入地阅读论文，但这并不意味着你不能轻易在其他地方获得更多价值。花太长时间阅读不重要的论文是一种失败模式。

##### 使用Anki的Syntopic阅读

我谈到了如何使用Anki对论文进行浅薄的阅读，以及更深入的论文阅读。还有一种感觉，使用Anki不仅可以阅读论文，还可以“阅读”某些领域或子领域的整个研究文献。方法如下。

你可能会认为基础是大量论文的浅读。事实上，要真正抓住一个陌生的领域，你需要深入参与关键论文——像AlphaGo论文这样的论文。你从深入参与重要论文中得到的东西比任何单一的事实或技术都更重要：你了解该领域的强大结果是什么样子的。它可以帮助您吸收该领域最健康的规范和标准。它可以帮助你内化如何在现场提出好问题，以及如何将技术组合在一起。你开始明白是什么让像AlphaGo这样的东西成为突破——以及它的局限性，以及它确实是该领域的自然进化。这些事情不会被任何一个Anki问题单独捕捉到。但当人们深入地参与关键论文时，他们开始被人们提出的问题集体捕获。

因此，为了了解整个领域，我通常从一篇真正重要的论文开始，最好是一篇确立我首先对这个领域感兴趣的结果的论文。我按照我为AlphaGo描述的内容，彻底阅读了那篇论文。后来，我彻底阅读了该领域的其他关键论文——理想情况下，我阅读了该领域最好的5-10篇论文。但是，穿插着，我也对更多不太重要的（尽管仍然很好）的论文进行了浅读。到目前为止，在我的实验中，这意味着数十篇论文，尽管我预计在某些领域，我最终会以这种方式阅读数百甚至数千篇论文。

你可能想知道为什么我不只关注最重要的论文。部分原因是平凡的：很难说出最重要的论文是什么。浅薄地阅读许多论文可以帮助您弄清楚关键论文是什么，而无需花费太多时间深入阅读那些不那么重要的论文。但还有一种文化，人们可以阅读一个领域的面包和黄油论文：一种对日常进步是什么样子的感觉，对该领域实践的感觉。这也是有价值的，特别是对于建立该领域的总体情况，并激发我自己的问题。事实上，虽然我不建议你花很大一部分时间阅读坏论文，但与坏论文进行良好的对话当然是可能的。刺激是在意想不到的地方发现的。

随着时间的推移，这是Mortimer Adler和Charles van Doren称之为*合成阅读*的一种形式\*\*在他们奇妙的“如何阅读一本书”中：Mortimer J.Adler和Charles van Doren，《如何阅读一本书：智能阅读的经典指南》（1972）。我建立了对整个文献的理解：已经做了什么，什么还没有做。当然，它并不是真的阅读整本文学作品。但在功能上，它很接近。我开始识别悬而未决的问题，我个人希望回答的问题，但这些问题似乎还没有得到回答。我识别出一些技巧，这些观察似乎孕育了可能性，但我还不知道其重要性。而且，有时，我会识别出在我看来是全场的盲点。我也向Anki添加了关于所有这些的问题。通过这种方式，Anki是支持我创造性研究的媒介。作为这种媒介，它有一些缺点，因为它的设计没有考虑到支持创造性的工作——例如，它不适合在划痕空间内进行冗长的自由形式的探索。但即使没有以这种方式设计，它作为创造性的支持也是有帮助的。

我一直在描述我如何使用Anki来学习对我来说基本上是新的领域。相比之下，对于一个我已经非常了解的领域，我的好奇心和我对该领域的模型往往已经非常强烈，以至于很容易整合新的事实。我仍然觉得Anki很有用，但它在新领域绝对最有用。伟大的英国数学家John Edensor Littlewood在Béla Bollobás（1986）编辑的《Littlewood's miscellany》中写道\*\*：

> 我试图在我感兴趣的领域之外学习数学；经过任何间隔后，我不得不重新开始。

这抓住了我过去发现学习新领域所需的巨大情感努力。如果没有大量的驱动器，在新现场棒中制作大量材料是非常困难的。Anki为解决这个问题做了很多工作。从某种意义上说，这是一种情感假肢，实际上有助于创造我实现理解所需的动力。它不能完成整个工作——如前所述，有其他承诺（如创意项目或依赖我的人）来帮助创造这种动力是非常有帮助的。尽管如此，Anki帮助我给了我信心，我可以简单地*决定*我要深入阅读一个新领域，并保留和理解我学到的大部分内容。这适用于我尝试过的所有概念理解领域\*\*我很好奇它能很好地用于运动技能和解决问题，这两个领域我还没有尝试过使用Anki。

以这种方式阅读的一个令人惊讶的后果是它变得更加愉快。我一直喜欢阅读，但从一个具有挑战性的新领域开始有时是一件苦战，我经常被怀疑我是否能真正进入这个领域所困扰。反过来，这种怀疑降低了我成功的可能性。现在我有信心，我可以进入一个新的领域，并迅速获得良好的、相对深刻的理解，这种理解将是持久的。这种信心使阅读更加愉快\*\*许多人已经写了如何使用个人记忆系统阅读的说明。我的思想特别受到以下刺激：Piotr Wozniak，[增量阅读](https://www.supermemo.com/help/read.htm)。

##### Anki的更多使用模式

在研究了使用Anki阅读技术论文后，让我们回到一般使用模式\*\*另一个有用的模式列表是：Piotr Wozniak，[有效学习：制定知识的二十条规则](https://www.supermemo.com/en/articles/20rules)。本节中有很多东西，在第一次阅读时，您可能希望浏览并专注于那些最吸引您眼球的项目。

**使大多数Anki问题和答案尽可能原子：**也就是说，问题和答案都只表达一个想法。例如，当我学习Unix命令行时，我输入了一个问题：“如何从`linkname`到`filename`创建软链接？”答案是：“`ln -s filename
 linkname`”。不幸的是，我经常把这个问题弄错。

解决方案是通过将问题分解成两部分来重构问题。一块是：“创建Unix软链接的基本命令和选项是什么？”答案：“`ln -s …`。第二部分是：“在创建Unix软链接时，`linkname`和`filename`按什么顺序排列？”答案：“`filename
 linkname`”。

将这个问题分解成更多的原子碎片，将我经常出错的问题变成了我经常出错的两个问题\*\*一个更原子的版本是将第一个问题分解为“创建链接的Unix命令是什么？”和“`ln`命令创建软链接的选项是什么？”在实践中，我多年来一直知道`ln`是创建链接的命令，所以这没有必要。最重要的是：当我想在实践中创建一个Unix软链接时，我知道怎么做。

我不确定是什么导致了这种影响。我怀疑这部分与焦点有关。当我在组合问题上犯错误时，我经常有点模糊我的错误到底在哪里。这意味着我没有足够敏锐地关注这个错误，所以没有从我的失败中学到那么多。当我在原子问题上失败时，我的头脑确切地知道该集中在哪里。

总的来说，我发现你经常从将Anki的问题分解为更原子的问题中获得实质性的好处。这是一个强大的问题重构模式。

请注意，这并不意味着您不应该同时保留原始问题的某些版本。我仍然想知道如何在Unix中创建软链接，因此值得在Anki中保留原始问题。但它成为一个综合问题，从简单的原子事实到更复杂的想法构建的问题层次结构的一部分。

顺便说一句，仅仅因为一个问题是原子的，并不意味着它不能涉及非常复杂、高级的概念。考虑以下来自广义相对论领域的问题：“罗伯逊-沃克度量中的*dr2*项是什么？”答案：*dr2/(1-kr^2)*。现在，除非你研究过广义相对论，否则这个问题可能看起来相当不透明。这是一个复杂的、综合性的问题，假设你知道Robertson-Walker指标是什么，*dr2*是什么意思，*k*是什么意思，等等。但以这种背景知识为条件，这是一个相当原子的问题和答案。

以这种方式使用Anki的一个好处是，你开始习惯性地将事情分解成原子问题。这尖锐地明确了你学到的不同东西。就我个人而言，我发现这种结晶令人满意，原因（具有讽刺意味的是）我很难表达。但一个真正的好处是，后来我经常发现这些原子想法可以以我最初没有预料到的方式组合在一起。这很值得麻烦。

**Anki的使用最好被认为是一种精湛的技能，需要开发：**Anki是一个极其简单的程序：它允许您输入文本或其他媒体，然后根据您的响应决定的时间表向您展示该媒体。尽管如此简单，但它是一个令人难以置信的强大工具。而且，像许多工具一样，它需要技巧才能很好地使用。值得将Anki视为一种可以发展到精湛水平的技能，并尝试继续提升到这种精湛技艺。

**Anki不仅仅是一个记住简单事实的工具。这是一个几乎可以理解任何事情的工具。**一个常见的误解是，Anki只是为了记住简单的原始事实，如词汇项目和基本定义。但正如我们所看到的，可以使用Anki进行更高级的理解。我关于AlphaGo的问题从“Go板有多大？”等简单问题开始，最后是关于AlphaGo系统设计的高级概念问题——关于AlphaGo如何避免从训练数据中过度推广、卷积神经网络的局限性等主题。

培养Anki作为技艺技能的一部分是培养将其用于超越基本事实的理解的能力。事实上，我所做的（以及下面将要提出的关于如何使用Anki的许多观察）实际上都是关于理解某事的意义。将事情分解成原子事实。建立丰富的互联和集成问题层次结构。不要提出孤儿问题。如何参与阅读材料的模式。问题类型的模式（和反模式）。您想要记住的那种东西的模式。Anki技能具体地实例化了你如何理解的理论；发展这些技能将帮助你更好地理解。说成为大师级的Anki用户就是成为理解的大师级者，这太强烈了。但这有一些真相。

**使用一副大牌：**Anki允许您将卡片整理成一副牌和副牌牌。有些人用它来创建一个复杂的组织结构。我以前这样做过，但我逐渐\*\*这是渐进的，因为由于上下文的变化，问题有时需要重写。例如，我的Emacs和Unix命令行甲板都有非常相似的问题，比如：“如何删除一个单词？”这些问题需要重写，例如：“在Emacs中，如何删除一个单词？”（顺便说一句，对于像我这样的长期Emacs用户来说，这似乎是一个奇怪的问题。事实上，我用Anki来帮助我改变在Emacs中删除单词的方式，这就是为什么我有一个关于这个主题的Anki问题。我以这种方式对我的Emacs工作流程进行了许多改进。）将我的甲板和子甲板合并成一个大甲板。世界没有被整齐地分离成一个组成部分，我相信碰撞非常不同类型的问题是件好事。有那么一瞬间，Anki问我一个关于鸡肉应该煮的温度的问题。下一个：关于JavaScript API的问题。这种混合真的对我有好处吗？我不确定。到目前为止，我还没有找到任何理由使用JavaScript来控制鸡肉的烹饪。但我不认为这种混合会造成任何伤害，并希望它能创造性地刺激，并帮助我在不寻常的环境中应用我的知识。

**避免孤儿问题：**假设我在网上阅读，偶然发现了一篇关于阿尔巴尼亚巨型獴的梳理习惯的好文章，我以前从不知道我对这个主题感兴趣，但事实证明它很吸引人。很快，我就问了5到10个问题。这很好，但我的经验表明，几个月后，我可能会发现这些问题相当陈旧，并经常弄错。我认为原因是这些问题与我的其他兴趣脱节，我将失去让我感兴趣的背景。

我称这些*孤儿问题为孤儿问题*，因为它们与我记忆中的任何其他问题都没有密切关系。在Anki有一些孤儿问题也不错——很难知道什么会变成只是短暂的兴趣，什么会变成与我的其他兴趣相关的实质性兴趣。但是，如果你的少数问题是孤儿，这是一个迹象，你应该更多地专注于与你的主要创意项目相关的Ankifying问题，并减少Ankifying切线材料。

特别值得避免孤独的孤儿：一个在很大程度上与其他一切都脱节的问题。例如，假设我正在阅读一篇关于新主题的文章，我学到了一个似乎特别有用的想法。我规定永远不要提出一个问题。相反，我试着至少提出两个问题，最好是三个或更多。这通常足够了，它至少是一些有用知识的核心。如果这是一个孤独的孤儿，我不可避免地总是把问题弄错，进入它根本就是一种浪费。

**不要分享甲板：**我经常被问到我是否愿意分享我的Anki甲板。我不是。很早的时候，我意识到把个人信息放在Anki里会非常有用。我的意思不是任何非常私人的东西——我永远不会把深刻、黑暗的秘密放进去。我也没有放任何需要安全的东西，比如密码。但我确实放了一些我不会随便说的东西。

例如，我有一个（很短！）表面上迷人和令人印象深刻的同事名单，我永远不会和他们一起工作，因为我一直看到他们对待别人不好。Ankify治疗的一些细节是有帮助的，所以我可以清楚地记住为什么应该避开那个人。这不是随意传播的正确信息：我可能误解了对方的行为，或者误解了他们操作的背景。但在Anki，这对我个人来说很有用。

**构建您自己的甲板：**Anki网站有许多[共享甲板](https://ankiweb.net/shared/decks/)，但我发现它们只有一点用处。最重要的原因是，制作Anki卡片本身就是一种理解行为。也就是说，找出好问题和好的答案，是很好地理解新主题的一部分。使用别人的卡片就是放弃大部分理解。

事实上，我相信构建卡片的行为实际上有助于内存。记忆研究人员一再发现，你越详细地编码记忆，记忆就越强。通过精心编码，它们本质上意味着你形成的协会的丰富性。

例如，可以试着作为一个孤立的事实来记住，1962年是第一颗通信卫星Telstar被送入轨道的一年。但记住它的更好方法是将这一事实与他人联系起来。相对平淡无言地，你可能会观察到Telstar是在苏联第一颗卫星Sputnik发射仅5年后发射的。没过多久就为电信腾出空间。不那么平淡无奇——更丰富的阐述——我个人觉得Telstar在引入ASCII*的前*一年被送入轨道，ASCII可以说是第一个用于通信文本的现代数字标准。在我们拥有通信文本的数字标准之前，人类就有了一颗电信卫星！找到这种连接是精心编码的一个例子。

构建Anki卡的行为本身几乎总是一种精心编码的形式。它迫使你思考问题的替代形式，考虑最佳答案，等等。我相信即使是最基本的卡片也是如此。如果你构建更复杂的卡片，将基本事实与其他想法联系起来的卡片（如Telstar-ASCII链接），逐渐建立一个由丰富的相互关联的想法组成的网络，这当然会成为事实。

话虽如此，有一些有价值的甲板共享做法。例如，有一些医科学生社区认为共享和有时合作建造甲板很有价值\*\*请参阅[MedicalSchoolAnki subreddit](https://www.reddit.com/r/medicalschoolanki/)，其中包含对最佳甲板的频繁讨论，如何使用它们，以及用于不同目的的不断变化的最佳甲板。另见论文：Michael Hart-Matyas*等人*，[医科学生建立协作抽认卡项目的十二个技巧](http://augmentingcognition.com/assets/Hart-Matyas2018.pdf)，医学教师（2018年）。我还发现，包含非常基本问题的共享甲板有价值，例如询问谁画了一幅特定画等问题[的艺术甲板](https://ankiweb.net/shared/info/685421036)。但为了更深入地理解，我还没有找到使用共享甲板的好方法。

**培养精心编码/形成丰富协会的策略：**这实际上是一个元策略，即形成策略的策略。一个简单的示例策略是*使用“相同”问题的多个变体*。例如，我之前提到了我的两个问题：“琼斯2011年声称物理学诺贝尔奖获得者在1980-2011年做出获奖发现的平均年龄是多少？”还有：“哪篇论文声称物理学诺贝尔奖获得者在1980年至2011年期间平均48岁时做出了他们的获奖发现？”从逻辑上讲，这两个问题显然密切相关。但就记忆的工作原理而言，它们是不同的，导致在非常不同的触发器上产生关联。

**记忆宫殿和类似的技术呢？**有一套著名的记忆技术，基于记忆宫殿、轨迹方法等想法\*\*一个有趣和信息丰富的概述是：Joshua Foer，《与爱因斯坦一起月球漫步》（2011年）。这是一种极端的精心编码形式，与您想要记住的材料进行丰富的视觉和空间关联。以下是Joshua Foer讲述了一段对话，其中助记符Ed Cooke描述了一种基本技巧：

> 然后，Ed向我解释了他让名字难忘的程序，他在比赛中用它来记住与名字和面孔活动中的99个不同照片头像相关联的名字和姓氏。这是他承诺我可以用来记住人们在派对和会议上的名字的一种技术。“这个把戏实际上看似简单，”他说。“总是将一个人的名字的声音与你能清晰想象的东西联系起来。这都是关于在你的脑海中创造一个生动的图像，将你对人脸的视觉记忆固定在与人的名字相关的视觉记忆上。当你需要回头记住这个人的名字时，你创建的图像只会突然出现在你的脑海中......所以，嗯，你说你的名字是Josh Foer，嗯？”他挑了挑眉毛，下巴戏剧性地抚摸了一下。“嗯，我会想象你在我们第一次见面的地方，在比赛大厅外和我开玩笑，我会想象自己分成四块作为回应。Four/Foer，明白了吗？至少对我来说，那个小图像比你的名字更有趣，应该很好地留在脑海中。”

我尝试过这些技术，虽然它们很有趣，但它们似乎对记忆琐事最有用——扑克牌序列、数字字符串等。对于更抽象的概念来说，它们似乎不太发达，这种抽象通常是最深刻理解的地方。从这个意义上说，他们甚至可能会分散对理解的注意力。也就是说，可能我只是需要找到更好的方法来使用这些想法，就像我需要弄清楚Anki一样。特别是，可能值得进一步研究从业者用于形成丰富协会的一些技术。正如Foer引用记忆专家的话所说，学习“以更令人难忘的方式思考”是很有价值的。

**Anki95%的价值来自5%的功能：**Anki有自动生成卡、标记卡、插件生态系统等方式。在实践中，我很少使用这些功能。我的卡片总是两种类型之一：大多数是简单的问答；大量的少数是所谓的*cloze*：一种填空测试。例如，我将使用clozes来测试自己最喜欢的引号：

> “如果个人电脑真的是\_\_，那么它的使用实际上会改变\_\_的\_\_”，\_\_，\_\_”（答案：新媒介，思维模式，整个文明，Alan Kay，1989年）。

Clozes也可以用来提出不涉及引号的问题：

> 阿德尔森错觉也被称为\_\_\_错觉。（答案：格子阴影）

为什么不使用更多Anki的功能呢？部分原因是，我只从核心功能中得到了巨大的好处。此外，学习很好地使用这组小功能需要做很多工作。篮球和篮球是简单的设备，但你可以花一生的时间来学习很好地使用它们。同样，基本的Anki实践可以极大地发展。因此，我专注于学习很好地使用这些基本功能。

我认识很多人，他们尝试了Anki，然后去兔子洞学习尽可能多的功能，这样他们就可以“高效”地使用它。通常，他们追求1%的改进。通常，这些人最终会放弃Anki，认为它“太难了”，这通常是“我紧张了，我没有完美地使用它”的同义词。这太可惜了。如前所述，Anki比（比如）普通抽认卡提高了20倍。因此，他们放弃了2000%的改进，因为他们担心他们错过了一些最后的5%、1%和（在许多情况下）0.1%的改进。这种兔子洞似乎对程序员特别有吸引力。

因此，当有人开始时，我建议不要使用任何高级功能，不要安装任何插件。简而言之，不要患上程序员效率疾病的坏病例。学习如何使用Anki进行基本问答，并专注于探索该范式中的新模式。这比花在功能上摆弄这些功能的任何小时都更能为你服务。然后，如果您养成定期使用高质量Anki的习惯，您可以尝试更高级的功能。

**使用Anki存储有关朋友和家人的事实的挑战：**我尝试过使用Anki存储（不敏感！）关于朋友和家人的问题。它很适合诸如“[我的朋友]是素食主义者吗？”之类的东西。但我的用法在更棘手的问题上有点搁浅了。例如，假设我和一个新朋友谈论他们的孩子，但从未见过这些孩子。我可以提出这样的问题：“[我朋友的]长子叫什么名字？”或者，如果我们谈论音乐，我可能会说：“音乐家[我的朋友]喜欢什么？”

这种实验是善意的。但提出这样的问题经常让我感到不舒服。这似乎太像在假装对我的朋友感兴趣。有一个很强的社会规范，如果你记得你的朋友对音乐的品味或他们孩子的名字，那是因为你对那个朋友感兴趣。使用记忆辅助工具在某种程度上感觉不真实，至少对我来说是这样。

我和几个朋友谈过这件事。大多数人都告诉我同样的事情：他们一开始就感激我遇到了这么多麻烦，并觉得这很迷人，我会非常担心它是否不真实。所以也许担心是错误的。尽管如此，我仍然对此有困难。我采用Anki是为了不那么个人化的东西——比如人们的食物偏好。也许随着时间的推移，我会用它来存储更多的个人事实。但现在我正在慢慢来。

**程序性记忆与陈述性记忆：**记住事实和掌握过程之间有很大的区别。例如，虽然在Anki问题提示时，您可能记得Unix命令，但这并不意味着您将认识到在命令行上下文中使用该命令的机会，并自如地将其键入出来。找到新颖、创造性的方法来组合您所知道的命令，以解决具有挑战性的问题，这仍然是另一回事。

换句话说：要真正内化一个过程，仅仅审查Anki卡片是不够的。你需要在上下文中执行这个过程。你需要用它解决真正的问题。

话虽如此，我发现转学过程相对容易。就命令行而言，我经常使用它，以至于我有很多机会真正利用我对命令行的Ankified知识。随着时间的推移，这种陈述性知识正在成为我在上下文中经常使用的程序性知识。也就是说，最好更好地了解转移何时有效，何时无效。更好的是，一个集成到我实际工作环境中的内存系统。例如，它可以在Unix命令上查询我，同时将我放在实际的命令行。或者，也许它会要求我在命令行时解决更高层次的问题。

我在这方面尝试了一个实验：在我查看Anki卡时模仿键入命令的动作。但我的主观印象是，它效果不太好，而且这样做也很烦人。所以我停了下来。

**超越“名字不重要”：**我是一名受过训练的理论物理学家。理查德·费曼在物理学中讲述了一个著名的故事，他驳斥了知道事物名称的价值。小时候，费曼和一个无所不知道的孩子在田野里玩耍。以下是费曼讲述\*\*理查德·P时发生的事情。费曼：“你在乎别人怎么想？《好奇角色的进一步冒险》（1989）。：

> 一个孩子对我说：“看到那只鸟了吗？那是一只什么样的鸟？”  
>   
> 我说：“我一点也不知道它是什么样的鸟。”  
>   
> 他说：“这是一种棕色喉咙的鹅口疮。你父亲没有教你任何东西！”  
>   
> 但情况恰恰相反。他[费曼的父亲]已经教过我：“看到那只鸟了吗？”他说。“这是斯宾塞的莺。”（我知道他不知道真名。）“嗯，用意大利语来说，它是*Chutto Lapittida*。在葡萄牙语中，它是*Bom daPeida......*你可以用世界上所有语言知道那只鸟的名字，但当你完成后，你对这只鸟一无所知！你只会知道不同地方的人类，以及他们所谓的鸟。因此，让我们看看这只鸟，看看它*在做什么*——这才是最重要的。”（我很早就学会了知道某物的名字和知道某物之间的区别。）

费曼（或他的父亲）继续对真实知识进行了深思熟虑的讨论：观察行为，了解其原因，等等。

这是个好故事。但它走得太远了：名字确实很重要。也许没有那个无所不知道的孩子想象的那么多，而且他们通常不是一种深厚的知识。但它们是让你建立知识网络的基础。

在我的科学训练中，这个名字无关紧要的比喻被反复钻进我身上。当我开始使用Anki时，起初我觉得在系统中输入有关事物名称的问题有点愚蠢。但现在我热情地这样做，因为我知道这是理解道路上的第一步。

Anki对各种事物的名称都很有用，但我发现它对非语言事物特别有帮助。例如，我提出了关于艺术品的问题，比如：“艺术家[Emily Hare的](https://www.emilyhare.co.uk/)画作*Howl*是什么样子的？”答案：

我提出这个问题有两个原因。主要原因是我喜欢时不时地记住这幅画的经历。另一个是为这幅画起个名字\*\*实际上，一个更好的问题是展示这幅画，并询问它的名字是什么。如果我想对这幅画进行更分析性的思考——比如说，关于颜色渐变的巧妙使用——我可以添加更详细的问题。但我很高兴只是把图像的体验记在记忆中。

**当你落后时，你会怎么做？**当你用卡片落后时，Anki就变得具有挑战性。如果你跳过一两天——或五十天——卡片就会开始备份。回来发现你一天有500张卡片要查看，这很吓人。更糟糕的是，如果你摆脱了Anki的习惯，你可以远远落后。我基本上停止使用Anki7个月，并回到了数千张积压的卡片。

幸运的是，赶上并不难。我给自己设定了每天逐渐增加卡片的配额（100、150、200、250，最终是300），并每天完成这些配额，持续数周，直到我赶上。

虽然这不太困难，但有点令人沮丧和沮丧。如果Anki有一个“追赶”功能，将多余的卡片分散到您日程安排的几周内，那就更好了。但它没有。无论如何，这是一个明白了，但解决这个问题并不难。

**将Anki用于API、书籍、视频、研讨会、对话、网络、活动和地点：**我之前所说的关于Ankifying论文的几乎所有内容也适用于其他资源。这里有一些提示。我已经将API的讨论分成了一个附录，如果有兴趣，您可以在[下面阅读](http://augmentingcognition.com/ltm.html#Anki_APIs)。

对于研讨会和与同事的对话，我发现设置Anki配额非常有帮助。例如，对于研讨会，我试图找到至少三个高质量的问题来回答Ankify。对于扩展对话，至少要向Ankify提出一个高质量的问题。我发现设置配额有助于我更加关注，特别是在研讨会期间。（我发现在一对一的谈话中*先验*地关注要容易得多。）

我对视频、事件和地点更随意。比如说，在郊游或去新餐厅后，系统地回答3-5个问题，以帮助我记住这段经历。我有时会这样做。但我没有那么系统。

当我阅读论文和书籍时，我倾向于实时Ankify。研讨会、对话等，我更喜欢沉浸在体验中。与其离开Anki，我会很快在脑海中（或纸上）记下我想Ankify的东西。然后我稍后把它输入Anki。这需要一些纪律；这是我更喜欢设定小配额的原因之一，所以我只需要稍后输入几个问题，而不是几十个问题。

书籍需要注意：阅读整本书是一项重大承诺，定期添加Anki问题会大大减慢你的速度。在决定如何安基化时，值得记住这一点。有时，一本书是如此密集，材料丰富，值得花时间添加很多问题。但漫不经心地忘记眼前的一切是一个坏习惯，我偶尔会陷入这个习惯。

你Ankify的东西不是一个微不足道的选择：Ankify服务于你长期目标的东西。在某种程度上，我们成为我们成为成员，所以我们必须小心我们记住的东西\*\*向Kurt Vonnegut道歉，他写道：“我们是我们假装的样子，所以我们必须小心我们假装的样子。”这总是正确的，但Anki让它变得特别真实。

话虽如此，一个有趣的模式是回到我旧的、Anki前在书上的笔记，并把它们Ankify。这通常可以很快完成，并让我在现在大部分被遗忘的书籍上投入的时间获得更大的回报\*\*朋友有时抱怨许多书籍都是过度填充的文章。也许这种填充的一个好处是，它强制执行Anki般的间隔重复，因为读者需要几周的时间来阅读这本书。这可能是一种低效的方式来记住要点，但总比对这本书完全没有记忆要好。

我还没有弄清楚的是，如何将Anki与笔记结合起来，用于我的创意项目。我不能用Anki代替记笔记——它太慢了，而且对很多事情来说，我的长期记忆力使用很差。另一方面，将Anki用于重要项目有很多好处——流畅地获取记忆是许多创造性思维的基础。我认为，联想思维的速度在创造性工作中很重要。——John Littlewood在实践中，我发现自己本能地和非系统地做一些事情作为笔记，其他事情作为Anki的问题，还有其他事情兼而有之。总的来说，它工作正常，但我的感觉是，如果我应用更系统的思想和实验，可能会好得多。部分问题是，我没有一个很好的笔记系统，句号！如果我为此做更多的努力，我怀疑整个事情会变得更好。尽管如此，它仍然工作正常。

**避免是/否模式：**我有时会养成的一个坏习惯，那就是有很多Anki问题，答案为是/否。例如，在学习机器学习中的图形模型时，我添加了一个不太好的问题：

> 对于大多数图形模型来说，计算分区功能是否棘手？

答案是“是”。就目前而言，这很好。但阐述这个问题中的想法将有助于我的理解。我可以添加一个关于分区功能对哪些图形模型是可处理的问题吗？我可以举一个分区功能难以解决的图形模型的例子吗？无论如何，计算分区函数是棘手的意味着什么？是/否问题至少应该被视为问题重构的候选者\*\*通过与代码气味的类比，我们可以谈论“问题气味”，以暗示可能需要重构。是/否结构是问题气味的一个例子。

**外部记忆辅助工具还不够吗？**对Anki等系统的一个常见批评是，外部存储设备——谷歌、维基和笔记本等系统——真的应该足够了。很好地使用，当然，这些系统作为Anki的补充非常有用。但对于创造性的工作和解决问题来说，内在的理解有一些特别之处。它使联想思维的速度变快，能够快速尝试许多想法的组合，并直觉模式，如果你需要继续费力地查找信息，这是不可能的。

流利在思考中很重要。艾伦·凯和阿黛尔·戈德堡提出了\*\*艾伦·凯和阿黛尔·戈德堡，[个人动态媒体](http://augmentingcognition.com/assets/Kay1977.pdf)（1977）。长笛的思想实验，其中“吹音符和听到音符之间有一秒钟的延迟！”正如他们所观察到的，这是“荒谬的”。同样，当考虑到所有相关的理解时，某些类型的想法更容易拥有。对于这一点，Anki是无价之宝。

**如果个人内存系统如此出色，为什么它们没有被更广泛地使用呢？**这个问题类似于一个老笑话，说两位经济学家正在走，其中一个发现一张20美元的钞票。他们说：“看！地上有20美元！”其他回答是：“不可能！如果它真的在那里，有人早就把它捡起来了。”

这个类比只是部分的。事实上，Anki似乎持续供应20美元的钞票躺在地上。询问为什么它没有被更广泛地使用是有道理的。相关研究文献中引用最多的论文之一\*\* Frank N.Dempster，《[间距效应：未能应用心理研究结果的案例研究](http://augmentingcognition.com/assets/Dempster1988.pdf)》（1988年）。是为什么这些想法没有更广泛地用于教育的讨论。虽然写于1988年，但论文中的许多观点至今仍然正确。

我个人怀疑有三个主要因素：

* 在对记忆的实验研究中，人们一直低估了以类似于Anki的方式分发研究所带来的收益。相反，他们更喜欢最后一分钟的填鸭式学习，并相信它能产生更好的结果，尽管许多研究表明它没有。
* 心理学家Robert Bjork建议\*\*Robert A。Bjork, [Memory and MetamemoryConsiderations in the Training of Human Beings](http://augmentingcognition.com/assets/Bjork1994.pdf)（1994）。“理想难度原则”，即当我们处于遗忘边缘时，如果测试记忆，记忆会得到最大程度的加强。这表明一个高效的内存系统本质上将有些难以使用。人类与困难的活动有着复杂的关系，通常不喜欢进行这些活动，除非有强烈的动机（在这种情况下，它们可能会变得令人愉快）。
* 像Anki这样的系统使用起来很有挑战性，而且使用起来也不好。

考虑开发可能克服部分或所有这些问题的系统很有趣。

#### 第二部分：更广泛的个人记忆系统

在这篇文章的第一部分，我们通过我个人经历的视角审视了一个特定的个人记忆系统，Anki。在本文的第二部分中，我们将考虑关于个人记忆系统的两个更广泛的问题：记忆作为一种认知技能有多重要；以及认知科学在构建个人记忆系统中的作用是什么？

##### 无论如何，长期记忆有多重要？

长期记忆有时会被贬低。人们诋毁“写记忆”是很常见的，特别是在课堂上。我从许多人那里听说，他们放弃了一些课程——有机化学很常见——因为它“只是一堆事实，我想要一些涉及更多理解的东西”。

我不会为糟糕的课堂教学或经常教授有机化学的方式辩护。但低估记忆的重要性是错误的。我曾经相信这种关于记忆重要性低下的比喻。但我现在相信记忆是我们认知的基础。

这种变化有两个主要原因，一个是个人经验，另一个是基于认知科学的证据。

让我从个人经历开始。

多年来，我经常帮助人们学习量子力学等技术科目。随着时间的推移，你会看到人们陷入困境的模式。一个常见的模式是，人们认为他们被困在深奥的复杂问题上。但当你深入挖掘时，事实证明他们在基本符号和术语方面遇到了困难。当你不清楚每三个单词或符号时，就很难理解量子力学！每句话都是一场斗争。

就像他们试图用法语创作一部美丽的十四行诗，但只知道200个法语单词。他们很沮丧，认为麻烦在于难以找到一个好的主题、引人注目的情感和图像等等。但真正的问题是，他们只有200个单词可以作曲。

我有点虔诚的信念是，如果人们更专注于记住基础知识，而少担心“困难”的高层次问题，他们就会发现高层次问题自己解决了。

但是，虽然我把这个看作是对其他人的强烈信念，但我从未意识到它也适用于我。我不知道我有多强烈地适用于我。用Anki在新领域阅读论文让我无法理解这种错觉。我发现Anki让学习这些科目变得容易得多，这几乎令人不安。我现在认为，对基础知识的记忆往往是理解的最大障碍。如果你有一个像Anki这样的系统来克服这个障碍，那么你会发现它更容易进入新领域。

Anki让学习新技术领域变得多么容易的经历极大地增加了我对记忆重要性的内在理解。

认知科学也对记忆在认知中的关键作用有很多结果。

研究人员Adriaan de Groot和Herbert Simon（单独）做了一条引人注目的工作，研究人们如何获得专业知识，特别关注国际象棋\*\*例如，参见Herbert A。Simon, [How Big is a Chunk?](http://augmentingcognition.com/assets/Simon1974.pdf), Science (1974) and Adriaan de Groot,*Thought and Choice in Chess*, Amsterdam University Press (2008, reprinted from 1965)..他们发现，世界一流的国际象棋专家对棋盘的看法与初学者不同。初学者会看到“这里的棋子，那里的车”，等等，一系列单独的棋子。相比之下，大师们看到了更复杂的“块”：他们认可的作品组合，并且能够以比单个作品更高的抽象水平进行推理。

Simon估计，国际象棋大师在训练期间学习了25,000到100,000个这样的棋子，学习这些棋子是成为一流棋手的关键因素。这些玩家对国际象棋位置的看法确实与初学者截然不同。

为什么学习识别和推理这些块对发展专业知识有如此大帮助？这是一个推测性的非正式模型——据我所知，它还没有被认知科学家验证，所以不要太认真对待它。我将在数学的背景下描述它，而不是国际象棋，因为数学是我与各种能力的人交谈的经验领域，从初学者到有成就的专业数学家。

许多人的有成就的数学家的模型是，他们聪明得惊人，智商非常高，并且有能力处理头脑中非常复杂的想法。一个普遍的看法是，他们的聪明给了他们处理非常复杂的想法的能力。基本上，他们有一台更高马力的发动机。

诚然，顶尖的数学家通常非常聪明。但这里对正在发生的事情有不同的解释。根据Simon的说法，许多顶级数学家通过努力工作，内化了许多比普通人更复杂的数学块。这意味着，对我们其他人来说，非常复杂的数学情况对他们来说似乎非常简单。因此，并不是说他们拥有更高的马力思维，即能够处理更多的复杂性。相反，他们之前的学习给了他们更好的分块能力，因此大多数人认为情况很复杂，他们认为情况很简单，而且他们发现更容易推理。

现在，西蒙在研究国际象棋者时使用的大块概念实际上来自乔治·米勒1956年的一篇著名论文《神奇的数字七，加或减二》\*\*乔治·A。米勒，[神奇的数字七，加或减二：我们处理信息能力的一些限制](http://psychclassics.yorku.ca/Miller/)（1956年）。Miller认为，工作记忆的能力大约是七块。事实上，事实证明，这个数字因人而异，个人的工作记忆能力与其一般智力能力（IQ）之间存在实质性的相关性\*\*在Phillip L中可以找到相关性的回顾。阿克曼，玛格丽特·E。Beier和Mary O. Boyle，[工作记忆和智力：相同还是不同的构造？](http://augmentingcognition.com/assets/Ackerman2006.pdf)心理公报（2006）..通常，你的工作记忆力越好，你的智商就越高，反之亦然。

米勒所说的大块的意思，他留下了一些模糊的东西，写道：

> 术语bit和 chunk的对比也凸显了一个事实，即我们对什么是大块信息不是很确定。例如，Hayes获得的五个单词的记忆跨度......可能同样被称为15个音素的记忆跨度，因为每个单词中大约有三个音素。直觉上，很明显，受试者回忆了五个单词，而不是15个音素，但逻辑上的区别并不明显。我们在这里处理的是将输入组织或分组到熟悉的单元或块的过程，这些熟悉的单元的形成已经进行了大量学习。

换句话说，在Miller的叙述中，这个块实际上是工作记忆*的基本单位*。因此，西蒙和他的合作者正在研究国际象棋棋手工作记忆中使用的基本单位。如果这些块更复杂，那么这意味着玩家的工作记忆具有更高的有效容量。特别是，智商较低但能够调用更复杂的块的人将能够比智商高但不太复杂的内在块的人对更复杂的情况进行推理。

换句话说，在某个领域记住更多的大块有点像在该领域有效提高一个人的智商。

好吧，这是一个投机性的非正式模型。无论它是否正确，似乎将高级数据内部化确实是获得专业知识的关键部分。然而，这并不一定意味着使用Anki等系统将加快此类块的获取速度。这只是一个论点，即长期记忆在获得某些类型的专业知识方面发挥着至关重要的作用。尽管如此，似乎有道理的是，经常使用Anki等系统可能会加快专家使用的高级块的获取速度\*\*为了确定这一点，这将有助于确切了解这些块是如何产生的。这似乎仍然不为人知。如果它除了长期记忆外，还涉及大量的分析和解决问题，我不会感到惊讶。然后，这些块是有效认知的核心，包括我们理解、解决问题和创造的能力。

##### 分布式实践

为什么Anki有效？在本节中，我们简要介绍了认知科学的关键基本思想之一，即*分布式实践*。

假设你在派对上被介绍给某人，他们告诉你他们的名字。如果你注意了，而且他们的名字不太不寻常，你几乎肯定会在20秒后记住他们的名字。但你更有可能在一小时内忘记他们的名字，更有可能在一个月内忘记他们的名字。

也就是说，记忆会腐烂。这不是新闻！但伟大的德国心理学家赫尔曼·埃宾豪斯（Hermann Ebbinghaus）有一个好主意，可以系统地、定量地研究记忆衰减\*\*赫尔曼·埃宾豪斯，《[记忆：对实验心理学的贡献](http://psychclassics.yorku.ca/Ebbinghaus/index.htm)》（1885）。最近对Ebbinghaus结果的复制可以在以下位置找到：Jaap M。J.Murre和Joeri Dros，[Ebbinghaus遗忘曲线的复制和分析](http://journals.plos.org/plosone/article?id=10.1371/journal.pone.0120644)（2015）。特别是，他对记忆衰减的速度以及导致衰减的原因很感兴趣。为了研究这一点，Ebbinghaus记住了无意义的音节字符串——如“fim”和“pes”——后来测试了自己，记录了他在不同时间间隔后保留了这些音节的程度。

Ebbinghaus发现，正确回忆一个项目的概率（大致）随着时间的推移而呈指数下降。今天，这被称为*Ebbinghaus遗忘曲线*：

是什么决定了曲线的陡度，即记忆衰减的速度有多快？事实上，陡峭程度取决于许多事情。例如，对于更复杂或不太熟悉的概念，它可能更陡峭。你可能会发现更容易记住一个听起来与你以前听过的名字相似的名字：比如Richard Hamilton，而不是Suzuki Harunobu。所以他们会有一个更浅的曲线。同样，你可能会发现记住视觉的东西比记住口头的东西更容易。或者一些口头的东西，而不是运动技能。如果你使用更复杂的记忆方法——例如助记符，或者只是小心地将一个想法与你已经知道的其他事情联系起来——你也许能够将曲线拉平\*\*尽管这种扩展得到了大量研究，但构建详细的扩展预测模型的工作却出人意料地少。一个例外是：Burr Settles和Brendan Meeder，[语言学习的可训练间隔重复模型（](http://augmentingcognition.com/assets/Settles2016.pdf)2016）。本文构建了一个回归模型，以预测在线语言学习平台Duolingo上学生记忆的衰减率。结果不仅更好地预测了衰减率，还提高了Duolingo学生的参与度。

假设你在派对上被介绍给一个人，然后20分钟内不要想他们的名字。但随后你需要把它们介绍给别人，所以需要记住它。在那之后，你的召回概率将再次非常高。Ebbinghaus的研究表明，重新测试后概率将呈指数衰变，但衰变速度将比最初慢。事实上，随后的重新测试将进一步减缓衰减，随着内存通过多个召回事件进行整合，衰减曲线逐渐变平：

这种衰变时间的逐渐增加是Anki和类似内存系统设计的基础。这就是为什么Anki逐渐扩大了测试之间的时间。

这些现象是科学家广泛研究的更广泛的想法的一部分。这组现象有几个相关术语，但我们将使用“分布式实践”一词，意思是在时间上分布式的实践，最好是以最大限度地促进保留的方式。这与填鸭式练习形成鲜明对比，通常被称为集体练习，人们试图将所有学习都纳入一个课程中，依靠重复。

##### 关于认知科学在增强认知的系统设计中的作用

自Ebbinghaus以来，对分布式实践的不同变化进行了数千项研究。这些研究教会了我们很多关于长期记忆的行为。最重要的是，他们强调分布式实践优于大规模实践\*\*许多实验还试图评估参与者对大规模实践与分布式实践有效性的看法。值得注意的是，他们经常认为大规模实践更有效，尽管分布式实践的表现可靠地优于分布式实践。很想跳进那本文献，并将其用作内存系统设计指南\*\*与其做这样的评论，不如让我指出几篇作为有用切入点的评论。Benedict Carey的书《我们如何学习》（2015年）是一个很好的流行介绍。对分布式实践文献的有用评论包括：Cepeda*等人*，[口头回忆任务中的分布式实践：审查和定量合成](http://augmentingcognition.com/assets/Cepeda2006.pdf)（2006年）；以及：Gwern Branwen，[间隔重复](https://www.gwern.net/Spaced-repetition)。但作为系统发展的指南，也值得思考该文献的局限性。

虽然科学家对分布式实践进行了大量研究，但关于分布式实践的许多基本问题仍然知之甚少。

我们不详细理解为什么内存会发生指数衰减，或者当该模型崩溃时。我们没有好的模型来说明什么决定了衰变率，以及为什么它因不同类型的记忆而异。我们不明白为什么在随后的召回后，衰变需要更长的时间。我们对扩大学习间间隔的最佳方式知之甚少。

当然，有许多部分理论可以回答这些问题和其他基本问题。但是没有单一的、定量预测的、被广泛接受的一般理论。因此，从这个意义上说，我们对分布式实践知之甚少，可能距离合理充分理解还有几十年（如果不是更多的话）。

为了具体说明这一点，让我只举一个例子：有时我们的记忆不会衰减，但会随着时间的推移而变得更好，即使我们没有意识到明确的回忆行为。非正式地，你可能在自己生活中注意到了这一点。心理学家威廉·詹姆斯做了一个开玩笑的观察，他将其归因于一位不知名的德国作家，即\*\*威廉·詹姆斯，《心理学原理》（1890年）。

> 我们冬天学习游泳，夏天学习滑冰。

事实上，1895年对Axel Oehrn\*\* Axel Oehrn，Experimentelle Studien zur Individualpsychologie（1895）的一项研究实验验证了这种效果。虽然随后的实验证实了这一结果，但它敏感地取决于被记忆的材料类型、确切的时间间隔和许多其他变量。现在，从某种意义上说，这与Ebbinghaus指数遗忘曲线相矛盾。在实践中，一个相当好的启发式是Ebbinghaus曲线近似保持，但也有例外，通常在有限的时间内，对于非常特定的材料类型。

我提到这一点不是为了破坏你对Ebbinghaus模型的信念。但作为一个警告：记忆是复杂的，我们不太理解许多大局问题，在对任何给定模型过于信任之前，我们应该小心。

话虽如此：分布式实践和Ebbinghaus遗忘曲线的基本效应是真实、巨大的，并已得到许多实验的证实。相比之下，像Oehrn发现的那样的效果就不那么重要了。

这让我们处于一个奇怪的境地：我们对记忆有足够的理解，可以得出结论，像Anki这样的系统应该能帮上大忙。但是，在设计这样一个系统时，需要的许多选择必须以*临时*的方式做出，以直觉和未经证实的假设为指导。科学文献中的实验还*不能*证明这些设计选择是合理的。原因是这些实验大多不是为了解决这些问题。他们将专注于要记忆的特定类型的信息。或者他们会专注于相对较短的时间——在一天或一周内记忆，而不是几年。这项工作有助于我们建立更好的记忆理论，但它不一定能回答设计师构建系统所需的问题。

因此，系统设计者必须寻找其他地方，寻找非正式的实验和理论。例如，Anki使用Piotr Wozniak在个人实验基础上开发的紧致算法\*\*见：Piotr Wozniak，[SuperMemo 2002到SuperMemo2006中使用的重复间距算法](https://www.supermemo.com/english/algsm11.htm)。Anki使用算法SM-2..虽然Wozniak发表了[许多论文](https://www.supermemo.com/english/publicat.htm)，但它们是非正式的报告，没有遵守传统认知科学文献的规范。

从某种意义上说，这并不令人满意：我们对使用什么间距时间表不是很了解。但系统必须使用一些时间表，因此设计师会尽力而为。这似乎比天真的方法效果要好得多，但从长远来看，有一个基于详细人类记忆理论的方法会很好。

现在，对此的一个回应是说，你应该进行科学设计，并为所有设计选择提供良好的实验证据。我听说这被用作对Anki等系统设计师的批评，他们做了太多的*临时*猜测，而不是以系统的科学理解为后盾。

但他们应该怎么做？等50或100年，直到这些答案出来？放弃设计，成为未来30年的记忆科学家，这样他们就可以为系统设计中需要回答的所有问题提供适当的“科学”答案？

这不是设计的工作方式，也不是它应该工作的方式。

如果设计师等到所有证据都出来，没有人会设计任何东西。在实践中，你想要的是大胆、富有想象力的设计，探索许多想法，但受到科学知识的启发和知情（而不是太受制于）。理想情况下，除此之外，还会有一个更慢的反馈循环，设计选择将提出有关记忆的问题，这将导致新的科学实验，从而提高对记忆的理解，这反过来又会为设计提供新的途径。

这种平衡并不容易实现。人机交互（HCI）社区试图在他们构建的系统中实现它，不仅是为了记忆，也是为了增强人类的认知。但我认为它效果不太好。在我看来，他们在设计中放弃了很多大胆、想象力和愿望\*\*作为一个局外人，我知道这个评论不会让我在HCI社区中成为任何朋友。另一方面，我认为保持沉默也没有任何好处。当我看到社区内的重大事件时，如CHI会议，与早期的增强工作相比，绝大多数论文似乎都很胆小。这说明，发布传统的静态论文（pdf，甚至不是交互式JavaScript和HTML）仍然是该领域的核心。与此同时，他们也没有做成熟的认知科学——他们没有对大脑进行详细的理解。找到想象力设计和认知科学之间的正确关系是增强工作的核心问题，这并不是微不足道的。

同样，想象认知科学家开始构建系统是很容易的。虽然这有时可能有效，但我认为在大多数情况下不太可能产生好的结果。构建有效的系统，甚至是原型，都是困难的。认知科学家大多缺乏做好它的技能和设计想象力。

对我来说，这表明需要一个单独的人类增强领域。该领域将接受认知科学的投入。但它从根本上将是一门设计科学，面向大胆、富有想象力的设计，并构建从原型到大规模部署的系统。

##### 致谢

我最初对Anki很感兴趣，部分原因是[Gwern Branwen](https://www.gwern.net/Spaced-repetition)、[Sasha Laundy](https://sasha.wtf/anki-post-1/amp/)和Derek [Sivers](https://sivers.org/srs)的写作。感谢Andy Matuschak、Kevin Simler、Mason Hartman和Robert Ochshorn关于这篇文章的许多刺激性对话。我特别感谢Andy Matuschak的许多深思熟虑和愉快的谈话，特别是指出Anki可以成为理解的精湛技能，而不仅仅是记住事实的手段，这种观点是多么不同寻常。最后，感谢所有[在我的推特帖子](https://twitter.com/michael_nielsen/status/957763229454774272)上评论Anki的人。

#### 附录1：Anki学习时间分析

以下是对研究Anki卡进行20多年召回所需努力的大概分析——我们可以合理地考虑终身召回。请注意，分析对所做的详细假设很敏感，因此不应过于认真地对待时间估计。尽管如此，了解所涉及的时代是有用的。

当最初输入卡片时，Anki只需要在1分钟后进行审查，然后是10分钟。在这些审查之后，审查之间的间隔大幅增加，达到1天。之后的间隔扩展率可能略有不同\*\*原因是Anki允许您在查看卡片时指定您发现卡片“简单”或“硬”，此外还有通用的“好”（意味着您做对了）或“再次”（表示您做错了）。这些附加选项会改变间隔扩展的确切速率。在实践中，我几乎总是选择“好”，或者告诉Anki我把卡弄错了。但对于我的卡来说，每次成功审查的典型扩展率约为2.4。这意味着成功的审查将把间隔提高到2.4天，然后提高到2.4 \* 2.4 = 6.76天，以此类。平均而言，我大约每12张卡中就有1张卡错了，所以到第12张卡，我们之间的评论间隔约为2.49 = 2,642天。请注意，我们提高到第9次幂而不是第12次幂，因为直到卡片的第三次重复，间隔才达到1天。

如果你把这些间隔全部加起来，这表明失败审查之间的典型时间约为12年。然而，请注意，我已经很久没有使用Anki了，这个估计可能过于乐观。通过观察我的卡片审查之间的平均间隔已经是1.2年，我们可以在失败之间的时间上得到一个下限。要实现1.2年的间隔，需要大约0.9年的成功之前审查，所以平均而言，我的卡片在两次失败之间至少需要2.1年。然而，实际数字可能要高得多，因为没有理由假设我对大多数这些卡片的下一次审查会失败。因此，假设一个保守的估计是4到7年失败之间的平均时间。

如果我们假设失败之间的平均时间为4年，那么超过20年意味着5次失败，并审查5次失败\*每期10次评论=50次，总共50\*8秒=400秒，或大约7分钟。

相反，如果我们假设平均失败之间的时间是7年，那么超过20年意味着大约3次失败，并审查3次失败\*每个周期11次审查=33次，总共33\*8秒≈260秒，或大约4分钟。

请注意，在Anki的模型中，失败会将审查间隔重置为10分钟，然后重置为1天，2.4天，以此类。在实践中，这似乎太保守了。在一张卡片失败了一两次后，我通常会明白，如果Anki在重置审查时间表时不那么严厉，那就更好了。更好的复习时间表将减少总学习时间，如果2分钟的典型承诺是可能的，我不会感到惊讶。

#### 附录2：使用Anki学习API

Anki的一个很好的用途是帮助学习API。以下是一些适合我的模式，以及一些关于反模式的警告。

它从我决定有一些我想学习在项目中使用的API开始。有时，我只想使用一下API——比如说，对于50-100行代码，甚至只是一些1-10行代码片段。在这种情况下，我最好把它放飞，改编来自其他地方的片段，并根据需要咨询文档。

但假设我知道我将在项目中更认真地使用API。例如，对于我的论文《[作为技术思考](http://cognitivemedium.com/tat/)》，我想使用3D图形构建一些原型，并决定学习[three.js](https://threejs.org/) Javascript库的基础知识。

一个诱人的失败模式是思考“哦，我应该先掌握API”，然后深入研究教程或文档。除了快速浏览教程或文档外，这是一个错误。更好的方法是找到一个功能正常的小代码，它与我项目的核心功能相关。它不需要与整个项目相似，但理想情况下可以实现一两个类似的功能，并且有几十或几百行代码长。我让代码运行，然后开始进行小调整，添加我需要的功能，取出我没有的位，并尝试理解和改进代码。

我可能只是错误地让事情发生......我感到非常兴奋，把事情变成现实......一旦它变得栩栩如生，它就开始告诉你它是什么。-丹·英戈尔斯

这样做的好处是，我一次只需要更改1到5行代码，并且我看到在实现我的目标方面取得了有意义的进展。那太令人兴奋了。使用机器学习的隐喻，这就像在有意义的项目空间中进行梯度下降。

当然，在这样做的时候，我会不断在文档、StackOverflow等上查找内容。我还将阅读和理解我开始的代码片段。Ankify这一切很诱人，但这是一个错误：这需要太多时间，而你Ankify太多，后来发现没有什么用。然而，当某样东西显然是一个核心概念，或者我知道我会经常重复使用它时，它值得添加到Anki中。通过这种方式，我逐渐建立了一个知识库，这些知识库可以在真实的现场项目中使用。而且，慢慢地，我变得越来越好。

一旦我在项目上取得了真正的进展，并确信我已经很好地选择了API，那么通过教程就有意义了。我通常会快速学习几个这样的教程，并确定我相信我能最快地从中学习的教程。然后我努力克服它。在这个阶段，我做Ankify，但要保持相对轻。试图让一切都变得令人费尽如其情，但我最终会以巨大的时间成本记住很多无用的信息。只有Ankify材料要好得多，我知道我会反复需要。通常，这意味着我已经可以看到，在我项目的当前阶段，我现在需要它。在第一次传球时，我是保守的，更少的材料。然后，一旦我完成了一次教程，我就会回顾它，这次是了解我以后可能需要的一切。这第二次通过通常相当快——通常比第一次通过快——但在第二次通过时，我有更多的背景，我对Ankify什么的判断更好。

我继续这样做，在处理我的项目和处理Anki之间来回跳动，因为我正在阅读教程和文档，以及阅读代码时出现的材料——来自他人的代码，甚至我自己编写的代码。我发现，如果未来可能有用的话，为我个人编写的代码提供Ankify API会非常有帮助。仅仅因为我写了一些东西并不意味着我将来会记住它！

所以：不要直接跳进Ankifying教程和文档。等等，在你的项目上认真工作的同时做。我必须承认，我建议这样做的部分原因是我发现自己很难接受这个建议。我几乎总是后悔没有关注它。我启动了一个新项目，想“哦，我需要这样那样的API”，然后深入教程，花几个小时。但我挣扎和挣扎，进展非常缓慢。直到我记得找到一些工作代码开始，并立即发现事情进展得更好。然后，我发誓再也不使用教程优先的方法了。不幸的是，在实践中，我觉得它很诱人。

整体过程很像对新API的常见边做边学方法，在项目上工作时，您可以通过重复逐渐学习API。主要区别在于，偶尔穿插使用Anki大大加快了您聚集新知识的速度。

一种潜在的失败模式是想“哦，有一天我可能想学习这样那样的API，所以我应该开始添加卡片，即使我目前没有使用API的项目。”

我已经试过几次了，我的建议是：不要这样做。

这是我在文章正文中描述的一种问题：在某一天使用知识时储存知识的诱惑。如果您在项目中同时认真使用API，您将更快地学习。使用API创建新东西有助于您从API中确定要记住的重要内容。它还——这是猜测——向你的大脑发送一个信号，说“这真的很重要”，这对你的记忆有很大帮助。因此，如果您想进行投机性Akification，请不要这样做。如果你发现自己开始了，就停下来。

一个更具挑战性的部分失败模式是Ankifying什么变成孤儿API。也就是说，我将在一个项目上使用一个新的API，并Ankify来自API的一些材料。然后项目就完成了，我没有立即使用相同API的另一个项目。然后我发现我的头脑不会很好地参与卡片——有一个半自觉的想法“我为什么要学习这些无用的东西？”我只是不再觉得卡片像我积极使用API时那样有趣了。

这是一个困难的局面。我使用经验法则，如果看起来我不太可能再次使用API，我会在卡片出现时删除它们。但是，如果我可能在未来一年左右使用API，我会把它们保留在甲板上。这不是一个完美的解决方案，因为我确实稍微断开了与卡片的连接。但这是我找到的最好的妥协。

在学术工作中，请引用以下内容：Michael A.尼尔森，《增强长期记忆》，http://augmentingcognition.com/ltm.html，2018年

本作品根据[知识共享署名-非商业性3.0未移植许可证](http://creativecommons.org/licenses/by-nc/3.0/deed.en_GB)授权。这意味着您可以自由复制、分享和构建这篇文章，但不能出售它。如果您对商业用途感兴趣，[请联系我](mailto:<EMAIL>)。
