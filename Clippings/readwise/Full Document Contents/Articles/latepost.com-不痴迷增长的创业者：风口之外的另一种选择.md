---
人员: 
  - "[[latepost.com]]"
tags:
  - articles
日期: 2022-10-18
时间: None
链接: https://www.latepost.com/news/dj_detail?id=1348
附件:
---
## Document Note

## Summary

## Full Document
![摘要图片](https://imgproxy.readwise.io/?url=http%3A//www.latepost.com/uploads/cover/c847ab0a6e9a950e48260a97188e1300.jpg&hash=32321ed895c61de92bf1dab0e660f386)
柳毅是一家创业公司的 CEO，虽然是位 90 后，但他的生活到处都散发着一种 “退休感”。

他每天自然醒，不过，是早上五点半；上午，天气好的时候，他要专门晒会太阳；中午，他自己烧几道菜，盛进不同样式的和风陶瓷盘；晚上七点半，还要打一个多小时的乒乓球。

他对员工要求很严格：不许加班。开发进度延期也不必赶，“收入不会因为一次发布而有什么影响。” 他和下属们说。这是他创业的原则：让用户提升生活品质的应用，不能出自一个血汗工厂。

作为一个成熟的开发者，他早就不再频繁地刷 App Store 榜单，因为产品的下载量增长通常遵循市场规律。

7 月 27 日那天是个例外。凌晨 6 点，他新开发的产品 “谜底黑胶”（MD Vinyl）在 App Store 美区下载榜突然飙升到第 10 名，超过 Snapchat。9 小时后，又超过 YouTube、Google、Instagram、TikTok，成为总榜第二。这个产品没花钱做推广，却超越了大公司规模化运营的产品们，他感到 “非常意外”。

那天晚上，柳毅约同事们出来喝酒，庆祝可能成为冠军的时刻。一直到酒喝完，也没登上冠军宝座。“最后自我安慰，庆祝第二名，第二也很厉害。” 他笑着讲到。

回到家，柳毅睡不安稳，隔一两小时就醒来刷一下排行榜。凌晨 5 点 57 分，终于到第一名了。他高兴极了，在床上坐起来，忍不住一直笑。他把排名第一的截图发到他组织的独立开发社群，和群友们说，“一切皆有可能。”

当一次冠军并不容易。多年前，柳毅在一个互联网大公司做移动应用开发。那家市值数千亿美元的公司也曾誓言进军海外，投入过不小的资金和团队，但从没有一个产品在美国这样的海外大市场到过第一。

尽管整个行业都在说流量红利消失，但类似柳毅这样的小型团队正在增加，收入也在改善。2022 年 5 月，苹果引用的一份第三方分析报告指出，从 2019 年到 2021 年，中国小型开发团队（产品年下载量低于 100 万次，年收入不超过 100 万美元）的营收增长了 94%。

这批最成功的小团队创业者往往有类似经历：专注技术或产品，有的曾在互联网大厂开发岗做得不错，有的在创业公司当过合伙人、CTO。他们依然可以在很多人向往而不得的环境里获得不错的收入，但抽身离开。

他们不再追逐曾经标准的中国互联网叙事：大市场、大投资、大增长；不再迎合中国最广大的用户群，而是做自己真正喜欢的产品，期望找到有共同需求的用户；不再频繁加班，为迭代放弃生活。

在这条路上，技术褪去 “印钞机” 的时代光环，回到理性工具的本质。一款产品也拿回了它的自然成长规律，在漫长的、持续多年的打磨中被更多用户获得、认可。

他们的成功有赖于环境的变化。不止全球，中国今天也有足够多的人受够了大公司无节制地弹窗广告、分期提醒、数据滥用，愿意为一些尊重用户的产品付一杯咖啡的钱。

无尽增长不再是事实。新环境下，大公司依然是大公司，但创业者和用户都有了更多选择。

一个放松的 CEO，“玩” 着做产品

7 月 28 日，应用登顶那天一早，柳毅把妻子梁逸伦叫醒——她是谜底科技联合创始人，也是柳毅的 “顶梁柱”。

“我们到第一了。” 柳毅说。

妻子很淡定，应了一声，就继续睡了。

柳毅做 iOS 开发 11 年了，“今年这一个夏天把十年想的很多目标都完成了。”他说。

![](https://www.latepost.com/uploads/contentImg/aec27f5086330a369dcc4fdaec30ab93.jpg)
柳毅（右一）和谜底科技部分成员

谜底科技 2018 年成立，直到 2022 年 6 月底才迎来第 6 个员工。十几款应用都在传达团队的气质：年轻、灵动，有时还有点调皮。

比如，“谜底时钟” 有一个 “霓虹” 主题，给手机充上电，霓虹灯管样式的表盘才会一格一格亮起来——灵感来自柳毅玩解谜游戏时遇到的一个关卡；换到 “积木” 主题，晃晃手机，数字块会像真的积木一样掉落。

上线最初一年，“谜底时钟” 每天的下载量几乎都在 500 次以下，柳毅仍然坚持更新，把自己脑子里的好想法加进去，直到产品积累到 19 种主题、100 多个小组件。市面上几乎没人能抄它了。2022 年 5 月，“谜底时钟” 成为 132 个国家 App Store 的 App Of The Day；6 月被提名苹果设计奖（Apple Design Awards）。

小创意可以慢慢经营变大。“谜底时钟” 最初定价 12 元，随着版本升级提价到 18 元、25 元。2016 年，柳毅就关注到苹果官方鼓励订阅制的趋势：买断制产品，苹果分走 30%，订阅制第二年只分 15%。他认为这是对小团队更健康的发展模式：用户花十几块钱就把产品买断，开发者要生存就只能不停拉新。三五年后只剩老用户时，很难有动力把产品做得更好。

2021 年，他增加了订阅制。有时柳毅觉得自己的产品定价有点贵，不过他会把个人感受和对市场的认知分开：“地球上的人太多了，找到愿意为你的产品付费的那一部分人就好”。他自信产品能提供对等的价值。

![](https://www.latepost.com/uploads/contentImg/54e525e794f1764bf5b3d5f97cb22588.jpg)
“谜底时钟” 设计师的手绘草图

2022 年 2 月上市的 “谜底黑胶” 是谜底科技最快达到 100 万下载的应用。这款音乐小组件的视觉模拟黑胶唱机：音乐播放时，专辑封面会像唱片一样旋转起来。

这是绝不会在大公司立项的产品：不做市场调研，不看数据，完全源自开发者的爱好。

淘二手唱片有特殊的乐趣：托着封套轻轻把盘抽出来，“彩蛋” 随机掉落——一张五十年前的报纸剪报，上面有对专辑的推荐，是前主人留下的。柳毅一年前开始玩黑胶，买过一箱 60 张唱片，像开盲盒那样一张一张打开，把蒙尘的唱片拿到清水下冲刷，晾干。他从未想过还可以这样和音乐交互。

柳毅坐在地板上听，盯着唱片一圈一圈旋转，直到播完才起身去翻面。他想把自己专注听音乐的美好感受传递给更多人，于是着手设计手机音乐插件应用。

他砍掉所有可能分散注意力的播放器功能，反复跟设计师强调 “要沉浸感”， “谜底黑胶” 主视觉的唱机就是照着柳毅家那台画的。

设计过程不做互联网开发必不可少的 A/B 测试。他把 iPad 放地上，唱片 UI 设计草图放到全屏，躺在沙发上，时不时侧身看看，想象用户会不会也想一直看着它。用心的产品，用户感受得到。很多人评价 “盯着它看了很久”。柳毅很欣慰：“这就是我想要的”。

7 月，TikTok 上逐渐掀起一股自发推荐 “谜底黑胶” 的风潮：年轻女孩们露出吃惊的表情，下一个镜头切到手机桌面，“谜底黑胶” 的唱片机在手机上缓缓转动。有的视频甚至只有播放音乐的录屏，短短几秒钟，就获得几十万点赞。这些视频都打着 #mdvinyl 的标签。

离开大公司，一个人的产品、1MB 的坚持

找到独立开发者程凯需要多花些时间。

从北京东北二环坐地铁将近 2 小时，下了地铁只能找到哈啰单车，它还会提示：你在运营区的边缘，不要骑出去。在一片略显荒凉的地带，两个巨大的长方体建筑拼插在一起，表面上是大小不一的几百个小窗户，在周遭环境中显得过于现代。

那是一个图书馆，也是程凯每天办公的地方，他的 “工位” 就在图书馆电子阅览室里，是角落的一张桌子。

他的工作时长是上午 3 小时，下午 3 小时——过去在中关村上班，通勤往返时间就得 3 小时。他每天平均写代码不超过 100 行，因为 “好多功能不是一气呵成的”。晚上回家后的时间不属于工作，他看书看电影，有时打打电子游戏 FIFA。

过去 7 年，程凯的时间和热爱都倾注给一款产品，X 浏览器（只有 Android 应用）。它是一个手机浏览器应用，目前日活在 30-40 万之间。

每个手机都自带了 “够用” 的官方浏览器，这是一个存量市场——用户增长像一条 “每个月几乎看不出变化” 的缓坡。不会再有大公司倾注人力做一个新的手机浏览器，对独立开发者却是好事。浏览器的产品研发和维护能以十年计，中间的迭代、打磨，没办法通过人海战术去逾越。

他保持着一个好开发者的自律，7 年里保持每周或每两周一次版本更新。他喜欢挑战技术难题，花很长时间优化广告拦截规则响应的速度，思路卡住就去散步。灵感来了，马上回图书馆敲代码，导入 10 万条规则的时间从 100 毫秒降到了 20 毫秒——这一切发生在用户察觉不到的地方，但足够多的改变叠加在一起吸引到了足够多的簇拥。

程凯也不做超级应用，不追求通过一款超级产品吞噬用户的在线时长。他坚持让 X 浏览器只有 1MB 大小，几乎到了执着的地步。

有人抱怨网页切换不够顺滑，他不改，因为一个过渡动画的 UI 框架要 3MB；想实现什么功能都自己写，如果集成别人的框架又是 2-3MB；看起来非常基础的扫码功能要 700KB 左右，他把它设计成插件，让用户按需下载——当人们对几百兆的应用司空见惯，一个 1MB 的浏览器 “一定会超出预期”。他觉得这很酷。

保持产品极简、极速，也意味着克制地盈利。X 浏览器默认首页只有一个搜索框和 “收藏”“历史” 按钮，只有一种最基础的盈利方式：用搜索联想词为百度等合作方引流，获取分成。

程凯估计现在的收入比 7 年前上班时多两倍，如果继续留在大公司肯定不止这个数字。在能满足日常生活的情况下，他不想接影响用户体验的广告。他有一种自觉：“要清楚自己就是服务小众用户，别有这么多欲望。”

X 浏览器的产品研发、项目进度管理、前端应用编程、后台开发、上线后的测试和优化，都是一个人完成。1990 年代，互联网还是新事物的时候，这是被硅谷鼓励和推崇的 old-fashion。

这些年，大公司的软件开发早已变成劳动密集型工作，他们不再需要全栈工程师，更不要说硅谷一度推崇的 “10X 工程师”（十倍工程师，一人顶十个用，可以解决各种复杂问题）。技术工种细分是工程上的进步，个体解决确定问题比创造性工作更多。大公司里的工程师们清楚地知道，自己的工作不足以承接他们的技术理想，但薪资和职位在过去可以确定地增长，也稳住了绝大多数人。

在独自创业之前，程凯是帮公司快速增长的人。

2015 年，他辞去傲游部门经理职务，“奔着钱” 去了一家医美创业公司做 CTO，估算几年后行权能拿几百万美元。这是个强运营导向的业务，用什么技术架构根本没人关心。他整天埋在整容美女照片中，思考如何让术后效果更吸引眼球，提升点击率。

干到第二个月，他猛然发现自己每天都频繁看表，盼望下班。以往，他可是那种会主动加班的人。他问自己：真的很在意那笔钱吗？想通之后，他迅速帮公司招到下一任 CTO 后离职，回到了老本行：做手机浏览器。

2015 年，工具类应用出海热潮达到最高点。程凯也想做浏览器出海，“来个降维打击。”——这是当时具有高度自信的互联网人经常说的词。

他去谈投资，“骨子里对华丽 PPT 这类东西很排斥”，就干讲。投资方是一位头部游戏出海公司创始人，也是著名的连续创业者。

矛盾随后浮现：投资人希望程凯只把精力放在产品和技术上，市场推广交给他们，并要求尽早在产品内做商业化，程凯认为这大概率会影响用户体验。投资人希望快速验证，让开发两三个月的产品直接上线 Google Play，这不是程凯预期中的节奏。

20 万元上线推广费砸出去，“幻想过一炮而红，但怎么可能呢？” 程凯再次被要求让渡公司主导权。

“我觉得你根本不像 CEO……你根本没有大的野心。” 投资人说话直接。沟通的结果是，程凯退回了那笔钱，跟团队宣布散伙。

一一送走当初苦口婆心招来的同事，离开借用的投资人公司的工位，程凯反而松了口气，他更清楚不是所有的产品创造者都适合当一位这个时代的互联网创业者，“再让我当 CEO 我可能不愿意了。” 他说。

都曾卷入创业热潮

2014 年 6 月，中关村创业大街 “开街”，之后一年孵化将近 600 个创业团队，其中 350 多个团队总共得到超过 17 亿元的投资。

创业者们在车库咖啡兴奋地聊项目，幻想着 “拿 VC 一笔钱去改变世界”。一些来历不明的人路过听了几句就可能递上简历。那是中国互联网历史上不再复现的，龙卷风级别的 “万众创业” 潮，席卷无数聪明而有野心的年轻人，也让一些人丢掉客观理性。

2015 年，笔记应用 flomo 创始人刘少楠第一次创业，做了一款叫 “食色” 的美食图片社区。融资很容易，天使轮 100 万人民币是前领导投的，协议都没签，钱就打到了他的私人账户上。

拿到钱，他和合伙人马上在上海市中心小别墅租了一间屋。他曾拜访过当时在法租界洋楼办公的小红书，“一进去就 fancy 到不行”。小红书的人曾跟他说，“租这种地方招聘成功率很高”。

在一个洋气的地方创业，是那时的流行趋势。其实那别墅他待得很不舒服：交通不便，没有共享空间的公共设施，房子带的小花园要自己打扫，时不时还得跟邻居扯皮。

刘少楠那时陷入一种狂热和迷乱交织的状态。产品日活很快做到 10 万，他连商业计划书都没写就融到 pre-A 轮，这次是 100 万美元。

创业早期的顺风顺水让他的判断尺度失去平衡。在产品每天新增图片 10 万张时，他觉得和大众点评 “或可一战”，后者当时日增点评 30 万条。

他清楚记得那场追逐数字的噩梦。为了赶上更低的价格，每晚 12 点打开微博 “粉丝通” 买流量，凌晨 4、5 点定闹钟起床，再把 “粉丝通” 关掉。后台每秒刷新一下，账上的余额就掉一两百块，最多时一晚上烧掉两三万。

这钱花得值吗？

“别人的钱是无根之水，‘咣’ 地打到你账上，就花呗。” 他说。

在焦虑和自负的驱动下，刘少楠决定：告别擅长和喜欢的 “图片社区”，只做 “美食”，用更通俗的话说，卖盒饭，为产品找到 “良好” 的变现模式。

没有餐饮经验不要紧。“不就是开个饭店吗？学！” 当时，刘少楠这样想到。那几年，在互联网领域创业的人，很多会觉得 “传统行业都是渣渣”。他那时经常说美团那句：我不会，但我可以学。

在 PPT 写下 “去厨师化的新一代中央厨房” 的愿景后，刘少楠连刷几十位投资人，那是 2015 年，没人再愿意为这个故事买单。

妻子冷静地为他这次卖盒饭的创业给出评价：“神经病”。

刘少楠回嘴：“你不懂。”

那时，他不敢承认梦想越来越遥不可及。

他租下一间美食大排档二楼的后厨，每天上午 10 点开始备餐，加热预制料包，连同蒸好的米饭一起扣在饭盒里，一中午能做 220 份饭。他有时觉得自己团队挺厉害，有自研数据系统预测订单数量，效率很高，但更多时间感到消耗，“每天想的是怎么跟外卖小哥把烟递好，确保东西准时送到”。

账上的钱 “烧” 完了。团队也不知道该怎么办，吃了一顿火锅然后散伙，所有人如释重负。他才意识到，“人是多么能骗自己”。那次创业 3 年，刘少楠自己每个月拿 12000 元的工资，房贷就 6000 元，在上海过得很苦。

“蠢货。” 他骂当时的自己。

在痴迷增长和活下去之间，体面地盈利、体面地服务用户

现在回看，融资能力差、增长不极致的公司后来都没有什么机会。平台型公司的发展往往先通过免费产品、甚至倒贴钱换规模，再进行变现，一次次实践着 “当一个商品免费时，用户就成了商品。”

大公司的产品愈发臃肿，令人厌倦。各种眼花缭乱的入口为新业务引流、为平台促活，就连打车软件也会邀请你买菜、借贷——当用户增量不再有巨大想象空间时，这张网必须变得更复杂，以在同一个人身上获取更多的价值。

拿钱做大事的路线并不适合所有人，市场也没那么多钱了。小创业者想要养活自己，商业模式通常更直接：做付费产品或服务，而不是让用户期待免费。

独立开发者 baye 做过 8 款产品，几乎没做过主动推广——他不擅长经营，也不相信推广能让独立开发产品的收入突破一个量级。

他创造一款产品的来由常常是自己和身边人的实际需求，无法被大公司满足的那些。比如，追踪热点新闻的后续、给图片中的隐私信息自动打码，还有过滤垃圾短信。

2017 年，iOS 11 推出垃圾短信过滤接口，以及新的机器学习框架 Core ML，可以在本地处理机器学习数据，不再需要将用户信息发到服务器。这刚好契合 baye 的主张：不使用云技术，不请求手机网络权限，最大程度保护用户隐私。

当时有款呼声很高的过滤垃圾短信应用，产品设计非常原始，需要手动添加 “退订” 等关键词来识别广告。baye 意识到那是一个用新技术更好解决老问题的机会。

没有先例，没有用户数据，没有帮手，他最初只能用土办法：把自己多年里收到的几万条短信从备份里导出来，一条一条做标记，制成产品 “熊猫吃短信” 的第一份机器学习样本——那是他日后的重要代表作。

两周后，“熊猫吃短信” 模型识别垃圾短信成功率超过 90%。

短信过滤领域不乏腾讯手机管家、360 防骚扰大师等竞品，“熊猫吃短信” 依旧获得了成功。它曾多次被 App Store 推荐，曾登顶 iOS 付费应用榜，2020 年被一位抖音博主推荐后获得更多关注。

“熊猫吃短信” 上线后的样本全部来自用户反馈，每月一两万条，“双十一” 期间会增加两到三倍。baye 也经常在社交媒体发起投票，用一两千份投票结果来帮助决策。用户可以随时向他提出需求和建议，他基本都会回复。

这是大公司的产品经理做不到的。“熊猫吃短信” 定价 3 元，一次性买断。加上其他几款独立开发的收入，虽然不如在大厂打工那么多，但也足够他当时在北京体面地生活。

不必为资本服务的创业者更可能做到尊重用户。

2020 年开始做 flomo 之后，刘少楠和合伙人 Lightory 把经营理念写进产品说明：不融资，“确保不用刻意追求规模和增长”；不卖广告，“因为贩卖用户的注意力与笔记服务希望解决的问题背道而驰”；以订阅制会员作为收入模式，但不卖永久会员，“上一个卖永久会员的是乐视。请问永久是多久？”

他们相信，先把整体用户价值做大，回报 “会像影子一样跟着你”，来得慢一些也没关系。为了让 flomo 体面地存在足够长的时间，他们严控成本——flomo 没做短信验证码登录，因为每条短信要 0.04 元。

做事的边界越清晰，人越自由。刘少楠早上八点半来到创业园区的小办公室，十一点多开始午休，搬一把户外椅在园区里晒太阳看书，晒热了就回去干活，下午四五点收工。他经常漫步在自然景致中，拍下龙井山深处的绿荫、西湖朦胧的山水，园区小竹林围出的一小块蓝天——他是为数不多的，让人想起杭州是 “人间天堂” 而不仅是 “互联网之都” 的创业者。

![](https://www.latepost.com/uploads/contentImg/ae7437ed48f329baa440d24c6587fffa.jpg)
刘少楠有时在园区户外办公

Lightory 在上海家中办公，每天确保完成一件 “对最终结果有足够大影响” 的任务，其他大量时间用于阅读。广博的阅读使他脑中构建起一张认知网络，他在这场思维游戏中收获愉悦。他们每个月至少安排一次 “不为解决问题” 的见面，花一整天时间爬山或者闲逛，探讨 “维特根斯坦怎么 PUA 罗素”。

flomo 的 slogan“持续不断记录，意义自然浮现” 也是逛着聊出来的。不做功能繁多的编辑器；反对信息囤积，不支持一键导入；不希望用户产生 “有意无意的表演欲”，不做打卡签到等社交功能；笔记分享出去也没有品牌 logo。

克制的路上还是要不断对抗欲望的本能。

2021 年 5 月，他们开发了 “导航卡” 功能，在笔记中打的标签会链接起来，形成一张信息网。内测时，用户却普遍用不上手。他们开始反思自己 “是否 ego 过于庞大了”。“虽然之前口头上说，我们做小点就好，内心最深处还是想，总得做个像 Notion 一样的大玩意。” 刘少楠说。

没有太多纠结，新版本的 “导航卡” 功能被他们砍掉了。

一位大厂的朋友跟 Lightory 说，这种决策他不理解也没法做：“如果这功能是我一个下属做的，就是他一个季度的 KPI，不上线怎么办？怎么评价他的工作？”Lightory 不会让这种情况在 flomo 这个极小团队发生，“错误功能的堆积会让后续决策成本指数上升，而且能堆积一个错误就会再堆积十个”。

“我们不融资啊”，刘少楠笑着总结这次创业非常自如的原因。Lightory 补充，在创业里，“真正值得做的事没那么多”。

![](https://www.latepost.com/uploads/contentImg/cacdc7a5e2eadc993e9621f38bd60dc6.jpg)
flomo 两位创始人远程协作，这是“难得的合影”

体面不等于容易

不过，文章里记录的这群人可能只是小团队里的少数幸运者。

国内小团队的实际平均收入远远低于他们的水平。一位资深独立开发者观察到，目前国内大部分独立开发产品月收入小于 5000 美元，都没法纳入知名第三方移动广告数据监测平台 Sensor Tower 的统计。

据开发者技术社区思否发布的《2020 中国独立开发者生存现状调研报告》，将近一半的独立项目月收入在 1000 元以下——比当年中国所有省市的月最低工资都低；只有十分之一的独立项目月收入超过 2 万元——如果这是大厂月薪，应届工程师都会觉得不值一提。

佼佼者们也在更严苛的市场环境下经受挑战。X 浏览器主要面向极客群体，本来就是广告主定义的低价值流量，最近半年，随着合作方进一步收紧了评判标准，产品能得到的分成比例就更低。

程凯自认是个慢性的人，而过去几年互联网多的是要求快速起量的生意，大多数 “折腾来折腾去，最后什么也剩不下”，他跟不上，也不喜欢。他平静地想着未来可能的最糟糕情况：收入砍掉八成。“就当拿退休金了。” 程凯说，“从反脆弱角度讲，一个人做事是我最大的优势，怎样 ‘过冬’ 我都捱得过去。”

根据 QuestMobile，2021 年 5 月，中国移动互联网用户增速同比首次负增长。“熊猫吃短信” 屏蔽掉的无数条垃圾短信，见证消费互联网的五年：电商促销信息一度被用户标记为 “正常”，因为折扣力度实在很大。P2P、在线教育也曾是 “主角”。最近一年，“数字藏品” 平台推广短暂涌现，持续增加的是催收信息。

baye 想去做一件 “真正的大事”。在北京待了八九年的他感觉烦闷的情绪到达顶点：看不进去书，下意识地刷抖音，回过神来又卸载。朋友建议他 “找业内人士好好聊聊”。他去参加了前同事聚会，大家聊得火热，聊币圈，聊 “谁赚了多少钱”。

经常有投资人约他喝咖啡，不停带来新信息的诱惑：做 AI、做社交，各种方向看起来都很美好。他最终决定放弃这些虚无缥缈的机会——他现在称之为噪音。他要做属于自己的创造，而不是帮别人完成梦想。

2021 年 6 月，他彻底搬到青岛定居，只看了两次房就决定了。

新的灵感 “随时都会产生”，甚至在挂号的时候——他带父母去看病，各医院信息不互通，反复挂号、做同样的检验。他想优化医疗信息系统，但这事做不了。显然，并不是技术问题。

也有更实际的灵感。给他家做全屋定制的装修设计师只是拼凑几个模板就交付了，他想用技术手段生成真正的定制化方案，再直接从厂家拿物料；老家的服装产业仍然走低端路线，他想把纺织机器全部数据化，再吸引设计师带着 IP 元素来生产……

这些有想象空间的事情对他个人的挑战性太大。传统产业的数字化改革是一场更大的冒险，需要他做很多之前没做过、甚至抗拒的事，比如谈融资、整合行业资源。

“我觉得我是不善言辞、内向的人，这是正确的认知还是自我设限？”baye 第一次思考这件事。他开始买大量讲哲学的书。一本书里写着：哲学并不解决现实问题。

有时他也会懊恼：“我和扎克伯格明明一起经历了那个时代”。从高中到现在，他一直追随最顶尖的互联网创业者。现在，他决定跟自己 “部分地和解了”。

在整个行业开始习惯 “寒气” 时，小型创业团队想找到新的增长点，限制更多。

2021 年，刘少楠和 Lightory 了解了企业 SaaS 的开发难度和回报之后，放弃了企业协同方向——这里早就被巨头引领：2019 年底，钉钉与阿里云融合，得到更多中大型企业客户，在 2020 年 2 月和 2022 年 3 月 DAU 破亿；2020 年底，企业微信活跃用户数超过 1.3 亿，较年初翻了一倍；同年，飞书对所有企业和组织免费开放，开始与钉钉、企业微信正面争夺市场。

大增长机会稀缺，对任何规模的团队都是公平的考验。

若生意不可持续，拿到投资反而会促成更大范围的灾难。ofo、蛋壳、每日优鲜都是例子，投资人、用户、员工、供应商都成了受害者。当最大的公司都开始调整节奏，适应新的环境，一代创业者最终会学会控制自己的欲望。

题图来源：电影《现代启示录》
