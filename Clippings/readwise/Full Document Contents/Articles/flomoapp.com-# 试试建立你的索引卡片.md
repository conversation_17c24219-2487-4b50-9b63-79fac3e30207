---
人员: 
  - "[[flomoapp.com]]"
tags:
  - articles
日期: None
时间: None
链接: https://help.flomoapp.com/thinking/no-classification/guide-card.html
附件:
---
## Document Note

## Summary

## Full Document
之前在 flomo 的群里面，经常会有新加入的小伙伴来问：如何更好地使用 Tag，有没有 tag 的模板，甚至有些小伙伴希望先看到别人的使用 tag 的「成品」，然后再来决定怎么使用 flomo。

这恰恰是 flomo 所反对的，**因为一旦有了这样的分类压力，我们的记录自然会变少；反之，是否分门别类就能让我们变得睿智？其实倒也未必。**

知识并不是一个名词。并不是我们在 flomo 里面摘录了许多的内容，处理的井井有条就叫有知识。知识是一个动词，是我们在某个情境下，通过好奇心来给我们指点方向，然后通过勤奋的挖掘来让我们针对这个问题获得更多的信息。

而最关键的，是**在合适的时候用到这部分知识**。至此我们才能说我们真正掌握了知识。

![](https://resource.flomoapp.com/101/images-288.png!webp)
所以不是分类不重要，而是先明白一件事，**分类的目的是什么：**

* 是基于自己的好奇心发现这些信息，开始准备长期来研究，并且希望在合适的时间用上；
* 还是仅仅把信息分门别类放到合适的位置，然后就此告一段落？

如果仅仅是后者，那么怎么样的分类方式，对你将来都没有价值。如果是前者，大可不必担心。

flomo 的设计本身就受到德国的社会学家，也是知识管理大师尼克拉斯 · 卢曼的 Zettelkasten（又称卡片笔记法）的影响。

在他这套方法论里面，主要强调的是：

* 让想法之间更好地联系；
* 归集在一处，提高我们的工作效率；
* 不浪费我们的付出，因为当下也在为未来储存知识
* **根据我们所要解决的问题，自动调整规模**

在卢曼的卡片盒中，并不是按「内容类型」来分归集卡片，而是利用结构化笔记(Structure Note) 来解决索引的问题。

所谓索引笔记，它是一个关于其他笔记及其联系的笔记，相当于自己笔记的系统的 hao123.com （导航站）。

![](https://resource.flomoapp.com/101/image-21.png!webp)
![](https://resource.flomoapp.com/101/images-389.png!webp)
