---
人员: 
  - "[[知乎专栏]]"
tags:
  - articles
日期: 2022-01-26
时间: None
链接: https://zhuanlan.zhihu.com/p/365856365
附件: https://pic1.zhimg.com/v2-7abd54d0b2006a77baf3070374b1ae52_720w.jpg?source=172ae18b)
---
## Document Note

## Summary

原文出自Coach Tony的 博客，https://betterhumans.pub/replace-your-to-do-list-with-interstitial-journaling-to-increase-productivity-4e43109d15ef深受启发，并推荐作者的其他文章。下面是原文和译文。 间隙…

## Full Document
![间隙日志（或称间歇日记）](https://pic1.zhimg.com/v2-7abd54d0b2006a77baf3070374b1ae52_1440w.jpg?source=172ae18b)
原文出自Coach Tony的[博客](https://link.zhihu.com/?target=https%3A//betterhumans.pub/replace-your-to-do-list-with-interstitial-journaling-to-increase-productivity-4e43109d15ef)，

深受启发，并推荐作者的其他文章。下面是原文和译文。

#### **间隙日志**

#### **A new journaling tactic that immediately kills procrastination and boosts creative insights.**

#### **一种新的写日志的策略，可以立即消除拖延症，提高创造性的见解**

Here’s the basic idea: instead of tracking your work with a to-do list, track your work with a journal.

这里有一个基本的想法: 与其用待办事项清单来追踪你的工作，不如用日志来追踪你的工作。

For this tactic, journaling in Evernote or Notepad is fine. A paper journal would be fine too. The difference is going to come down to taste — the journal option that you find more enjoyable is the one you’re more likely to keep using.

对于这种策略，在 Evernote 或者记事本上记日志是很好的。纸质日记本也可以。区别在于口味ーー你觉得更有趣的日记选项是你更有可能继续使用的选项。

During your day, journal every time you transition from one work project to another. Write a few sentences in your journal about what you just did, and then a few more sentences about what you’re about to do.

在你的一天中，每次从一个工作项目过渡到另一个工作项目时，都要记录下来。在你的日记里写几句关于你刚刚做了什么，然后再写几句关于你将要做什么。

A project transition is when you make a switch: from checking email, to preparing a presentation, to attending a project status meeting, and then back to checking email. Each of these is a project in your day, and the times between them are interstitial moments when you should write in a journal.

项目过渡就是当你做出一个转变: 从查看邮件，到准备演讲，到参加项目状态会议，然后又回到查看邮件。这些都是你一天中的一个项目，它们之间的时间是你应该在日记中写作的间隙时刻。

For example:

例如:

```
9:37am.   
Finished email to Nik about writing another article for us. 
I probably don't need to follow up. 
Also, part of my mind is still wondering if I should have suggested a topic to him.

Now, switching over to writing an article. What's my next action? Oh. Just open Medium. 
The article is going to flow easily once I get the intro down. 
I should steel myself for having to rewrite that intro a few dozen times.

```

Damn. That’s a lot more work than just checking off a checkbox in my Wunderlist:

该死，这可不仅仅是在我的 Wunderlist 中勾选一个复选框那么简单:

**Here’s the quick pitch for what Interstitial Journaling does for your productivity, procrastination, and creativity.**

这里有一个关于间隙日志对你的生产力、拖延和创造力的快速介绍。

We weren’t built for multi-tasking, so transitions between projects are very tough. We end up getting lost in procrastination. Even when we manage to transition quickly into our next project, our brain is still thinking about the last project.

我们不适合同时处理多个任务，因此项目之间的过渡是非常困难的。我们最终会在拖延中迷失。即使当我们设法快速过渡到下一个项目时，我们的大脑仍然在想着上一个项目。

That means our second project suffers from partial attention. The science of multi-tasking says partial attention can mean a 40% or more reduction in cognitive performance.

这意味着我们的第二个项目受到部分关注。多任务处理的科学研究表明，部分注意力可能意味着认知能力下降40% 或更多。

The Interstitial Journaling tactic solves all of these normal problems. It kills procrastination, empties our brain of the last project, and then gives us space to formulate an optimal strategy for our next project.

间隙日志策略解决了所有这些常见的问题。它消除了拖延症，清空了我们大脑中的上一个项目，然后给我们空间为我们的下一个项目制定最佳策略。

That’s plenty of instruction if you want to just close this article and try it yourself. But, because I’m a productivity enthusiast, I’ll give you the origin story, the lessons from the people I tested this on, ways to customize this process for your own work, and the science behind the process.

如果你想结束这篇文章，自己试一试，这里有大量的指导。但是，因为我是一个热衷于提高效率的人，我会给你们讲述起源的故事，从我测试过的人那里学到的经验，为你自己的工作定制这个过程的方法，以及这个过程背后的科学。

Pomodoro is named for the this silly tomato shaped timer. 番茄工作时间是以这个傻乎乎的番茄形状的计时器命名的
In the **[Pomodoro Technique](https://link.zhihu.com/?target=https%3A//en.wikipedia.org/wiki/Pomodoro_Technique)**, you alternate between set work intervals and set break intervals. Most people go with 25-minute work intervals followed by 5-minute breaks.

在番茄工作法中，你可以在设置的工作间隔和设置的中断间隔之间交替。大多数人每隔25分钟工作一次，然后休息5分钟。

Generally, the break is your reward. You can surf the web guilt-free, get a snack, or text your best friend.

一般来说，休息是你的奖励。你可以毫无负罪感地上网，吃点零食，或者给你最好的朋友发短信。

What I wondered was: *what if you tried to do something productive with those five minutes?*

我想知道的是: 如果你试着用这五分钟做一些有意义的事情会怎么样？

At the time I was wondering this, I happened to be doing a deep dive on morning journaling using Julia Cameron’s Morning Pages as a jumping off point to explore all the ways you can use journaling for personal growth.

当时我正在思考这个问题，我碰巧在早上用Julia Cameron的《Morning Pages》，作为一个跳板，做了一个“深度潜水”，去探索所有你可以用日志来促进个人成长的方法。

So, I put the two together.

所以，我把这两者放在一起。

What if you journaled for your five minute Pomodoro break? That’s basically what this tactic is. And the answer to what happens is: a lot of really good things.

基于这种策略，如果你把你的五分钟番茄时间记录下来呢？答案是: 会发生很多很棒的事情。

##### The minimum journal entry

Every time you switch projects, open up your journal and enter the following three things:

每次你换项目的时候，打开你的日记，输入以下三件事:

1. Note the time. Most people will find having these timestamps to be useful to look back on. 写上时间。大多数人会发现有这些时间戳是对回顾非常有用
2. Write a few sentences about what you just worked on. “What project did I just finish? Are there any parts of that project that I’m still thinking about?” Use complete sentences rather than one word answers. “Email. Yes.” is not a valid answer. 写几句关于你刚刚做的事情的内容。“我刚刚完成了什么项目？那个项目还有什么部分我还在考虑吗?”使用完整的句子而不是一个单词的回答。“电子邮件。是的。”不是一个有效的答案
3. Write a few sentences about what you’re about to work on. “What is the first action of the project I’m about to start? How should I approach getting the project done?” 写几句关于你将要做的事情。“我将要开始的项目的第一个行动是什么？我应该如何完成这个项目?”

You can write more, and most of you will discover new things to write about, but the above is a solid format to start with.

你可以写更多的东西，大多数人会发现会冒出新的东西可写，而以上只是作为开始的一个固定格式。

##### Empty your brain

> “What project did I just finish? Are there any parts of that project that I’m still thinking about?”  
>    
>  “我刚刚完成了哪个项目? 那个项目中有没有我仍然在考虑的部分?”  
>  

When you empty your brain, you can then start the next project fully focused.

当你清空你的大脑，你就可以全神贯注地开始下一个项目。

My favorite pragmatic observation about genius comes from the computer science professor, Edsger Dijkstra:

我最喜欢的关于天才的实用主义观察来自于计算机科学教授 Edsger Dijkstra:

> The competent programmer is fully aware of the limited size of their own skull. They therefore approach their task with full humility, and avoid clever tricks like the plague.

有能力的程序员完全知道他们自己头骨的有限大小。因此，他们以十分谦卑的态度对待他们的任务，并且避免像瘟疫这样聪明的把戏。

A lot of people approach problems with hubris, hoping their brain will generate magical insight. Most often, they overload their brain by making the problem too hard. Then, their brain responds by generating mistakes.

很多人在处理问题时都傲慢自大，希望他们的大脑能产生神奇的洞察力。大多数情况下，他们的大脑负荷过重，使问题变得过于困难。然后，他们的大脑就会产生错误。

Dijkstra’s observation is that the most pragmatic path to genius (although he merely calls it competence) is to keep the problem *smaller* than your brain.

Dijkstra的观察是，通向天才的最务实的途径(尽管他仅仅称之为能力)是让问题小于你的大脑。

This is why Interstitial Journaling calls for journaling about what you just finished working on. Often, you’re still thinking about that project. Write those thoughts down, empty your brain.

这就是为什么间隙日志需要记录你刚刚完成的工作。通常，你还在想着那个项目。把这些想法写下来，清空你的大脑。

That way, when you start your next project, you’ll be bringing your full brain rather than your distracted half-brain.

这样，当你开始你的下一个项目时，你就会全神贯注而不是一个分散注意力的半脑。

##### First action vs. next action

> “What is the first action of the project I’m about to start?”  
>    
>  “我将要开始的项目的第一个行动是什么?”  
>  

I tested this Interstitial Journaling tactic with my coaching group. These are 500 people who treat productivity as one part ambition and one part hobby. They’re a little extreme about this topic, to be honest. But they’re always up for exploring the cutting edge.

我在我的训练小组中测试了这个间隙日志策略。这里有500个人，他们把生产力看作是雄心壮志和爱好的一部分。说实话，他们在这个话题上有点极端。但是他们总是在探索前沿。

I tried to convince my coaching group that they should update David Allen’s GTD concept of Next Action and call it First Action instead. They were split on whether this was a good idea.

我试图说服我的教练团队，他们应该更新David Allen 的 GTD 下一步行动的概念，改为第一步行动。他们对这是否是一个好主意意见不一。

In David Allen’s observation, most people write their to-do lists in terms of projects. “Change tires” would be an example of a project-oriented to-do.

根据David Allen 的观察，大多数人都是按照项目来写待办事项清单的。“更换轮胎”将是一个以项目为导向的实例。

The problem is when you get to a to-do list item framed as a project, you get stuck because taking action is too cognitively difficult. How does one “change tires”?

问题在于，当你把一个待办事项列成一个项目时，你会陷入困境，因为采取行动在认知上太困难了。如何“换轮胎”？

So, David Allen popularized the idea of rewriting your to-do list in terms of actions. Specifically, you should think about Next Actions so that you can keep momentum as you work down your to-do list.

所以，David Allen 推广了用行动来重写待办事项列表的想法。具体来说，你应该考虑下一步行动，这样你就可以保持动力，在你的待办事项清单上继续工作。

In David Allen’s GTD, you would translate the project-oriented to-do of “Change Tires” to the Next Action style of “Call Tire Stores for Pricing.”

在David Allen的 GTD，你会转变项目“换轮胎”到下一步行动风格的“打电话给轮胎商店并询问定价。”

Part of your Interstitial Journal entry is to identify the Next Action of your project. The easier the action, the more likely you are to do it and the less likely you are to fall prey to procrastination. This action is the first thing you should identify when you start journaling about your upcoming project.

间隙日志条目的一部分是识别项目的下一步行动。行动越简单，你就越有可能去做，你就越不容易陷入拖延症。当你开始记录即将到来的项目时，首先要确定的就是这个动作。

However, I’ve observed that many people who think they are writing a Next Action still manage to write actions that are too difficult to achieve. A poorly chosen Next Action will cause procrastination for the same reasons that a project-oriented to-do would.

然而，我观察到许多认为他们正在写下一步行动的人仍然设法写下那些难以实现的行动。选择不当的“下一步行动”会导致拖延，其原因与面向项目的“行动”会导致拖延的原因相同。

In the “Call Tire Stores” Next Action example above, how do you find the phone numbers to call all these tire stores? That’s how people get stuck. Under threat of violence, you’d figure it out. But in your day-to-day, facing a Next Action like that often makes surfing the web look more appealing.

在上面的“打电话给轮胎店”下一步行动的例子中，你如何找到电话号码来呼叫所有这些轮胎商店？这就是人们被卡住的原因。在暴力的威胁下，你会明白的。但是在你的日常生活中，面对这样的“下一步行动”会更加常见。

So, I asked my coaching group if we could rename Next Action to First Action.

所以，我问我的教练小组，我们是否可以将“下一步行动”改名为“第一步行动”。

The difference looks like trivial semantics, but the reason for this subtlety is that some people react differently to the words Next and First. Many people hear Next and think “Next Meaningful.” That’s what tricks them into writing down actions that are too hard to start or finish. Many people hear First and think “First Literal.”

这种差异看起来像是微不足道的语义，但是这种微妙的原因是一些人对 Next 和 First 这两个词的反应不同。许多人听到 Next 就会想到“下一个有意义的”这就是诱使他们写下那些难以开始或完成的行动的原因。许多人听到 First 就会想到“ First Literal”

So, the phrase First Action makes it easier to narrow in on a smaller action, like “Google Tire Store Phone Numbers” or even “Open Google.”

因此，“第一步行动”这个短语使得缩小范围变得更容易，比如“Google轮胎店电话号码”，甚至“打开Google”

This semantic change had people thinking about accuracy and, almost always, the accurate answer to the question of Next Action is something trivial, like opening a software program.

这种语义上的改变让人们思考准确性，而且，几乎总是，下一步行动问题的准确答案是一些琐碎的事情，比如打开一个软件程序。

But… not all people are the same. A different group of people rebelled against First Action because those tiny trivial actions looked boring. They needed an action that carried enough weight to feel like it was worth doing. Otherwise, they would procrastinate for fear of boredom.

但是，不是所有的人都是一样的。另一组人则反对“第一行动”，因为那些微不足道的小动作看起来很无聊。他们需要一个有足够分量的行动，让他们觉得值得去做。否则，他们会因为无聊而拖延。

People come in all flavors. I’m presenting to you specific steps as a starting point. I call this First Action now. But if you’re tied to calling this Next Action, then you have my full permission.

萝卜青菜，各有所爱。我将向你们展示一些具体的步骤作为起点。我现在称之为“第一步动”。但是如果你被束缚在调用这个“下一步行动”，那么你完全可以试试我的方法。

##### Mindful strategizing: do less, do it smarter, delegate it

> “How should I approach getting the project done?”  
>    
>  “我应该如何完成这个项目?”  
>  

At this point in the journaling, you’ve cleared your head and you have a chance to think about the best way to get your next project done.

在写日志的这个阶段，你的头脑已经清醒了，你有机会思考完成下一个项目的最佳方法。

Most often, getting the project done is a matter of scope. If you only have an hour, then you have to use a strategy that fits within an hour.

大多数情况下，完成项目是一个范围问题。如果你只有一个小时，那么你必须使用一个适合一个小时的策略。

The first time I had a strategy epiphany during an Interstitial Journal entry was right before editing an article. I had five different article submissions that I could choose from, and I wanted to publish at least one of those that day.

我第一次使用间隙日志，是在编辑一篇文章之前的策略顿悟。我有五篇不同的文章可以选择，我想在那天至少发表其中的一篇。

My default mode is to think I’m some sort of editing superhero, even though I’m a complete amateur with no training. So, my impulse is to pick the very first draft and then charge through it.

我的默认模式是认为我是个写作超人，即使我是一个完全没有受过训练的业余爱好者。所以，我冲动地选择第一篇，然后准备完成它。

In other words, I normally wouldn’t consider strategy at all.

换句话说，我通常根本不会考虑策略。

But, in my Interstitial Journal I noted that I only had an hour for this editing work. From experience, I know that editing an article can take anywhere from 15 minutes to 2 hours.

但是，在我的间隙日志上，我注意到我只有一个小时的时间来做这个编辑工作。根据经验，我知道写一篇文章可能需要15分钟到2小时不等。

So, breaking all normal rules of my own behavior, I opened all five articles. Then, I ordered them by which looked to be easiest to edit and chose the easiest article. Am I a genius? No. But that brief moment of mindfulness helped me pick a smarter strategy. I got the article submitted on time. And in the course of my day, projects completed on time add up to major victories.

所以，打破了我自己行为规则，我打开了所有的五篇文章。然后，我按照看起来编写难易度的顺序排列它们，并选择最容易的文章。我是天才吗？不是。但是那个短暂的正念时刻帮助我选择了一个更聪明的策略。我按时完成了那篇文章。在我的一天中，按时完成的项目会带来重大的胜利。

Anyone who has ever done journaling in other contexts knows this — your journal is an opportunity for truth and honesty about yourself that you don’t normally have. I’m too ambitious about what I take on, while being cowardly about working hard. Putting those thoughts into a journal moves them from feelings that secretly rule my decisions to rational concepts that I can analyze and solve.

任何曾经在其他情况下写过日志的人都知道这一点ーー你的日志是一个了解真实和诚实自我的机会，这是你通常不具备的。我对自己的工作抱负太大，而对努力工作却怯懦不前。把这些想法写进日记，可以把它们从秘密支配我决定的感觉转移到我可以分析和解决的理性概念上。

I think the right word for this experience is *mindfulness*. Journaling as you work produces mindfulness about your context, goals, mood, and skills. Honestly, many so-called knowledge workers are going through their day as mindlessly as possible. You’re really going to stand out with this tactic.

我认为这种体验的恰当说法是“正念”。当你工作的时候，写日志可以让你对你的环境、目标、情绪和技能保持专注。老实说，许多所谓的知识工作者正在尽可能漫无目的地度过他们的一天。这种策略真的会让你脱颖而出。

I almost never explain any productivity topic without referencing the book, **[Thinking, Fast and Slow](https://link.zhihu.com/?target=http%3A//www.nytimes.com/2011/11/27/books/review/thinking-fast-and-slow-by-daniel-kahneman-book-review.html%3Fmcubz%3D0)**.

我几乎会在解释任何关于效率的话题时引用《思考，快与慢》这本书。

The book covers two modes of decision making. One is a rational but effortful mode. This is what we wish ruled our life. The other is an emotional and habitual mode that sits just below our consciousness. This is what actually rules our life.

这本书涵盖了两种决策模式。一种是理性而努力的模式。这就是我们希望支配我们生活的模式。另一种是一种情感和习惯模式，就在我们的意识之下。这才是真正主宰我们生活的模式。

The magic of journaling is that it is almost always effective at bringing thoughts and feelings up to a place that triggers your rational mind. The net effect is that you’re rebalancing and being more rational.

记日志的神奇之处在于，它几乎总是能有效地将你的想法和感受带到一个触发你理性思维的地方。最终的结果是，你重归平衡，变得更加理性。

##### Maker vs. Manager considerations

In the introductory instructions, I said I wasn’t very concerned about what tool you used for journaling, an app or a physical journal.

在介绍说明中，我说我不太关心你用什么工具写日志，一个应用程序或者纸笔。

This isn’t completely true — the best tool for you really depends on whether you’re a Maker or a Manager.

这并不完全正确ーー对你来说最好的工具实际上取决于你是创造者还是管理者。

If you’re doing project work on a computer all day, then I think you’ll prefer using an app like Evernote. There’s something about just flipping back and forth between your apps that helps keep most people in the flow.

如果你整天都在电脑上做项目工作，那么我想你会更喜欢使用像 Evernote 这样的应用程序。在应用程序之间来回切换有助于让大多数人保持心流。

If that’s your work day, I’d call you a Maker (this is a reference to the article **[Maker’s Schedule, Manager’s Schedule](https://link.zhihu.com/?target=http%3A//www.paulgraham.com/makersschedule.html)**). Makers should keep their journal in an app.

如果那是你的工作日，我会称你为制造者(详见原文引用)。制造者们应该把他们的日志保存在一个应用程序中。（这里没看明白，欢迎评论指正）

If you’re a Manager, then you probably spend a lot of your day away from your computer in meetings. For you, I’d recommend two things:

如果你是一个经理，那么你可能会花很多时间远离电脑参加会议。对你来说，我建议你做两件事:

1. Use a paper journal. 使用纸质日志
2. Schedule meetings with a five minute gap in between. That gives you time to do the journaling. 把会议的安排留出五分钟的间隔，这样你就有时间写日志了

If you are a Maker who works with your hands instead of a computer, then congratulations. The physical world is a rich and wonderful place, and I always enjoy turning off my computer to visit it. You should use a paper journal.

如果你是一个用双手而不是电脑工作的创造者，那么恭喜你。现实世界是一个丰富而美妙的地方，我总是喜欢关掉电脑去参观它。你应该用纸质日志。

Changing projects feels like making a U-Turn. 改变项目感觉就像掉头一样
##### Your journal is a habit for pivoting to new projects

An NBA trainer introduced me to a concept he teaches his athletes called “Next Play Speed”. The concept is that there is a transition between offense and defense, where basketball players often get lost. This is the basketball version of an Interstitial Moment.

一位 NBA 教练向我介绍了一个概念，他教他的运动员称为“下一个进攻速度”。这个概念是在进攻和防守之间有一个过渡期，篮球运动员经常在这个过渡期迷路。这是篮球版的间隙日志。

He uses a video (below) of Michael Jordan transitioning back and forth from offense to defense. It’s actually pretty amazing to watch, because Jordan makes those transitions much faster than everyone else on the court.

他使用了迈克尔 · 乔丹从进攻到防守来回转换的视频(如下)。这实际上看起来很惊人，因为乔丹比球场上的其他人更快地完成了这些转换。

What the NBA trainer, Graham Betchart, does is clue you into the habit that Jordan developed. It’s not “switch fast!” — that’s just a goal.

教练 Graham Betchart 所做的就是让你了解乔丹养成的习惯。这不是“快速转换!”那只是一个目标。

Rather, Jordan’s habit is that the instant the ball changes hands, Jordan plants his pivot foot and changes direction. That’s his habit.

相反，乔丹的习惯是，当球一转手，乔丹就移动他的中枢脚并改变方向。这是他的习惯。

Humans are famously slow project-switchers. Part of the problem is that it’s impossible to make a habit out of switching between projects because each project is unique. There’s nothing consistent to habitualize.

众所周知，人类是缓慢的项目转换者。部分问题在于，不可能养成在项目之间切换的习惯，因为每个项目都是独一无二的。没有什么东西可以让人习惯化。

But, Interstitial Journaling is something you can make a habit out of. Finished a project? Habitually flip over to your journal, write the time down, reflect on what you just did, then reflect on what you’re about to do.

但是，间隙日志是你可以养成习惯的东西。完成了一个项目？习惯性地翻阅你的日志，写下时间，反思你刚刚做了什么，然后反思你将要做什么。

It’s the knowledge worker’s version of planting a pivot foot.

这是知识工作者的版本的确立中枢脚。（pivot foot，中枢脚，篮球术语，详见[百科](https://link.zhihu.com/?target=https%3A//baike.baidu.com/item/%25E4%25B8%25AD%25E6%259E%25A2%25E8%2584%259A/9649380%3Ffr%3Daladdin)）

In Graham’s explanation for basketball players, he sets the goal of achieving a “Next Play Speed” of less than one second.

在Graham对篮球运动员的解释中，他设定的目标是达到不到一秒钟的“下一个进攻速度”。

##### Upgrade option:journal everything

> For your Next Project Speed let’s think in terms of a few minutes. Can you transition from one project to another in fewer than five minutes?  
>    
>  对于你的下一个项目速度，让我们用几分钟的时间来思考。你能在不到五分钟的时间内从一个项目转换到另一个项目吗？  
>  

This tactic is focused on the interstitial moment between projects. But the obvious upgrade is to journal while you’re working on the projects as well. Basically, you journal the entire time that you’re working.

这种策略专注于项目之间的间隙时刻。但是显而易见的升级是在你做项目的时候记录日志。基本上，你把你工作的所有时间都记录下来。

I’ve **[written about this in other places](https://link.zhihu.com/?target=https%3A//betterhumans.coach.me/this-alternative-todo-list-will-help-you-complete-100-tasks-every-day-aae1130faac8%2338ac)** as an alternative way to use to-do list software. Instead of working up a complete list of what you need to do, simply track tasks as you go to build a sense of flow. Think of it as similar to the way a lawyer has to track his or her work in six-minute increments. That probably sounds like a huge chore.

我已经在其他地方写过这方面的文章，作为使用待办事项列表软件的一种替代方式。与其列出你需要做的事情，不如简单地在你开始的时候追踪任务，从而建立一种流动感。可以把它想象成一个律师必须以六分钟的增量跟踪他或她的工作的方式。这听起来可能是一项繁重的工作。

But if you get into it, you end up with this massive document of how productive you were that day. When I was using this tactic, I’d routinely complete 100 or more tasks in a day. So, part of what kept me tracking the minutia of each project was simply pride.

但是如果你深入其中，你最终会得到一份大量的文件，上面记录着你那天的工作效率。当我使用这个策略的时候，我通常一天要完成100个或者更多的任务。所以，让我一直追踪每个项目的细节的部分原因只是自豪感。

The pragmatic benefit comes from taking David Allen’s Next Action concept and putting it on steroids (I like the steroid metaphor, apparently). If Next Action is a good tactic to get into a new project, it’s equally good to take you through every little step until you finish.

实用主义的好处来自于采用David Allen的**下一步行动**概念并使用类固醇(显然，我喜欢类固醇的比喻)。如果“下一步行动”是进入一个新项目的好策略，那么带你完成每一个小步骤直到你完成它同样也是很棒的。

Originally, I’d taken the task-journaling concept and put it into the to-do list metaphor. So, all I ever captured was a long list of next actions in list form.

最初，我采用了任务日志的概念，并将其放入到待办事项列表中。所以，我所捕捉到的只是一长串列表形式的下一步行动。

But, when I moved from a list to a journal, I realized that I could analyze myself as I tracked these tasks. There’s a particular reason to do this analysis, which comes from **[recent research on procrastination](https://link.zhihu.com/?target=https%3A//betterhumans.coach.me/how-to-use-psychology-to-solve-the-procrastination-puzzle-6e6a56cdd535)** (summarized by Dr. in one of the first Medium Members articles).

但是，当我从一个列表转到一本日志时，我意识到我可以在追踪这些任务时分析自己。做这个分析有一个特殊的原因，这个原因来自于最近对拖延症的研究(由 Tim Pychyl 博士在第一篇 Medium Members 文章中总结)。

The idea is that procrastination is often a short term mood repair initiated by your subconscious. The problem and the decision to solve it are both happening below the level of conscious thought. The solution is to find a way to identify and then solve that mood at a conscious level. Journaling is one way to do that.

这个观点认为拖延通常是由你的潜意识发起的一种短期的情绪修复。问题和解决问题的决定都发生在有意识的思考水平之下。解决办法是找到一种方法，在有意识的层面上识别并解决这种情绪。写日志是一种方法。

So, as I moved my task-journaling into a proper journal, I started to ask myself, *why did I just procrastinate*? The answer was often illuminating and, more importantly, something I could overcome.

所以，当我把我的“任务日志”写成一本真正的日志时，我开始问自己，为什么我要拖延？答案往往很有启发性，更重要的是，我可以克服一些困难。

Here is a concrete example, and I want you to note the number of times I say “darn”. Those were moments of procrastination.

这里有一个具体的例子，我希望你们注意我说“ darn”的次数。这些都是拖延的时刻。

```
12:39. Edit Productivity Article

[... journal about the previous project ... ]

First Action: Open draft. I tested out a new copy editor and need to see if I liked their edits.     

Darn. I don't like this person's edits. Oh well. The piece is too dense. I'm going to break up the longer paragraphs.  

Oh. Darn. I got distracted mid-way and ended up on Twitter. I don't even know where or how. Oh. I was on HuffPo (which got unblocked on my computer), saw a post about Chris Evan's dog, and that somehow led to Seth Rogen's Twitter stream. Back to work.Need to rewrite these subheadings.

Darn. Got stuck again — this time rewriting a sub-heading made me flip over to easier goofing off. Caught myself right away. The problem is skill. I don't have a "theory of subheadings" and so I don't have any strategy for rewriting this one. Back to it and this time I'll trust my gut about what makes a good subheading.

Got tripped up one more time. When I saved the piece, I saw my Medium notifications and ended up responding to someone's response to one of my posts.

Back at it. Just have to drop the article into Asana for Medium's managing editor to publish. Checked on image rights. Had to learn how to use Google Image search via uploaded image.

1:35pm. Done.

```

That’s a work project that took me 56 minutes and in which I got distracted three times. The journal ended up being my safety net. It’s what I mentioned above about “Next Play Speed.” Having a journal gives me a habitual response to noticing my procrastination.

在这个工作项目中我花了56分钟，期间我分心了三次。日志最终成为了我的安全网。这就是我上面提到的“下一个攻击速度”写日记让我对自己的拖延症有了一种习惯性的反应。

So, yes, the emphasis and the name of this tactic is on the interstitial moments. But I believe strongly that you should actually journal your entire day.

所以，是的，这个策略的重点和名称就是间隙时刻。但是我坚信你应该每天都写日志。

>  Does this really replace your to-do list?  
>    
>  这真的会取代你的待办事项清单吗？  
>  

Yes, sort of.

是的，算是吧。

First of all, you need to differentiate between to-dos. There are to-dos that people keep which are just long-running backlogs of things they wish they would do some day. This journaling tactic has nothing to do with those.

首先，你需要区分待办事项。人们保留的待办事项只是他们希望某一天能做的事情的长期积压。这种写日志的策略与那些没有关系。

Then there are the to-do lists that act as a working document for your day, which I think are more common. You constantly check these lists to figure out what to do next and then update them when new to-dos pop up.

还有一些待办事项清单，作为你一天的工作文件，我认为它们更常见。你不断地检查这些列表，以确定下一步要做什么，然后在新的待办事项弹出时更新它们。

That second, working document, should be replaced by this journaling tactic.

第二，工作文稿，应该被这种记日志的策略所取代。

But, if that causes you consternation, realize that the beauty of a journal is that it has a flexible form.

但是，如果这让你感到惊愕，那么请认识到日志的美妙之处在于它有一种灵活的形式。

I often plop a small to-do list at the bottom of my journal. If some little thing pops up that I don’t want to forget, then I just quickly write it down at the bottom of my journal.

我经常在日记的最后列一个小小的待办事项清单。如果突然出现一些我不想忘记的小事情，我会很快地把它们写在日记的底部。

But, regardless of what I write around the edges of my journal, the focus is on the interstitial moments. That’s the core of bringing mindfulness and focus to my work.

但是，不管我在日记的边缘写些什么，焦点都集中在间隙时刻。这就是把正念和注意力集中到我的工作中的核心。

I generally believe there aren’t very many original thoughts — but as far as I can tell, this tactic is basically original.

我通常认为没有很多原创的想法ーー但就我所知，这种策略基本上是原创的。

Well, actually, it’s obviously a derivative idea based on journaling work by Julia Cameron and Josh Waitzkin and on the very popular Pomodoro technique.

好吧，实际上，这显然是一个衍生的想法，基于 Julia Cameron 和 Josh Waitzkin 的日志工作和非常流行的番茄工作法。

The name came from **[Terrie Schweitzer](https://link.zhihu.com/?target=https%3A//medium.com/u/58d4839b7777%3Fsource%3Dpost_page-----4e43109d15ef--------------------------------)**, and the idea of trying combinations came to me from Scott Adams.

这个名字来自于Terrie Schweitzer，尝试组合的想法来自于Scott Adams.。

But other than that, I think this is an original productivity idea. I’ve never heard of other people doing anything like this in a formal way.

但除此之外，我认为这是一个原创的生产力想法。我从来没有听说过其他人以正式的方式做这样的事情。

The reason originality is important to me is that if other people have done this, then I want to hear about it, link to it, and learn from it. So, if you’ve seen other techniques like this, please share them with me.

原创性对我来说很重要的原因是，如果其他人已经这样做了，那么我想要听到它，链接到它，并从中学习。所以，如果你见过其他类似的技巧，请与我分享。

Also, if you try this and make your own variations, I’d love to hear about those as well.

此外，如果你尝试这样做，并作出自己的变化，我很乐意听到这些。
