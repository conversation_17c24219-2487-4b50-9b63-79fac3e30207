---
人员: 
  - "[[tencent.com]]"
tags:
  - articles
日期: 2024-06-17
时间: None
链接: https://cloud.tencent.com/developer/article/2429120
附件: https://cloudcache.tencentcs.com/open_proj/proj_qcloud_v2/gateway/shareicons/cloud.png)
---
## Document Note

## Summary

我之前只知道，反向链接可以索引到1个笔记；直到今天才发现，原来反向链接还可以精确地索引到1个笔记的1个段落。不知道是不是新近版本增加的功能，我真的还挺喜欢它。

## Full Document
我之前只知道，反向链接可以索引到1个笔记；直到今天才发现，原来反向链接还可以精确地索引到1个笔记的1个段落。不知道是不是新近版本增加的功能，我真的还挺喜欢它。

借着这个话题，顺便也再多叭叭两句个人对反向链接的理解。

![](https://developer.qcloudimg.com/http-save/yehe-2838019/a7184140cace3a22313dcd628f8d311c.png)
反向链接，是知识管理工具的核心功能，将离散的知识【点】或观【点】，串联成【网】状结构，被称为【关系图谱】（或【知识星图】），实现从任意点出发，发散地检索出与之链接的【点】。

比如说，我以某位同事创建了一个笔记，凡是与她有关的交往记录，都会用反向链接——或者单纯使用笔记标题也可以——与它建立关联；这样，即便是很多年后，当我回想起这位同事时，都可以从这个笔记出发，利用反向链接(或标题关联)，将所有与之相关的记录聚合到一起，就可以由此形成非常清晰、具体的记忆。

![](https://developer.qcloudimg.com/http-save/yehe-2838019/09be59fd3f1db18f2c6ef3141dcad299.png)
作为一个知识管理工具，本质上是对大脑的模拟，因此也被称为【第二大脑】。

人类的大脑，大体上包含记忆、回忆、联想、思考和创新这么几个功能：

* 记笔记，是对【记忆】功能的模拟；
* 检索，是对【回忆】功能的模拟；
* 反向链接，是对【联想】功能的模拟；
* 多维度管理，是对【思考】功能的模拟；
* 在一定知识积累的基础上，找出知识之间潜在的链接，并由此产生新知识的过程，就是【创新】。

所以在我看来，反向链接或可称为Obsidian，及所有知识管理工具，的充分必要条件——即，知识管理工具必须具备反向链接功能；只有具备反向链接的笔记工具，才是真正的知识管理工具。

目前，我的Obsidian库大概包含5500个笔记，由于笔记之间添加了反向链接，由此形成的网状结构，大体上是这样式儿的：

![](https://developer.qcloudimg.com/http-save/yehe-2838019/888b7cbead8381d9c091381fae267462.png)
其中，一眼望去个头儿越大的节点，包含反向链接的数量也越多，通常也代表，这个节点的重要性越大。

我之前只知道，反向链接可以指向1个笔记；直到今天才知道，原来反向链接还可以精确地指向1个笔记的1个段落。

![](https://developer.qcloudimg.com/http-save/yehe-2838019/6238994d84c7c8418be2de323c8d31ba.png)
具体操作方法是这样的：

1. 创建1个索引到笔记的反向链接；
2. 在反向链接中添加“^”号，自动识别当前笔记中的段落；
3. 选中某个段落，即可在反向链接中，自动添加段落识别码；
4. 在反向链接中添加“|”号，可以对当前反向链接进行改名；
5. 将鼠标移动到反向链接上方，同时按住【Ctrl】键，预览索引段落；或者，点击反向链接，跳转到指定笔记的指定段落。

![](https://developer.qcloudimg.com/http-save/yehe-2838019/b9e152f404b9c1f4c47a71b2a36a58f1.png)
用反向链接索引段落的好处是：不用单纯为了便于内容关联，刻意将笔记拆解成颗粒度很小的主题单元；而是可以写作篇幅更长，逻辑更完整的笔记。在引用时，如果仅用到某个长篇笔记中的1段，也可以通过这种方式引用到。

在使用反向链接引用段落时，可能需要注意的写作技巧是：

* 在一个段落内，尽可能包含一个完整的观点及其阐述，便于使用反向链接索引后，完整预览。

![](https://developer.qcloudimg.com/http-save/yehe-2838019/3d4b5fccfa70cbc52995b9861b377de6.png)
* 当观点或逻辑比较复杂，无法在1个段落中说明时，则尽可能采用金字塔原理进行写作，即首先用1个段落说明核心观点，再分段展开阐述。便于使用反向链接首先索引到核心观点；跳转到笔记后，再展开具体内容。

![](https://developer.qcloudimg.com/http-save/yehe-2838019/e8e634c2822bfc00598aac1da4567d81.png)
* 在表达总分观点或逻辑时，可使用无序序列；无序序列可以作为1个段落被反向链接索引和预览。

![](https://developer.qcloudimg.com/http-save/yehe-2838019/3348f24701988bce67322a00719596d9.png)
好了，今天的分享就是这些内容，感兴趣的朋友，赶紧试试吧。

本文参与 [腾讯云自媒体同步曝光计划](https://cloud.tencent.com/developer/support-plan)，分享自微信公众号。

原始发表：2024-06-06，如有侵权请联系 [<EMAIL>](mailto:<EMAIL>) 删除
