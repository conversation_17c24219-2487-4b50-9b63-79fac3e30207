---
人员: 
  - "[[歸藏的 AI 工具箱]]"
tags:
  - articles
日期: 2025-03-26
时间: None
链接: https://mp.weixin.qq.com/s/iYI1EFFnmeYWVDwm_riExA
附件: https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNESpet7yPM0ib3Ua28riaqGPUKL1tlicFQGnlSd1OcQNftibl8E6M3WtnkQ/0?wx_fmt=jpeg)
---
## Document Note

## Summary

GPT-4o 的图像生成功能真正强大的地方是几乎可以通过自然语言对话完成现在复杂的 SD 图像生成工作流的所有玩法。

## Full Document
昨晚 Open AI 更新了 GPT-4o 的图像生成功能，很多朋友还在按照传统图像模型在进行测试，美学表现，编辑等。

但其实他真正强大的地方是几乎可以通过自然语言对话完成现在复杂的 SD 图像生成工作流的所有玩法。

我下面会测试所有主流的 AI 图片复杂工作流玩法类型。

比如：重新打光、扩图、换脸、融脸、风格化、风格迁移、换装、换发型还有你能想到的所有。

当然现阶段复杂图像生成流程做不到的他也能做到，后面我会展示几个。

没想到我吭哧瘪肚研发的那么多图像玩法这就都没用了。

#### 直接看测试

左边都是输入图（如果左边有两张代表有两张输入图），右边最后一张都是输出图

模版图换脸：将第一张图 Sam 的脸换到第二张图上

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNHgSXoia4ZM3Qr6llgLZIM7Db4SHaj76U7VvTlFkvXKzRTaq6ZHcHc2A/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNMwjD0fjwnVVdteUjzrDQ26JXhOiaWsuNic4ttvqK3dicLWcn3aCn7Lk3w/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNDO02d5P5bltGnLekWAEiaPrlywnsAZnZ4tARia6RwG9YibM4GoVPfQRcg/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
图片重新打光：将这张图片的背景换在一个充满鲜花的室外并且重新打光，人物不变

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNHgSXoia4ZM3Qr6llgLZIM7Db4SHaj76U7VvTlFkvXKzRTaq6ZHcHc2A/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNwbHeQroO6D4GmTIVJwttl8UY8KBS0dRVtqbDCp7MXzWBI2jIHshZtg/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
💡

图片重新打光：将他的背景放在一个魔法世界，同时带上巫师帽子，人物和穿着不变，并且重新打光

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNHgSXoia4ZM3Qr6llgLZIM7Db4SHaj76U7VvTlFkvXKzRTaq6ZHcHc2A/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNjYrsgOicOVbeO3HUeEmc8ceic3GWjVTFc3QgLmXckjicTobyNw1kMT01g/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
🌟

换装换发型：让这张图片的 Sam 穿上洛丽塔服装

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNHgSXoia4ZM3Qr6llgLZIM7Db4SHaj76U7VvTlFkvXKzRTaq6ZHcHc2A/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tN1icuP4YZUsiaWOYIiaLEtgtYQh7WsIEnzXic4sm6aqqIzuccwnMSV1UicBw/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
🌟

风格转换：将第二张 Sam 变成类似第一张图片的 Q 版被人拿着的雪糕形象，

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNV6x2qXIicpRatllfL0J3TdAU3jCe86UHnEdMAWUckKPmuGpekpA7TgQ/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNUicfV4vzxSAONgdTCmnHR7mSj8piaf1NJdRbs8x4ECewTkAMp8JvYEAw/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNPwicMicW47Wt12Ut1gTAAGPt4hHibtM61jdJrH7FZdicXwmGIovIicaeibyQ/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
💡

风格转换：将第一张 Sam 的图片变成第二张葫芦娃的风格，需要保留服装和人物抽象特征，脑袋带上葫芦

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNUicfV4vzxSAONgdTCmnHR7mSj8piaf1NJdRbs8x4ECewTkAMp8JvYEAw/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNAHjtY0ueKt2bNasQR12SEWOQzxFxr3C34eMjW8QdKcb8flibvwQTv7A/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tN5R0FFF0YDp10H9rQ1jNN2W0kq6r2ibrF483XfsddtRXicuaUa0fEG61Q/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
📌

风格转换：将这张图片的 Sam 变成皮克斯 CG 3D Q 版的风格

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNUicfV4vzxSAONgdTCmnHR7mSj8piaf1NJdRbs8x4ECewTkAMp8JvYEAw/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tN9galFzibMLlr14FCPvu2xQotTiaajXG8lL8qLPGic2npUSaPHpqWpMibrA/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
🍰

风格转换：将图 1 Sam 的照片变成图 2 这种风格

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNUicfV4vzxSAONgdTCmnHR7mSj8piaf1NJdRbs8x4ECewTkAMp8JvYEAw/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNZPGibVJrAQfQRn31Sj6H1QHweW2JfbHibkqPEC0hDfwpSnZNbml094ww/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
📍

风格转换融合 LLM：将图 1 的 Sam 变成类似图 2 的词云风格，里面的词语都需要跟 Sam 相关

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNHgSXoia4ZM3Qr6llgLZIM7Db4SHaj76U7VvTlFkvXKzRTaq6ZHcHc2A/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNyCGKZx9LfuqyRGtHljibFln0ia2yVPgmo9hT7E70rAzLvDoM0kmNp63Q/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNZPtaCPvG0ejLAkNjyG1Fv52r2ibTj30BT7N2OLS1mib8JIuUkXMQ04JA/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
🐵

梗图表情面部融合：让图 1 的 Sam 做图 2 这个梗图的表情并且脸部放大，图像变模糊

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNHgSXoia4ZM3Qr6llgLZIM7Db4SHaj76U7VvTlFkvXKzRTaq6ZHcHc2A/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNtiaPZozBwmAhLnA94CjstNbjwKajmqeurvEJVyXW8ibZQWJZyib5icRvmw/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNh5SFNCFJbUZlIMg9vs1tLcJRObzqwibYj409trEBE0WXr8OE3aepUWQ/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
#### 主流 AI 生图流程做不到的案例

我看所有人都忽略了一个 GPT-4o 的特性，他可以直接生成透明通道的图片，可能很多人表示 SD 也有模型可以，但是用过敏神那个的都知道局限性有多大。再来看 GPT-4o 的透明通道有多简单。

我用他生成的透明图片搞了个视频，但凡你有长时间的 PS 使用经验，你都能意识到这意味着什么。

视频中的人物、街道、 天气三个图层都是透明通道图层，他们可以随意组合

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNc7v4Oj3WUkvmibBKhe63GJ8E3jic5KIDVRkv7xhvCnALzoGsYgibpEysQ/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNVhcZquPowkH1iaFiaRzdwNhtkXrkx8Kz0L3gJYvNboA6R2qxRlhgUhEA/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
另外在文字排版上 4o 图像生成模型的能力更是独一份的强，所以你可以直接让他修改 UI 设计稿，或者直接生成 UI 设计稿。

提示词1：将这张图更改为蓝色氛围，星星图标改为魔法棒图标，同时将里面文案描述的主题改为其他的

提示词 2：帮我生成一张这样的 UI 设计稿：Peerlist邀请链接界面分析，界面内容。。。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNGP4F10O7ialEzn7N4j188vCVjJPheqYpZYXibLdM1iaebttkZZg08naeA/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tN7ehcbsKW7vUwHMkSsLUgR3eRq0ia4f0Aq8LkotqzPe8sIQDdejyuIAA/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
最后你甚至可以为你的设计稿加上样机。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8Egc7NS17JYGHJj7bpCLzf7tNOSZc19icM2a0FvgQKmSwgY47HIUxh3codQqak1d1tictS73UdlnE2nLQ/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
#### 复杂工程化最终会被模型碾碎

当我们站在技术演进的长河中回望，总能发现一个不变的规律：所有领域的终极发展方向都是"由繁入简"。

从命令行到图形界面，从手工编码到可视化开发，再到如今的自然语言驱动——技术的真正成熟不是通过增加复杂性，而是通过消解复杂性来实现的。

那些我曾引以为傲的复杂工作流程——精心调教的提示词、层层叠加的插件、环环相扣的模型链——如今都被一个简单对话界面所取代。

从产品思维角度看，这代表了人机交互的本质转变：从"人适应工具"到"工具理解人"。

对创意行业的专业人士而言，这是一个既充满机遇又充满挑战的时刻。

复杂工程化注定会被模型碾碎，但被摧毁的只是表层的技术壁垒，而非创造的本质。当我们不再需要为工具所困，真正的创新才刚刚开始。

**今天的教程就到这里了，如果觉得对你有帮助的话麻烦给个赞👍或者喜欢🩷**
