---
人员: 
  - "[[小胡小胡0009]]"
tags:
  - articles
日期: 2024-09-21
时间: None
链接: https://sspai.com/post/92446
附件: https://cdnfile.sspai.com/2024/09/21/a6efc0268010a90159ad83c09c0f8e7b.JPG)
---
## Document Note

## Summary

利用 Logseq 分类汇总每周的 DailyLog 笔记，使用「记忆卡片」功能复习知识点。

## Full Document
**Matrix 首页推荐**

[Matrix](https://sspai.com/matrix) 是少数派的写作社区，我们主张分享真实的产品体验，有实用价值的经验与思考。我们会不定期挑选 Matrix 最优质的文章，展示来自用户的最真实的体验和观点。

文章代表作者个人观点，少数派仅对标题和排版略作修改。

#### 让记下的笔记真正记得

在上一篇文章 [Obsidian 结合 Logseq 使用的一些心得](https://sspai.com/post/91755) 中，我提到为了方便每周对 DailyLog 笔记复盘，开始使用 Logseq 作为主力笔记软件。终于在结束与 Logseq 的磨合期后，可以开始着手复盘每周的笔记。

DailyLog 形式的笔记缺乏连贯的逻辑，上下文之间缺乏关联，难以形成关联记忆，其次，我践行的是「间歇性日记」记录方法，每次做完笔记相当于把大脑清空，以方便快速进入下一阶段的任务。笔记记录完成的时刻，就是忘记的时刻，如果不复盘，做笔记完全就成了打字练习。

王掌柜在他的[文章](https://sspai.com/post/62414)中提到了打造以写作为目标的知识循环体系来帮助自己学习和记忆，本质上是通过写作将知识内化形成长期记忆，但很多时候我做笔记没那么有目的性，新识得的有趣的知识点或是信息备忘，同样是记笔记的出发点。

因此复盘的目标有二，一是**帮助自己复习笔记的内容**，二是**归纳整理以便于后续的内容输出**。而单靠每周复盘来复习知识点不足以完全记住，我还使用 Logseq 的「记忆卡片」功能定期复习以对抗记忆曲线。

围绕这两个目标，我为自己搭建了两套工作流，一是**自动化分类汇总笔记**，方便自己高效浏览和归纳整理笔记内容，二是**使用「记忆卡片」功能**帮助自己复习记录的知识点。

#### 自动化分类汇总笔记

同样也是因为 DailyLog 笔记缺乏连贯逻辑的原因，直接阅读一周的笔记，会觉得内容碎片化，降低复盘效率。工欲善其事，必先利其器。借助 Logseq 的查询语句，可以**将分散在不同日期的 DailyLog 笔记中、包含相同标签的 Block 自动汇总展示**，我设置了专门的「每周笔记汇总」 page，并在 page 中为每个标签设置独立的查询语句。最终实现效果如下：

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2024/09/21/7a32e0511e2a425d2c97e05e0066cc29.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=e3e98c05d1a14566071c223c90d9402d&referer=https://sspai.com)![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2024/09/21/2a55da137eb323dab1dcf5d01e6f5a8f.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=d318ab4ae404afefa49580e3bc5435bc&referer=https://sspai.com)此外 Logseq 有两个功能配合查询语句非常好用：

**查询的结果可以按照 列表/表格 的格式切换。**需要注意的是表格形式只会展示 Block 的第一行内容；

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2024/09/21/d276d71009f478528735c6dfa83523de.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=6756396077f3e396820abfe9d9a680a6&referer=https://sspai.com)![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2024/09/21/93433fc3621e2fb5eaee14dfb5cafdee.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=55490c65d0fcd434c88062f1477c49ee&referer=https://sspai.com)点击表格内的 Block，会在右侧边栏展示全部内容，并且支持直接在边栏直接编辑。![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2024/09/21/e4d062e49833778075b989637fc54276.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=a9687ed81cb8b5b6a5243ffbe30b7e79&referer=https://sspai.com)##### 为笔记打标签

为了实现自动按标签汇总 Block 的功能，首先要为笔记打标签。打标签的过程，实际上是给笔记内容分类，注意标签应当记录在每个 Block 里，而不是整个页面。我为自己设立的打标签的两个原则是：

* 不管记录的是什么内容，都可以归类到其中一个标签中；
* 可以轻松地将笔记归类到某个标签中，不需要纠结取舍哪个更合适。

以下是我总结的7个标签：

**「生产力」**：提高工作效率、工具使用、工作流程优化等方面的记录和思考。比如：

> 20:34 使用 Safari 将网页保存为 PDF 时，先打开阅读器模式，可以获得效果更好的 PDF #日记/生产力
> 
> 

**「产品思考」**：工作过程中的反思与感悟。比如：

> 20:34 确保自己收到的需求在现有流程中能够获得稳定产出。 #日记/产品思考
> 
> 

**「新知」**：记录新学到的知识、新发现的事物或技术。比如：

> 08:32 高尔夫球场有一类很特殊的人，专门用来记录球员击球的时点，以关闭该次击球的盘口。具体的做法是站在球员身边拿着专用手机，在球员即将击球时按住屏幕，击球结束的瞬间松开，同时赌博网站就会关闭该次击球的盘口。 #日记/新知
> 
> 

**「读书笔记」**：对于长篇文章或者书籍的阅读记录与思考。比如：

> **14:50** [从增量收缩到存量收缩：如何遏制其负效应](https://mp.weixin.qq.com/s?__biz=MzIyMTE0NjExMA==&mid=2649631108&idx=1&sn=84abd008f8423002fb3cdd2fffeb68cf&scene=58&subscene=0) #日记/读书笔记/社会与生活
> 
> * 文章剪藏：[[从增量收缩到存量收缩：如何遏制其负效应]]
> * 文章简介：这篇文章总结了 2024 年 1-7 月国内经济数据，指出了三个趋势：广义财政支出收缩、金融收缩的幅度加快且加大、实体经济收缩，提出未来需要进一步促进内需，但是具体落实非常困难。
> * 个人思考：……。
> 

**「想法与思考」**：一些闪念、自我反思或者对社会现象的思考。比如：

> 19:16 过度得要求自己可能会适得其反，想得太多倒不如先动手做起来 #日记/想法与思考
> 
> 

**「个人管理」**：经过思考之后形成的明确规则，需要自己在日常生活中遵循实施的。比如：

> 17:34 重要文件的归档规则：有电子原件的，直接将电子原件保存在 iCloud 文件夹中；没有电子原件的，通过备忘录 App 扫描成电子版保存。 #日记/个人管理
> 
> 

**「生活日志」**：日常生活中的琐事、个人事务处理、家庭活动等等。比如：

> 09:53 188 的手机号，是在支付宝开通的自动缴费；181 的手机号，是在天翼生活 App 开通的自动缴费 #日记/生活日志
> 
> 

##### 利用查询语句自动汇总

将笔记打上标签后，就可以通过 Logseq 支持的 Datalog 查询语言按标签汇总笔记。因为是每周复盘，我设置的查询语句的内在逻辑是：**检索近7天的 journals page**1**，将包含指定标签的 Block 按照日期降序排列**。虽然是第一次接触 Datalog 语言，但是查询逻辑和 Obsidian 的 dataviewjs、或是 SQL 本质上大同小异，在 AI 的帮助下，不算费力就得到了想要的结果。

###### 查询语句

```
#+BEGIN_QUERY
{:title "每周生活日志"
:query [:find (pull ?b [*])
       :in $ ?start ?today ?tag
       :where
           (between ?b ?start ?today)
           (page-ref ?b ?tag)]
:inputs [:-7d :today "日记/生活日志"]
:result-transform (fn [result] 
    (sort-by (fn [r] 
      (get-in r [:block/page :block/journal-day])) > result))
}
#+END_QUERY
```
上述查询语句可以分为四个部分：

`:title`：查询结果的标题；

`:query`：查询的主要逻辑：笔记需要在7天内，并且包含指定的标签；

`:inputs`：为参数赋值，`:-7d`代表7天前的日期，`:today`代表今天的日期，`"日记/生活日志"`代表要查询的标签，你可以将它修改为你自己的标签；

`:result-transform`：将结果按照日期降序排列。

**在使用 Logseq 的查询语句时，标签中包含的英文字母必须小写**。比如有一个标签是「Things」，写在查询语句里，则需要写为「things」，否则无法识别。

「读书笔记」这个标签下方，我还根据阅读的内容细分了多个标签，比如「社会与生活」「产品能力」「个人管理」等等，以便在复盘时汇总不同领域的阅读数量，与周一设立的目标比较。这里的查询语句略有不同：需要在`contains`中将细分的标签枚举出来。

```
#+BEGIN_QUERY
{
:title "每周读书笔记分类统计"
:query [:find ?tag (count ?b)
      :in $ ?start ?today
      :where
          [?b :block/ref-pages ?p]
          [?p :block/name ?tag]
          [(contains? #{"日记/读书笔记/个人管理"
                        "日记/读书笔记/产品能力"
                        "日记/读书笔记/生产力"
                        "日记/读书笔记/社会与生活"
                        "日记/读书笔记/理财"} ?tag)]
          (between ?b ?start ?today)]
:inputs [:-7d :today]
}
#+END_QUERY
```
#### 借助 Logseq 的「记忆卡片」功能对抗记忆曲线

Logseq 的「记忆卡片」功能与 Anki 一样，针对人的记忆曲线，利用间隔重复的算法来帮助我们形成长期记忆。

在为 Block 添加「card」标签后，Logseq 会以 Block 的内容生成一张卡片。点击左边栏的「记忆卡片」功能，就能看到所有包含「card」标签的 Block 生成的卡片。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2024/09/21/3d21483aa88828b14e15fe667a3506e0.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=37999f821cd3025211a7d28ec7268a7a&referer=https://sspai.com)![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2024/09/21/69e2b8bc4e4bcb36f122a2e6d1fa2b7b.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=b65cc1455903290bb888e3a72be6e0fa&referer=https://sspai.com)完整的抽认卡片实际应包含问题和答案两部分，Logseq 会以 Block 的内容作为问题，以子 Block 的内容作为答案。我省去了设置问题的步骤，选择直接看答案😊。~~不设置问题主要还是因为懒。~~

随着时间推移，包含「card」标签的 Block 会越来越多，不同领域的知识点混杂在一起，复习时混乱且低效。因此在设置卡片时要增加第二个标签来为知识点分类，以便通过标签过滤知识点复习。为此需要做两件事：

###### 添加标签为知识分类

我在为 BLock 添加「card」标签的同时，还会添加一个额外的标签来将知识点归类，比如「areas/术语名词」「areas/生活常识」。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2024/09/21/f632ddf3edb2ff135e4a4ee64edc4f68.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=a575f81fc9788164e0be28575f5796e3&referer=https://sspai.com)###### 借助 card 命令实现分标签复习

`{{cards (and "日记/新知" "areas/生活常识")}}` ，这段命令的意思是：使用同时包含「card」「日记/新知」「areas/生活常识」三个标签的 Block 生成卡片并复习。我在一个独立的 page 为不同类别的知识点提前设置好 card 命令，以便在复习的时候可以直接使用。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2024/09/21/6da45c26bd7737935d00661d8816fea6.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=27af86f3ee0a3beefe5392d79627a207&referer=https://sspai.com)在移动端上同样可以使用「记忆卡片」复习。实际上利用碎片时间，在移动端上复习卡片是非常高效的方式。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2024/09/21/986e39d193f48424117fe1b2c872b2cd.jpeg%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=a1a0c33314b0046a7c713955010d9feb&referer=https://sspai.com)这里必须提到 IOS18 的新功能「保留下载」：支持将文件一直保留在本地，不会因为手机存储空间不够而被自动删除。在此之前，除非我先将 Logseq 文件夹的所有文件手动下载，否则 Logseq App 是无法正确读取所有的文件内容的。这个不起眼的新功能，对我来说反而是 IOS18 最实用的更新。

另一种方式是将同一领域的知识点放在一个 page 中，通过`{{cards [[page名称]]}}`也可以实现针对单个领域的知识点复习，但是这种方式需要专门维护一个独立的 page，无法融到每天记录笔记的工作流中，我没有采用。

#### 一点总结

利用 Logseq 分类汇总每周的 DailyLog 笔记，以及使用「记忆卡片」功能复习知识点，是本文的重点。然而就复盘工作本身而言，尚有两点不足之处，需留待以后持续完善：

1. 如何利用双链和标签等工具，围绕某个主题归纳整理笔记内容，以便后续输出，尚未有成熟的方法；
2. 我会在每周复盘时为下周设立新的目标，这些目标尚显短浅，缺乏长期规划，以及如何评价一周目标的完成情况，也没有有效的机制。

另外我还想介绍一个特定场景下的小技巧：

上文提到我为自己设立了「个人管理」的标签，用来记录日常生活中遵循的规则。随着时间推移，规则会更新和失效，利用 Logseq 的双链可以很好地解决这个问题：

Logseq 的标签同样指向一个 page，在这个 page 中会将包含「个人管理」标签的 BLock 都关联出来。我会将生效中的规则都嵌入到 page 正文中，周复盘时，在正文中嵌入新的规则，更新变更的规则，删除失效的规则，这样就能高效地维护「个人管理手册」，以便后续查阅。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2024/09/21/3104cb4980f2bec0df5d4fa653b9ca07.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=575db1b6b723bd3e2a01e44201c87fd3&referer=https://sspai.com)> 关注 [少数派小红书](https://www.xiaohongshu.com/user/profile/63f5d65d000000001001d8d4)，感受精彩数字生活 🍃

> 实用、好用的 [正版软件](https://sspai.com/mall)，少数派为你呈现 🚀
