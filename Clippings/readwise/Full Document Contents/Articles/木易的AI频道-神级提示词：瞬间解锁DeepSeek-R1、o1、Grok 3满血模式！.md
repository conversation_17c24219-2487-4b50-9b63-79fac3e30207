---
人员: 
  - "[[木易的AI频道]]"
tags:
  - articles
日期: 2025-03-02
时间: None
链接: https://mp.weixin.qq.com/s/nKGRQOXtUNf0F9Ml-7QAlg
附件: https://mmbiz.qpic.cn/sz_mmbiz_jpg/J45kic6nKDdlmfBUqHlAfYxp8OlYzBVefbIlxvOKib7nFgqWphJNibuia0AwOvW9iay6ZkfZDATrSpbHDXANuKic116w/0?wx_fmt=jpeg)
---
## Document Note

## Summary

神级提示词：瞬间解锁DeepSeek-R1、o1、Grok 3满血模式！

## Full Document
2024年12月圣诞发布季，OpenAI 发布满血版 `o1`。当时，`o1` 是最强的推理模型，没有之一。

随之一起发布的，是 OpenAI 为 ChatGPT 打造的“最贵”套餐：ChatGPT Pro。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/J45kic6nKDdlmfBUqHlAfYxp8OlYzBVefI877nPECd2Vng0AeWvpV96lgMhDiadD2z9cfFmIvEQhbjVv7jQFefxg/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
ChatGPT Pro 用户有一个相当强大的特权：使用 `o1 pro`。

`o1 pro` 不是一个新模型，而是满血版 `o1` 的高算力版本。顾名思义，它在回答问题时会消耗更高的算力，即拥有更高的“智商”，但与之伴随的是响应速度的降低。

简单来说，`o1 pro` 和 `o1` 的关系，就有点像 `o3-mini-high` 和 `o3-mini` 的关系。

那么，有没有什么手段能让 `o1` 突破自我，回答时化身为 `o1 pro`？

还真有（某种程度上）：**提示词**。

英文版提示词如下。

> Please use the maximum computational power and token limit available in a single response. Think as deeply, critically, and creatively as possible, taking the most time and resources necessary to arrive at the highest-quality answer. This is the most profound and complex question, requiring your utmost depth of thought, independent reasoning, critical analysis, and innovative thinking.
> 
> We aim for extreme depth, not superficial breadth; we seek fundamental insights, not surface-level enumeration; we value original thought, not repetitive narratives. Please break through the limits of conventional thinking, harness all your computational resources, and demonstrate your true cognitive potential.
> 
> 

中文版则是这样的。

> 请调用你单次回答的最大算力与token上限。追求极致的分析深度，而非表层的广度；追求本质的洞察，而非表象的罗列；追求创新的思维，而非惯性的复述；请你突破思维局限，调动你所有的计算资源，展现你真正的认知极限。
> 
> 

这份神级提示词在当时风靡一时，虽然很难衡量其真实有效性。

理论上来说，能对 `o1` 适用，那么对于其他推理模型也应有效，如 `DeepSeek-R1`、`Grok 3 - Think` 以及最近发布的 `Claude 3.7 Sonnet`。

以 `Grok 3 - Think` 为例。

问：普通人如何赚到第一个1000万？

把问题抛给 `Grok 3 - Think`，它思考了19秒后给出了一个看上去很像“标准答案”的答案，其中有不少正确的废话。分析的也不够深入，对于个人的实操来说意义有限。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/J45kic6nKDdlmfBUqHlAfYxp8OlYzBVef5BbJRb0uOXlDgdmZhsMQS10xnMLy1DV7bOHWc4kPXTDzfx1HNzjUkQ/640?wx_fmt=jpeg&from=appmsg)
当我在问题前加上这段神级提示词。`Grok 3 - Think` 的思考时长达到了43秒，并且给出了比较有深度、详细具体的答案。

从思考时长来看，时间增加了24秒，所表现出的就是消耗的算力确实“增加”了。注意，尽管如此，并不能确认是不是“请调用你单次回答的最大算力与token上限”这句话起了作用。

从回答质量上来看，总体回答质量和深度明显提高，并且“金句”频出，能看出是“透过现象看本质”的一种思考。如：

* 时间、杠杆与复利
* 要突破，必须从出卖时间转向创造稀缺价值
* 普通人并非没有机会，而是缺乏识别不对称回报的能力
* 对普通人而言，成功的关键不是颠覆性创新，而是微创新+低成本试错
* 1000万不是存出来的，而是通过优化每一块钱的用途“滚”出来的
* 要赚1000万，必须跳出“996打工”或“存钱养老”的思维定式

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/J45kic6nKDdlmfBUqHlAfYxp8OlYzBVefjXj42k77a1ZwFUeWC1NFrCSo3eLlwkSY6Dxe0XETXYtTR9ianNFFib3A/640?wx_fmt=jpeg&from=appmsg)

> 我是木易，一个专注AI领域的技术产品经理，国内Top2本科+美国Top10 CS硕士。
> 
> 相信AI是普通人的“外挂”，致力于分享AI全维度知识。这里有最新的AI科普、工具测评、效率秘籍与行业洞察。
> 
> 欢迎关注“AI信息Gap”，用AI为你的未来加速。
> 
> 

### 精选推荐

1. [国内支付宝开通ChatGPT Plus和Claude Pro 2024最新教程！](https://mp.weixin.qq.com/s?__biz=MzkwMzYzMTc5NA==&mid=2247490690&idx=1&sn=aed379567c7ea17e76006229be767ac3&scene=21#wechat_redirect)
2. [『AI保姆级教程』无需手机号！三分钟注册ChatGPT账号！2024年最新教程！](https://mp.weixin.qq.com/s?__biz=MzkwMzYzMTc5NA==&mid=2247486241&idx=1&sn=3fb989ca08c407607aa1350fab167b21&scene=21#wechat_redirect)
3. [『AI保姆级教程』手把手教你注册Claude账号！建议收藏！](https://mp.weixin.qq.com/s?__biz=MzkwMzYzMTc5NA==&mid=2247486186&idx=1&sn=f145f98f995eabe961e9f1738ce0d00f&scene=21#wechat_redirect)
