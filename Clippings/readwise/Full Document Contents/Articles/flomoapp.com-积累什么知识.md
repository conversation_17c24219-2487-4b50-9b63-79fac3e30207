---
人员: 
  - "[[flomoapp.com]]"
tags:
  - articles
日期: None
时间: None
相关:
  - "[[教育]]"
  - "[[生活方式]]"
  - "[[生产力与自我提升]]"

链接: https://help.flomoapp.com/thinking/area.html
附件:
---
## Document Note

## Summary

本文探讨了个人知识管理中“积累什么知识”的重要性，提出了选择领域、项目和资源的三重框架。首先，领域是我们需要持续精进并为之承担责任的空间，项目是有明确目标和时间的活动，而资源是对我们个人兴趣的知识积累。选择合适的领域至关重要，因为这决定了我们在生活和工作中的发展方向。

在知识的积累过程中，清晰的目标和方向能帮助我们避免精力分散。许多人在完成项目时，往往忽视了与自己专长领域的长期关联，从而导致与目标逐渐偏离。因此，有必要记录项目经验，以便从中提炼出对领域的贡献。

此外，作者强调，真正的精进不仅依赖于消耗知识，还需要通过实际创造和项目实践来实现。选择那些让自己痴迷且对他人有价值的领域，能够激励我们持续投入，并为社会创造财富。

最后，面对个人的内心和未来，我们需勇于放弃无意义的关注，明确自身的兴趣和目标，以便在知识的海洋中找到自己的航向。

---

**[[问题]] 1：** 什么是个人知识管理中的领域、项目和资源？

[[答案]]：领域是我们需要精进并承担责任的事务空间，项目是有明确时间和目标的活动，而资源是个人兴趣相关的知识积累。

**[[问题]] 2：** 为什么选择合适的领域对个人发展至关重要？

[[答案]]：选择合适的领域决定了我们的发展方向和成长空间，能够避免精力分散并帮助我们更有效地积累知识。

**[[问题]] 3：** 如何通过项目来精进领域？

[[答案]]：通过实际参与项目并记录经验，我们可以从中提炼出对领域的贡献，实现知识的真正积累和应用。

## Full Document
在个人知识管理中，最难回答的一个问题是**该积累什么方面的知识。**

我们在世界上生活，往往有多种不同的身份，这也导致我们需要积累的知识看起来五花八门：作为一个产品设计师，需要积累设计相关的知识；带领一个小团队，需要积累管理方面的知识；刚有了宝宝，需要积累婴儿发育的知识；喜欢研究中国历史，需要了解各种正史野史；最近想买一辆汽车，需要积累汽车相关的知识 ……

这些知识看起来都有「用处」，但是如果不搞清楚优先级，那么就像一艘没有目的地的船只，虽然能停靠无数个岛屿，补充许多给养，却不知道要航向何处。

所以如果不想清楚自己该在哪些方面投入，那么再好的笔记工具也无法解决知识管理混乱的问题。

#### [#](https://help.flomoapp.com/thinking/area.html#换种视角看知识-领域、项目、资源) 换种视角看知识：领域、项目、资源

想象一下，将你大脑中所有的知识平铺在一张纸面上，大概会是什么样子？

我曾在刚工作的时候做过一次这样的实验如下图。能看到其中涉及的面非常广杂，有具体如招聘面试该怎么做；也有宽泛如自我认知是什么；有自己感兴趣的经济学原理；也有日常吃饭的家伙视觉设计；更有别人推荐但是自己一知半解的不确定要不要学的金融知识。

![https://resource.flomoapp.com/101/image-65.png!webp](https://resource.flomoapp.com/101/image-65.png!webp)
如果按照这种视角，那么就会被浩如烟海的知识所淹没，即使引入再多的科学分类法也不能解决问题。让我们换个角度来看上面这些知识：

* 将那些做了对别人有帮助，做砸了自己要承担责任的事情，**称为领域（Area）**
* 将那些有明确起止时间和目标的事情，**称为项目（Project）**
* 将那些自己持续感兴趣，但对别人没影响，别人也不在乎的事情，**称为资源（Resource）**

举个生活中的具体例子：保持身体健康是一个领域，因为身体坏了你需要为此负责，并且会影响家庭的收入，也会耽搁自己的工作。跑一个十公里马拉松锻炼，是一个项目；而周末去游泳池游十圈，则是另一个项目。我们通过一个又一个的项目来完成健康领域的精进。而你刚迷上户外运动，收集了许多资源准备研究看看哪个合适自己，则是一个新发展的兴趣。即使研究下来发现不适合自己，也没有什么大的影响。

根据这个视角再把上面例子中所需要的知识进行分类：

* **领域：** 视觉/平面/交互设计、数据分析、营销与推广、行为经济学 —— 这些都是自己日常工作吃饭的家伙，做好了对公司和用户都有好处；做砸了往往会绩效不及格或者被劝退，需要自己为此负责。所以在这些事情上我们持续不断精进，持续积累。
* **项目：** 招聘面试、团队协作 —— 这两个都是当时招募暑假实习生遇到的具体问题，需要在暑假开始前制定好招聘计划和要求，以及设定好团队协作的方式，有明确的目标和截止时间。完成这些项目既需要我们补充相关知识，也会在过程中产生许多属于自己的知识。
* **资源：** 历史、信息论、微观经济学、自我认知 ——这些都是自己持续感兴趣，有空就会去翻翻相关内容满足好奇心的事情。即使停下来或者做砸了，也没什么大的问题，因为本来就没有指望它们有什么明确的回报。所以这些知识的积累虽然令人愉悦，但并非是最高优先级。

所以大抵来看，我们日常需要积累的知识多半来自于自己关注的领域、资源、项目。而其中最重要的，则是领域的选择 —— **因为领域是让我们在这个世界上立足的根本。**

![https://resource.flomoapp.com/101/image-65.png!webp](https://resource.flomoapp.com/101/area001_resize.jpg!webp)
#### [#](https://help.flomoapp.com/thinking/area.html#何谓领域-如何选择) 何谓领域，如何选择

所谓领域，便是我们需要为此不断精进，需要承担责任，带来正外部性效应的事务空间。

举个例子，任何成年人都或多或少的需要对一些领域负责，在公司工作，你的岗位就是要不断精进的领域；成家立业后，家庭理财领域也需要你持续关注。这些领域一旦掉链子，就会造成很多麻烦。

但在领域的选择上往往会有几种困难，一种是传统教育带来的「标准答案」心态，总是试图在找到大多数人认可的方向，而没有听从自己内心选择；另一种是太过贪心，试图学习所有能学到的东西，但由于精力分散导致样样稀松；还有一种是固步自封，没有意识到自己关注的领域已经过时或者不再重要。

而如果你希望能在这个世界上留下点什么，那么选择哪些精进的领域，就非常重要了。硅谷教父 Paul Graham 曾在《[天才的车票理论  (opens new window)](https://www.notion.so/8b9a0810bed7427f937f95eab10d95cf)》一文中提到下面两个判断标准：

* 是否对此特别痴迷，即使没有任何回报也想做下去。
* 这件事是否对别人有帮助，还仅仅是满足自己。

**即分清楚你痴迷的领域是以消费为主，还是以创造为主。**

痴迷意味着你的能力跟得上，也不需要耗费很大的决心就能持续不断地干一件事，只有这样才能捕捉到别人关注不到的机会，带来更大的回报；而对别人有帮助，则意味着你在为社会创造财富。痴迷玩电子游戏的人很多，但能设计出来马里奥的只有宫本茂；痴迷互联网的人很多，但设计出来维基百科的人只有Jimmy Wales 和 Larry Sanger。

我在 2016 年意识到自己就属于上述所说贪心的人，样样稀疏没有积累。于是便决定放弃对写代码、组织管理等领域的研究，专心开始研究服务设计相关的领域。

这个决策一方面是自己痴迷于研究服务设计，相比于产品设计涵盖的范围更大；另一方面也是时代变化导致传统产品经理的领域式微 —— 当你点的外卖无法准时送达时，再好的 App 体验也无法弥补。所以如果能设计出好的服务体系，会对自己的产品甚至同行都有巨大的帮助。

![](https://resource.flomoapp.com/101/area007_resize.jpg!webp)
不过这并不是终点，尔后的这些年，自己关注的领域也在随着时代的变化和个人的选择而持续变化。（延伸阅读：[用 flomo 记录一年后，自己发生了什么变化  (opens new window)](https://mp.weixin.qq.com/s/9A_XLmUTRAYRKtfnow3_Rw)）

**但不变的是，去选择让你痴迷又对别人有价值的领域。**

#### [#](https://help.flomoapp.com/thinking/area.html#领域的精进与支撑) 领域的精进与支撑

领域、资源、项目并非是彼此割裂的，在我们选择好想要专精的领域后，可以再来看三者之间的关系。

领域通过一个又一个的项目来不断精进，而兴趣则随着时间的投入有可能转化为领域，而有些领域则会随着时空的变化而过时。只有领域没有项目，只是纸上谈兵，谈不上任何精进；只有项目没有领域，像是盲人摸象，不得要领；

你不可能通过别人的描述就知道苹果的味道，必须自己尝一尝。那么你也不可能通过收藏和阅读大量别人的文章，来让自己关注的领域精进 —— 领域的建设不能只有消费，还要有建设。

假如你对写作领域很感兴趣，精进的方法不能只有阅读，还需要通过创造许多得到别人认可的文章来达到。这个创造的过程，其实就是一个个具体的项目 —— 它们有明确的目标（发布在哪里），有明确的时间点（什么时候交稿），以及明确的标准。

值得注意的是，领域的精进并不取决于你做了多少项目，而是从项目中积累了哪些具体的经验和事实。所以不应该用 flomo 来管理项目进展，而是应该在 flomo 尽量记录从项目得到的事实和经验，来让自己关注的领域不断精进。

以我关注的 flomo 的运营领域为例，曾经记录过之前「建立社群运营规则」项目的一些经验：

* 许多人都说维护用户群特别麻烦。但在任务进行过程中，发现只要将群控制在 200 人以内即可自动扫码加群，这样就无需耗费人力添加好友，再拉到群内。
* 许多人都很关注社群粉丝的数量而不是理念。但在任务进行中发现，许多人会因为看到 flomo 不做待办清单、不做文档编辑的理念而加入社群，所以意识到理念比功能更能获得认同。

所以完成对应的项目并非是结束，如果不及时记录这些事实或者经验，那么项目的成效就会大打折扣，对领域的贡献就会低了许多。而如果你能持续不断记录，一张张的卡片，就能很好地证明在所在领域不断地精进。

![持续不断记录的运营项目心得和思考。](https://resource.flomoapp.com/101/area003_resize.jpg!webp)
持续不断记录的运营项目心得和思考。

我们经常爱收藏的文章、视频、金句等内容，许多时候都是资源而非领域的范畴。

但基于精力分配和知识的形成，不推荐跨太多领域收集资源，这样要么会造成精力分散，要么造成资源坟场。**更好地方法是，基于领域 （或者将来潜在的 Area）作为精进的方向，然后把资源当做领域的基石和养分**，不断地为未来的可能性做准备。

比如开始做 flomo 之前我就对个人知识管理，甚至是人类如何学习等方面很感兴趣，收集整理了大量的文章资源。而开始做 flomo 之后，这些兴趣就变成了自己需要不断精进的领域，而这些资源则成为了养料不断滋养着 flomo，也才有了诸位正在阅读的这篇文章。

![曾经作为资源的知识管理，现在已经挪至领域下面。](https://resource.flomoapp.com/101/area004_resize.jpg!webp)
`曾经作为资源的知识管理，现在已经挪至领域下面。`

![Untitled](https://resource.flomoapp.com/101/area002_resize.jpg!webp)
许多时候我们看似在完成许多项目，但这些项目和自己专精领域缺少长期的关联。比如对一个想要在编程领域成为专家的程序员来说，虽然管理团队看起来是「进步」，但这对于他「成为编程专家」的领域并没有什么帮助。当这种事情变多的时候我们就会发现，项目完成的越多，反而距离我们想要到达的地方越远。

同样，如果你的 flomo 中如果充斥着大量剪藏的来的内容和别人的观点，却从未对你的项目和领域有所帮助，那么要提醒下自己是不是关注的范围太过宽泛，或者自己设定的领域根本不是自己痴迷或者对别人有帮助的。

请确保他们在一个方向上。

#### [#](https://help.flomoapp.com/thinking/area.html#结语-对自己坦诚) 结语：对自己坦诚

**理清领域、找到和资源、项目的结合，最难的在于，你要坦然面对自己的内心。**

虽然我们最终都会死亡，但是距离死亡还是有很长的而时间，你可以有两种方式来面对：一种是让自己和一些宏大的东西联系起来，比如写一本书，建立一家公司，研究一种美食，持续帮助一些人；另一种是得过且过，不考虑过去和未来。

当你找到内心认可所要精进的领域，放弃那些无需关注的领域，积累什么方面的知识，也就逐渐清晰了。

如 Omar Bradley 所说：**按照星星，而不是按照过往船只的灯光设定航向。**

![图片来源：Quote Fancy](https://resource.flomoapp.com/101/area006.jpg!webp)[图片来源：Quote Fancy](https://quotefancy.com/quote/879313/Omar-N-Bradley-Set-your-course-by-the-stars-not-by-the-lights-of-every-passing-ship)
