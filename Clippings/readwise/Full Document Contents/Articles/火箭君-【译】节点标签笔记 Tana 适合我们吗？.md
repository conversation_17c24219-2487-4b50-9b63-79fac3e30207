---
人员: 
  - "[[火箭君]]"
tags:
  - articles
日期: 2023-04-15
时间: 2024-10-21 10:24:54.118628+00:00
相关:
  - "[[Bullet Journaling]]"
  - "[[Daily Note]]"
  - "[[Notion]]"
  - "[[Obsidian]]"
  - "[[Roam]]"
  - "[[Schema]]"
  - "[[Tabs]]"
  - "[[Tana]]"
  - "[[<PERSON><PERSON><PERSON><PERSON><PERSON>]]"
  - "[[交叉引用]]"
  - "[[信息]]"
  - "[[列表]]"
  - "[[卡片]]"
  - "[[卡片视图]]"
  - "[[参考节点]]"
  - "[[双向链接]]"
  - "[[双链笔记]]"
  - "[[反向链接]]"
  - "[[回车键]]"
  - "[[子节点]]"
  - "[[工作空间]]"
  - "[[库]]"
  - "[[待办事项]]"
  - "[[数字花园]]"
  - "[[数据库]]"
  - "[[数据结构]]"
  - "[[文件夹隐喻]]"
  - "[[日历]]"
  - "[[标签]]"
  - "[[根目录]]"
  - "[[概念]]"
  - "[[模块化笔记]]"
  - "[[看板]]"
  - "[[科技]]"
  - "[[笔记园丁]]"
  - "[[笔记工具]]"
  - "[[笔记建筑师]]"
  - "[[缩进键]]"
  - "[[节点]]"
  - "[[视图]]"
  - "[[超级标签]]"
  - "[[非线性]]"

链接: https://mp.weixin.qq.com/s/S0PvxP4yvInhg_VDTVa0DQ
附件: https://mmbiz.qpic.cn/mmbiz_jpg/hQibibdG339M0EPSYc6K1ODgWVsbtuzTzpPJS59zXbU77K3xXyzmjytEUtL4w8nvykjANILYo6iaE3yPnOCouunlQ/0?wx_fmt=jpeg)
---
## Document Note

## Summary

Tana 是一款新兴的笔记工具，其核心理念是使用“节点”作为基本单位，用户可以通过标签和衍生子节点创建多维视图。与传统的双链笔记工具不同，Tana 更加注重结构化和网状链接，受到“笔记建筑师”和“笔记园丁”两类用户的青睐。Tana 允许用户创建灵活的结构，而不是过度结构化，支持多种视图展示，如看板、列表和日历等。

对于“笔记建筑师”，Tana 提供了方便的创建结构的方法，可以交叉引用数据。而“笔记园丁”则可以轻松链接想法，实现网状数据结构。Tana 的设计允许用户在不依赖关键词检索的情况下，依然能确保笔记之间的关联，避免笔记孤立。

此外，Tana 通过唯一标识符和反向链接功能，帮助用户在节点间建立简单的连接，便于信息的非线性呈现。用户可以轻松缩放视图，创建基于列表的无限画布，并在不同的可视化形式中查看相关节点。这种设计旨在减少用户在使用过程中可能遇到的迷失感。

总体而言，Tana 以其简单易用的操作和强大的结构化能力，适合需要灵活记录和管理信息的用户。

---

**问题 1：**  
Tana 的核心概念是什么？  

答案：  
Tana 以“节点”为基本单位，通过标签和衍生子节点创建多维视图，强调灵活性和网状链接。

**问题 2：**  
Tana 是如何满足不同用户需求的？  

答案：  
Tana 适合“笔记建筑师”通过灵活创建结构和交叉引用数据，适合“笔记园丁”通过轻松链接想法实现网状数据结构。

**问题 3：**  
Tana 的反向链接功能有什么作用？  

答案：  
反向链接功能通过唯一标识符连接节点，帮助用户在信息之间建立简单的关系，促进非线性信息的呈现。

## Full Document
### Image

Tana 是一款新兴的笔记工具，火箭君之前有过介绍。

Tana 和以往的「双链笔记」或者「模块化笔记」不同，Tana 主打「节点」作为笔记的基本单位，在节点基础上执行 标签以操作 及 衍生子节点，最终形成各种维度的视图。

Tana 的理念是新颖的，在某些领域也是实用的，尤其受到一些酷爱「结构化」以及热爱「网状链接」用户的喜欢，但前卫的理念也受到传统文档用户的微词，比方说，感觉结构很乱，不像阅读文章那么顺畅。

之前火箭君说过，Tana更像是一个 伪装成笔记工具的数据库。他们家自己也称 Tana 为 Everything OS，雄心可见一斑。

火箭君也一直在想，Tana 肯定是迎合了某种思路，究竟是什么让 Tana 与众不同？今天的译文或许能给我们一点启发。

以下译文来自于：Clayton Miller ，火箭君做了编辑和节选。原文地址：https://medium.com/@curiousee/is-tana-right-for-you-feb02c9c95a1

Clayton 将用户分为：

重视结构和架构的用户， 以下简称：笔记建筑师

数字花园园丁类的用户，以下简称：笔记园丁

正文

Tana。是一个令人兴奋的选择。它位于「笔记建筑师」 和 「笔记园丁」 用户之间。

对于「笔记建筑师」来说，Tana 是一个很好的选择，因为我们可以轻松创建一个结构，而不会过度结构化或导致不灵活。

就像在 Notion 中一样，我们可以拥有共享关系的数据库，这样就可以轻松地交叉引用数据和信息。任何节点都有能力形成自己的数据库。并且可以在许多不同的视图中显示，例如看板、列表、日历、Tabs 和卡片。

![[Attachments/14e9e7c14a34320ea1fe86f7acd4eaaf_MD5.webp]]
![[Attachments/e57d81bd3f14a209bbfde269d3155441_MD5.webp]]
对于 「笔记园丁」 来说，Tana 也是一个很好的选择，因为链接想法变得很容易。

作为一名纯粹的数字花园园丁而言，数据结构是扁平的，而不是分层的，因为没有一个主题比另一个主题更重要。这是一个网状的数据结构。

此外，Zettlekasten 系统的语境下，即使关键词是松散的，笔记在某些方面仍然可以具有自己的生命力。（火箭君注：即使不依赖于关键词检索，也不会导致笔记变成孤儿或打进冷宫，重要有链接索引存在的话）  

尽管笔记仍然存在一些固有的层次结构，但要点是，层级结构不是系统中重要的方面，或者分类层级只是更好描述笔记的一个关键词（火箭君注：分类就是一种标签）。

综上所述，由于反向链接和标签很容易在 Tana 实现，我们就可以创建查询来表达各种想法。

设想，我们有一个节点；基本构建笔记块是通过在 bullet point（火箭君注：Tana 类似大纲笔记，从一个子弹节点开始输入）中键入内容来创建的。每个节点都是它自己的实例，系统赋予节点唯一的编号。我们可以通过输入和缩进开始创建后续的基本结构。

![[Attachments/368a17914d111b1d7c2442b97685ace3_MD5.webp]]
因此，Tana 是一个 「节点宇宙」，我们可以从一个节点生成更多的节点， 也可以嵌套节点，引用节点。所有这些节点都可以成为它们自己的数据库，其中包含字段、查询和其他元数据。通过使用超级标签，这些节点可以被转换为预定义的模板视图。

在 Tana 中，默认情况下，有这样的结构体系：工作空间 / 根目录 / 日历 + 库 + Schema。

* 日历：我们创建的大多数节点都将在日历中有位置，因为有类似 Daily Note的机制。
* 库：这个机制让我们把节点存储在您希望轻松找到它们的位置。
* Schema ：这是默认存储超级标签的地方。

我们需要做的就是开始输入每日节点并忘记复杂性。

![[Attachments/9139336a393eb582c207904f38863865_MD5.png]]
Tana 摒弃了典型的文件夹隐喻，转而使用节点。如我们所见，节点是系统中传递信息的一种方式。当记录开始变得更复杂时，关系或链接就会出现。那也是基于文件夹的结构分崩离析时。由于文件夹隐喻使用树结构。当从层次模型切换到关系和网络模型时，信息开始以非线性方式连接。

![[Attachments/63c3808bd26088e64b9ec98326f9513e_MD5.png]]
由于 Tana 中的每个节点都有一个唯一的标识符，您还可以将它链接到任何其他节点。这会创建一个反向链接，也就是创建了一个参考节点，我们可以在其中看到这条笔记与哪条笔记相关。

双向链接在这条笔记和那条笔记之间搭建了一座简单的桥梁，因为单击一次会将我们带到参考卡片，而另一次单击会将我们送回当前笔记。

这些节点的组合，让我们可以根据所需的上下文级别进行放大和缩小。设想我们正在创建一个基于列表的无限画布，当然上面有列表，并且可以将列表嵌套在列表中。我觉得这是一种减少复杂混乱的简单方法。

在Tana中，我们可以在 列表、看板、卡片视图或待办事项中查看这些相关节点。因此，可以轻松地以不同方式可视化您的数据。（火箭君注：其实不是 graph 或 图形化 的那种意义的「可视化」）

当我尝试使用 Obsidian 和 Roam 这样的应用程序时，最重要的阻力来自于容易迷失在无穷卡片中，缺少方向感和位置感。虽然 Obsidian 和 Roam Research 也是非常强大的应用程序，带有大量插件和很棒的社区。Tana 则帮助我很好的克服了这种阻力。

最后，让我们再看一下，Tana 看似复杂逻辑之上简单的操作：

* 我打开 Tana 并开始写作。面对的是 Bullet Journaling（子弹笔记） 中每日记录的那些基本概念。
* 当我想扩展一个概念时，我只要按下回车键和缩进键

一切就是那么简单。

![[Attachments/a816e73204da581daa4d24653178bd54_MD5.webp]]
