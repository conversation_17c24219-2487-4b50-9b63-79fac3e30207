---
人员: 
  - "[[知乎专栏]]"
tags:
  - articles
日期: 2022-11-09
时间: None
链接: https://zhuanlan.zhihu.com/p/581615378
附件: https://pica.zhimg.com/v2-8ec75c227d1d28a3fdf069dc792bf893_720w.jpg?source=172ae18b)
---
## Document Note

## Summary

让笔记流动起来！记笔记？这个太简单了，每个人似乎都会。但是你发现了吗？很多人记的笔记不仅一团糟而且质量很低，甚至他们自己都不愿意再次打开。 关于记笔记，我们已经全面介绍了记笔记的常见笔记记录方法、笔…

## Full Document
> 让笔记流动起来！

记笔记？这个太简单了，每个人似乎都会。但是你发现了吗？很多人记的笔记不仅一团糟而且质量很低，甚至他们自己都不愿意再次打开。

关于记笔记，我们已经全面介绍了记笔记的常见笔记记录方法、笔记记录误区及对应笔记策略、笔记组织管理方法。

这些关于笔记记录的文章，多数倾向于笔记技术（器），而对于笔记记录理念探讨较少。

**在传统操作中，我们记完笔记之后，笔记就静静躺在编辑器中，等待着我们的再次查看、检索和阅读**。这样的笔记是静态的。

在我看来，我们的笔记内容应该随着我们知识、经验、灵感的增加而逐日更新和提升。如此，笔记质量才能逐步提高。

为了让笔记流动起来，建立动态的笔记质量提升体系，今天介绍两种有效的笔记记录理念和方法——常青笔记和渐进式归纳。

#### 常青笔记

常青笔记是 **Andy Matuschak** 所提出的笔记记录理念。常青笔记的提出目的主要是渴望积累个人知识，而不是简单地获取信息。

![](https://pic4.zhimg.com/80/v2-2310f04e4de8ba01b6c4f297625d73d3_720w.jpg)
##### 何为常青笔记？

##### 常青笔记应该是原子化的

每个笔记应该只记录一件事情。如果笔记中的内容过多，那么笔记结构可能会比较混乱。

如果笔记中的内容过少，那么笔记则可能过于碎片化和松散，进而不容易建立笔记之间的联系。

![](https://pic3.zhimg.com/v2-30270b14f1188d76d4733bab814f15aa_r.jpg)
笔记标题应该是简明扼要的陈述、说明、描述

记笔记不宜过长也不宜过短，最好是原子化的。如果笔记超过一个以上的核心概念，那么最好拆分笔记。

至于具体取舍，需要笔记记录者加以判断和决定。

![](https://pic2.zhimg.com/v2-b501be616180e197877a2e18d79dade5_r.jpg)原子化
##### 常青笔记应该是基于概念导向的

概念相当于知识大厦的基石。记录笔记以概念为核心的话，方便以概念为基础，方便将各种旧概念、新概念、相关概念加以链接，建立知识体系之间的关联。

具体记录笔记时，我们的笔记标题应该围绕核心概念，使用一句话概括具体的笔记内容，让人一眼便知道我们的这段笔记的核心思想。

![](https://pic1.zhimg.com/v2-bd86ba2785379758373fc84f6d059bc0_r.jpg)
##### 常青笔记之间应该紧密关联

学习到各种碎片化的知识点并没有多大的意义。真正的学习应该是建立知识体系。

因此，在记录常青笔记时，应该强迫自己思考不同概念之间的关系，想办法将这些概念关联起来，通过概念地图勾勒起整个知识网络。

在具体使用中，我们的笔记应该至少与另外一条笔记相互关联，并且应该经常回顾笔记，根据需求将更多地笔记加以连接。

![](https://pic4.zhimg.com/v2-ec32525d61d0787ad1d9e43a0b641e73_r.jpg)
##### 常青笔记的组织应该是自然呈现而不是规划出来的

在使用文件夹体系或者标签体系时，如果一开始我们建立了过多的分类，这反而会让我们经常纠结我们所做的笔记应该放在哪个分类之下，而不是集中精神进行思考。

一开始对于分类的规划会阻碍我们的思考，并且很有可能不符合我们的使用场景需求。

因此，在常青笔记中，鼓励用户随着自己的使用需求，在需要的时候再进行管理笔记，让笔记管理体系随着使用场景而逐渐呈现出来，而不是进行刻意管理。

![](https://pic2.zhimg.com/v2-feb967b97343c3a03fd4cd2b035af611_r.jpg)自上而下 VS 自下而上
##### 为什么要记录常青笔记呢？

##### 常青笔记促使人们将记笔记作为作为一种思考方式

以往大多数人所记录的笔记往往是无效的：很多笔记用户去考虑如何记录更好看的笔记、如何组织笔记。殊不知，问题在于记笔记并不是我们的目标。

##### 不应该为了记笔记而记笔记

记笔记最核心的意义在于推进人们更好地思考。常青笔记的理念要求，能够促使笔记用户不断思考概念本身以及知识网络，不断通过记笔记训练我们的思维方式。

##### 常青笔记方便人们更好地组织笔记

常青笔记主张笔记内容原子化、概念导向以及关联笔记，为高效组织笔记提供了基础。

以原子化笔记为笔记的基本单位，以及以概念导向记录笔记，这赋予了笔记组织的灵活性，而不是需要过度纠结笔记的分类问题。而建立笔记之间的关联，会让笔记按照主题相互关联，建立知识网络体系。

![](https://pic3.zhimg.com/v2-dcb5d44b633900b61626da030c367af2_r.jpg)Andy Matuschak 的常青笔记首页
##### 常青笔记的记录方式更符合我们知识增长路径

常青笔记主张对笔记内容进行反复回顾、总结、反思，以此保障笔记内容的常青。这在很大程度上建立了知识积累的有效方式。

此外，我们洞察力的提升并非一朝一夕，而是建立在某个主题的日积月累。而通过常青笔记的记录习惯，可以保障我们某个主题进行长期思考。

![](https://pic1.zhimg.com/v2-44664c6204cd5fcc185c68351a729550_r.jpg)
#### 渐进式归纳

##### 为什么需要渐进式归纳？

在七月份阅读《卡片笔记写作法：从阅读到写作》这本书后，根据卢曼所提出到卡片盒笔记法（ZettelKasten笔记法）理念对我的笔记系统进行了重新设计。

具体来说，**卡片盒笔记法**将笔记区分为三种类型：**闪念笔记（Fleeting note）、文献笔记（Literature note）和永久笔记（Permanent note）三种类型**。

所谓**闪念笔记**，即自己的飞逝的灵感、想法等，属于临时笔记。

**文献笔记**则是我们阅读书籍、期刊等文献资料的所读所想，将知识、理解、感悟加以记录。

对于一些闪念笔记和文献笔记，如果只是和某个项目相关（也可以称为**项目笔记**），那么等待项目结束，这些笔记便可以存档或者删除。

**永久笔记**是对于闪念笔记和文献笔记的深度挖掘，将其中与项目相关的笔记剔除掉之后，将那些与自己已有知识体系相关、且经过时间沉淀和深度思考的笔记内容。

![](https://pic1.zhimg.com/v2-f81db9da31bcfba0764ca8738fba96e4_r.jpg)
可以看到，从闪念笔记到永久笔记，这三者之间是自下而上、层层递进的关系。

在我看来，卡片盒笔记法提供了一种真正关于笔记记录的动态工作流。

现在面临两个问题：**在卡片盒笔记中，如何从闪念笔记和文献笔记向永久笔记进行过渡？另外，单条笔记如何记录？**

关于第一个问题，常青笔记已经给出了答案。而关于第二个问题，答案是渐进式归纳。

##### 如何进行渐进式归纳？

渐进式归纳，英文为 Progresssive Summarization，也可以翻译为**渐进式总结**，这是著名生产力专家 Tiago Forte 所提出的笔记记录方法。

Forte 认为，在当今的信息化社会中，真正的挑战不是获取知识，而是找到知识。

**绝大多数人笔记记录存在的主要问题，便是难以在需要的时候快速找到自己所需要的内容**。

实现这一目标，一款拥有强大检索能力的笔记软件固然重要，但是依然不能解决如何在众多笔记信息中快速提取信息和知识的目的。

Forte 建议为笔记内容建立个人信息地貌，让我们的笔记内容变得层次分明，以实现像大纲结构那样的**快速定位**。

具体解决办法便是在保留**语境**的情况下进行**压缩**。然而，保留语境和压缩文本这两种操作本身存在矛盾。

对此，Forte 提出了渐进式归纳，以设计一种**兼顾语境和压缩、方便用户快速寻找知识和信息的笔记记录方法 —— 渐进式归纳**。

在我看来，渐进式归纳提供了一种处理原子化笔记的笔记记录方法，让单条笔记保持不再淹没至笔记数据库中，而是流动起来为我所用。

**下面是渐进式归纳的具体流程：**

1.**输入原始文本**。简单而言，将你从各种阅读软件、阅读器所获得的笔记导入你的笔记软件。这一步是渐进式归纳的基础。

![](https://pic2.zhimg.com/v2-2221f47470ca9b77e2e64cd48cd6e3bd_r.jpg)
![](https://pic4.zhimg.com/v2-4a50290f16a349b67b20eed4609e0cbb_r.jpg)
2.**对关键点进行加粗**。将你认为重要的部分进行加粗。

![](https://pic4.zhimg.com/v2-a052d40cae859516803930ae45c3a287_r.jpg)
![](https://pic4.zhimg.com/v2-a1e3d0d911cab7c55bd721330394eecf_r.jpg)
3.在已经加粗的文本中，**选择一些精华内容添加高亮**。这一步相当于内容的再次压缩和精炼。

![](https://pic1.zhimg.com/v2-60f6ce284c6da9b8000f2ccefc17012c_r.jpg)
![](https://pic2.zhimg.com/v2-08c3f60888558c62a3c7f4cf74ca4a69_r.jpg)
![](https://pic4.zhimg.com/v2-1630ffe519b48f9ad6726fcdddf6c6ff_r.jpg)
4.**添加微型摘要/mini-摘要**。这一步的要点在于不再使用别人的语言，而是要用自己的话对已经加粗或者高亮的内容进行重新表述，并添加自己的感悟和评价。

![](https://pic4.zhimg.com/v2-39a66cd7c1db9b37caf92020a79e199f_r.jpg)
![](https://pic2.zhimg.com/v2-67ac995dc6b941a7b81a888f260cae5d_r.jpg)
5.**内容关联和重组**。最后一步，便是站在更为宏观的角度，将与此笔记相关的其他笔记内容、外部链接、参考文献添加至该条笔记，以建立笔记关系网络。

![](https://pic1.zhimg.com/v2-d1fdfc754c9226f4d40d25c02997a348_r.jpg)
![](https://pic4.zhimg.com/v2-5d46596fc8668fa53732e304d22d71a7_r.jpg)
需要注意的是，**不同笔记的价值是不一样的。并不是每条笔记都需要进行到第五层次**。有些笔记可能进行到第二三层级即可。

那么，**渐进式归纳的边界到底在哪儿呢**？

渐进式归纳是层层递进、逐步提炼精要到过程。这样设计笔记的好处是，你既可以快速知道文本内容大意，在需要的时候，也可以查看某条笔记精华的上下文语境。

因此，**在渐进式归纳过程中，没有明确的标准告诉我们应该将笔记归纳至第几步**。

唯一的原则便是，笔记是辅助自己思考的工具，因此，精简的原则是保留笔记的可看性，只要能保障自己在将来需要的时候能快速找到、了解、吸纳某条笔记的观点即可。

##### 渐进式归纳的优点

从上面的步骤可以看到，渐进式笔记拥有层次感，保障了压缩和语境的平和。

如下图，渐进式归纳的笔记，既方便用户快速鸟瞰笔记全貌找到所需要的笔记，又能层层深入进入笔记内部，查看笔记的细节。

![](https://pic1.zhimg.com/v2-f0c4f8ecb539d3261f4984642b386d44_r.jpg)
鸟瞰：快速概览笔记

![](https://pic2.zhimg.com/v2-a1f2fd4c954fdcb39d0ae04e7638a6e1_r.jpg)
笔记有层次感，可以下降查看细节

#### 注释

* 文章**转载自 Effie Blog**, 已经获得授权。
* **Effie 专业写作软件**：一款**思写合一**的**专业写作软件**，拥有 Markdown 语法支持、全平台、极简沉浸设计等基本要求，同时内置了思维导图，允许用户将大纲列表**一键转化为思维导图模式**。无论是**严肃写作**，**随手记录**，亦或是把逻辑完善成思维导图，Effie 均可胜任。
