---
人员: 
  - "[[云峰Ivan]]"
tags:
  - articles
日期: 2025-02-21
时间: None
相关:
  - "[[flomo]]"
  - "[[picgo]]"
  - "[[bbedit]]"
  - "[[logseq]]"
  - "[[mdnice]]"
  - "[[typora]]"
  - "[[代码]]"
  - "[[修订]]"
  - "[[发布]]"
  - "[[图床]]"
  - "[[插件]]"
  - "[[编辑]]"
  - "[[markdown]]"
  - "[[obsidian]]"
  - "[[get笔记]]"
  - "[[免费版]]"
  - "[[腾讯cos]]"
  - "[[ai大模型]]"
  - "[[john gruber]]"
  - "[[信息消费]]"
  - "[[在线图库]]"
  - "[[在线工具]]"
  - "[[工作流程]]"
  - "[[工具思维]]"
  - "[[文件存储]]"
  - "[[文件管理]]"
  - "[[文字加工]]"
  - "[[文档解读]]"
  - "[[知识管理]]"
  - "[[笔记工具]]"
  - "[[语法污染]]"
  - "[[赛博上香]]"
  - "[[软件开发]]"
  - "[[markdown格式]]"
  - "[[公众号文章]]"
  - "[[本地化存储]]"
  - "[[笔记收集器]]"
  - "[[语音转文字]]"
  - "[[生产力与自我提升]]"

链接: https://mp.weixin.qq.com/s/kaQJ8WA6R-1hkqLLuxW_rQ
附件: https://mmbiz.qpic.cn/sz_mmbiz_jpg/55BG46UuxWo22BJtyI8Ynia3dGLcRxaowWvtIcT1VIgzvicx6gfTVrw79Nmw1ibB3ibKkKXOQic6CkknNicTKQ47hJcA/0?wx_fmt=jpeg)
---
## Document Note

## Summary

本文作者回顾了自己笔记工具的使用变迁，强调选择工具的核心在于“工具思维”的转变——从追求功能丰富转向追求简单高效，服务于信息的收集、加工和输出。作者放弃了 Logseq，原因是其对 Markdown 格式的过度改动导致文件难以兼容其他工具；而 Obsidian 虽高度可定制，但过多插件和调整反而浪费时间，陷入“工具的诅咒”。作者边缘化了 Flomo，转而拥抱 Get 笔记，主要因其在 AI 大模型支持下，输入体验更优且能辅助文字加工，提高了信息处理效率。最终输出主要面向微信公众号，采用 Typora 编辑 Markdown，配合腾讯 COS 图床和 PicGo 工具管理图片。文章还引用了 Markdown 发明者 John Gruber 的工作流程，强调工具的选择应服务于工作流程，而非工具本身。总结中提出，根据需求复杂度不同，简单记录可用 Flomo、Get 笔记等工具；公众号写作推荐 MDnice；复杂信息处理建议分工使用多工具并同步。

  
**问题 1：**  

为什么作者选择放弃 Logseq？  

答案：  
Logseq 对 Markdown 语法做了大量魔改，导致编辑后的文件只能由自身良好打开，其他工具打开格式混乱，影响兼容性和使用体验。

**问题 2：**  

Obsidian 在作者使用中存在哪些问题？  

答案：  
Obsidian 可高度定制，但作者陷入不断试用和调整的循环，浪费时间，忽略了真正的知识管理，形成“工具的诅咒”。

**问题 3：**  

Get 笔记相比 Flomo 有什么优势？  

答案：  
Get 笔记借助 AI 大模型支持，输入体验更好，能实现语音转文字、文档解读及文字加工，提高了信息收集和加工的效率。

## Full Document
最近在整理我的年度效率工具，其中一直在思考一个问题，我的笔记工具，为何会发生变更？ 毕竟效率工具的选择， 绝对不是单纯的喜欢不喜欢这么简单。

2024年，我放弃了几个长期使用的笔记工具，引入了新的工具。这一变化的背后，核心是工具思维的转变：从信息消费的角度出发，选择最简单、最省力的工具。工具的选择不再是为了追求功能的多寡，而是为了更高效地完成信息的收集、加工和输出。

**总结**

1. 如果你**仅仅是记录**，选择任何工具都可以（如 Flomo、Get笔记、语雀等），这些都是基于网络存储的在线工具，无需关心文件存储在哪里，简单，方便，易上手；
2. 如果你的目标是**公众号文章**，省心的选择是MDnice，直接编写、排版、输出即可（如同在线版的Word，图文并茂，无需关心后台如何实现）；
3. 如果你的**需求稍微复杂**，可以尝试我的方法，也就是信息的收集，加工和发布，分别使用不同的工具，中间通过手动或自动同步，连接在一起。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/55BG46UuxWo22BJtyI8Ynia3dGLcRxaowpBW0770MwGjPBJJhZPgaEYvfs6Xq789s6UjEibTFIdhrVGSrHibMrSqA/640?wx_fmt=png&from=appmsg)
**抛弃Logseq**

Logseq 是一个开放源代码的良心软件（也就是说，你甚至可以基于它的代码，魔改一份自己的 Logseq），基于文档块的引用功能非常强大，数据本地化存储，没有停止服务的风险。 然而，它的语法污染问题让我最终选择了放弃。简单来说，Logseq 对 Markdown 格式做了大量魔改，导致编辑过的 Markdown 文件几乎只能由它自己打开和编辑。其他工具虽然也能打开，但格式混乱，难以使用。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/55BG46UuxWo22BJtyI8Ynia3dGLcRxaowjPuuSlLnl4wYTTjDUzgPgU913HOPTTm3um3vZtVd4e6H4hHR80Y5ZA/640?wx_fmt=png&from=appmsg)
**Obsidian：从毛坯房到精装房的折腾**

Obsidian 是一个非常优秀的基于 Markdown 的笔记软件，遵循“file over app”的理念（即你的文件和知识比软件本身更重要）。它就像一个毛坯房，通过丰富的插件可以打造成精装房。然而，正是这种高度的可定制性让我陷入了“工具的诅咒”——不断试用、调整、再试用、再调整，最终陷入了工具的循环中，反而忽略了真正的知识管理。

这让我想起练习毛笔字的经历：字没写几行，笔墨倒是换了不少。结果呢？字并没有因此变得更好看，反而浪费了大量时间。

BTW：Obsidian 最近宣布了全面免费，也就是说，在企业工作的用户，可以免费使用而不用担心任何的版权问题了。如果你想要支持，可以买一份授权来支持他们（赛博上香）。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/55BG46UuxWo22BJtyI8Ynia3dGLcRxaowLbmeMjXb5RrKeS6gQyIm8C7WP8FjDKQbtV0kRgINsibLAKLq9guLfRg/640?wx_fmt=png&from=appmsg)
如同我想要练习写毛笔字，字没写几行，笔墨倒是换了不少。最后，我的字并没有更好看，so sad。

**边缘化 Flomo，拥抱 Get 笔记**

我几乎要抛弃Flomo，开始拥抱了Get笔记作为收集器。原因也很简单。原因很简单：Get 笔记的输入体验更好。比如直接语音转文字，并可以适度的加工（修订错别字，润色文字）； 可以直接音频转写文字（导入录音文件）；可以导入PDF,WORD等文档进行文档解读；而实现这些的关键点，是因为AI大模型的加持。

如果单纯只是输入和收集器，Get笔记只是略好，不足以完全替代Flomo；

但是如果从信息消费的视角，因为AI的加持，Get笔记向前走了一步，就是实现了文字的”加工“这一步骤。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/55BG46UuxWo22BJtyI8Ynia3dGLcRxaowrdN9T6AQ5TXDReZZcAT6ZMlkI5ZUiaPKqRv1UJEycACDe5rVrnQUACQ/640?wx_fmt=png&from=appmsg)
比如，我最近在看”王慧文的清华产品课“，随手记录了数十条的笔记，使用Get笔记，就可以让AI帮我总结我对这个课程的感受，并总结为一篇微信公众号的文章。 向前一步，已经实属不易。

**我的最终输出**

主要是微信公众号，那么，比较好的方式应该是Markdown，加上必要的一些图片；所以需要借助下最简单的（或者说使用体验最好的Markdown工具），所以，我选择了Typora。 微信公众号里面有一些图片，我使用的图床（你可以理解成是一个在线图库）是腾讯的COS,搭配PicGo工具实现。 当然，你如果使用MDnice，就可以不用Picgo+腾讯COS的方式，不过免费版有数量限制就是了（总体上MDnice 的免费数量限制还好，在一众收费工具中，堪称慷慨）。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/55BG46UuxWo22BJtyI8Ynia3dGLcRxaowQBb838DaP9X8BrzdXpCUZplHHHezA0icpqfYccLn1pmHC2UtwfluqUg/640?wx_fmt=png&from=appmsg)
**一个故事**

偶然间翻到 markdown 的发明者之一， John Gruber 的博客，他提到了他的工作流程。 John Gruber 的预期工作流程

1. 编写、编辑、修订于 BBEdit。
2. 当准备好时，登录 MT 的网页界面，粘贴文章并发布。

但实际上他的工作流程是这样的

1. 在 BBEdit 中编写。
2. 在浏览器中预览。
3. 切换回 BBEdit 进行修订。
4. 重复直到完成。
5. 登录 MT，粘贴文章，发布。

看来，Markdown 的发明，正是为了解决 John Gruber 在日常工作流程中的痛点。这也让我意识到，工具的选择和优化，始终是为了更好地服务于我们的工作流程，而不是为了工具本身。
