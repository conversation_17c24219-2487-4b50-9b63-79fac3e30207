---
人员: 
  - "[[juejin.cn]]"
tags:
  - articles
日期: 2024-08-26
时间: None
链接: https://juejin.cn/post/7407034973450715136
附件: https://lf3-cdn-tos.bytescm.com/obj/static/xitu_juejin_web/static/favicons/apple-touch-icon.png)
---
## Document Note

## Summary

前往飞书开放平台开发者后台创建自建应用；根据多维表格新增记录接口API要求的权限列表开通相应权限；将自建应用添加至相应多维表格协作者（注意：多维表格不要开启高级权限设置，否则自建应用会无权限）

## Full Document
* Automa [www.automa.site/](https://link.juejin.cn?target=https%3A%2F%2Fwww.automa.site%2F)
* 飞书开放平台 [open.feishu.cn/?lang=zh-CN](https://link.juejin.cn?target=https%3A%2F%2Fopen.feishu.cn%2F%3Flang%3Dzh-CN)

##### 1.创建飞书多维表格

##### 2.创建飞书自建应用

* 前往飞书开放平台开发者后台（[open.feishu.cn/app?lang=zh…](https://link.juejin.cn?target=https%3A%2F%2Fopen.feishu.cn%2Fapp%3Flang%3Dzh-CN%25EF%25BC%2589) 创建自建应用
* 根据多维表格新增记录接口API（[open.feishu.cn/document/se…](https://link.juejin.cn?target=https%3A%2F%2Fopen.feishu.cn%2Fdocument%2Fserver-docs%2Fdocs%2Fbitable-v1%2Fapp-table-record%2Fcreate%3FappId%3Dcli_a64ffb3362b9100d%25EF%25BC%2589) 要求的权限列表开通相应权限

![image.png](https://p3-xtjj-sign.byteimg.com/tos-cn-i-73owjymdk6/fd55b5d3277b47e7a233d60e4debd30b~tplv-73owjymdk6-jj-mark-v1:0:0:0:0:5o6Y6YeR5oqA5pyv56S-5Yy6IEAgemhhbmdzYW50eA==:q75.awebp?rk3s=f64ab15b&x-expires=1744202633&x-signature=vt2t0dS9C8KK%2F6QLFaFfUYtp6rw%3D)
* 将自建应用添加至相应多维表格协作者（注意：多维表格不要开启高级权限设置，否则自建应用会无权限）

![image.png](https://p3-xtjj-sign.byteimg.com/tos-cn-i-73owjymdk6/42fe991a20594d8298c73be71ad02b3d~tplv-73owjymdk6-jj-mark-v1:0:0:0:0:5o6Y6YeR5oqA5pyv56S-5Yy6IEAgemhhbmdzYW50eA==:q75.awebp?rk3s=f64ab15b&x-expires=1744202633&x-signature=pLKnpt0JGQod5g0s5ojNs1Xh4d4%3D)
##### 3.获取tenant\_access\_token

* 自建应用获取tenant\_access\_token接口文档：[open.feishu.cn/document/se…](https://link.juejin.cn?target=https%3A%2F%2Fopen.feishu.cn%2Fdocument%2Fserver-docs%2Fauthentication-management%2Faccess-token%2Ftenant_access_token_internal)
* app\_id和app\_secret说明：[open.feishu.cn/document/se…](https://link.juejin.cn?target=https%3A%2F%2Fopen.feishu.cn%2Fdocument%2Fserver-docs%2Fapi-call-guide%2Fterminology)

![image.png](https://p3-xtjj-sign.byteimg.com/tos-cn-i-73owjymdk6/52c58b99c00346bfa90ca3268d45696b~tplv-73owjymdk6-jj-mark-v1:0:0:0:0:5o6Y6YeR5oqA5pyv56S-5Yy6IEAgemhhbmdzYW50eA==:q75.awebp?rk3s=f64ab15b&x-expires=1744202633&x-signature=K9v8nHYqrSmNC3gyS6QsKLYu5MM%3D)

```
const app_id = "xxx";
const app_secret = "xxx";
let tenant_access_token = "";
const getToken = () => {
  return new Promise((resolve, reject) => {
    fetch(
      "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          app_id,
          app_secret,
        }),
      }
    )
      .then((response) => {
        return response.json();
      })
      .then((data) => {
        console.log(data);
        if (data.msg === "ok") {
          console.log("tenant_access_token 获取成功");
          tenant_access_token = data.tenant_access_token;
        }
        resolve();
      })
      .catch((error) => {
        console.log("tenant_access_token 获取失败");
        reject();
      });
  });
};
await getToken();

```

##### 4.获取多维表格app\_token与app\_id

```
https://xxx.feishu.cn/base/app_token?table=table_id&view=vewFz2HIhn

```

* app\_token：多维表格的唯一标识符（[open.feishu.cn/document/se…](https://link.juejin.cn?target=https%3A%2F%2Fopen.feishu.cn%2Fdocument%2Fserver-docs%2Fdocs%2Fbitable-v1%2Fnotification%25EF%25BC%2589)
* table\_id：多维表格数据表的唯一标识符（[open.feishu.cn/document/se…](https://link.juejin.cn?target=https%3A%2F%2Fopen.feishu.cn%2Fdocument%2Fserver-docs%2Fdocs%2Fbitable-v1%2Fnotification%25EF%25BC%2589)

##### 5.调用多维表格新增记录接口新增数据

多维表格新增记录接口文档：[open.feishu.cn/document/se…](https://link.juejin.cn?target=https%3A%2F%2Fopen.feishu.cn%2Fdocument%2Fserver-docs%2Fdocs%2Fbitable-v1%2Fapp-table-record%2Fcreate%3FappId%3Dcli_a64ffb3362b9100d)

```
const app_id = "xxx";
const app_secret = "xxx";
let tenant_access_token = "";
const getToken = () => {
  return new Promise((resolve, reject) => {
    fetch(
      "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          app_id,
          app_secret,
        }),
      }
    )
      .then((response) => {
        return response.json();
      })
      .then((data) => {
        console.log(data);
        if (data.msg === "ok") {
          console.log("tenant_access_token 获取成功");
          tenant_access_token = data.tenant_access_token;
        }
        resolve();
      })
      .catch((error) => {
        console.log("tenant_access_token 获取失败");
        reject();
      });
  });
};

const table_token = "xxx";
const table_id = "xxx";
const syncPm = async () => {
  await getToken();
  const data = {
    fields: {
      xxx: xxx,
    },
  };
  fetch(
    `https://open.feishu.cn/open-apis/bitable/v1/apps/${table_token}/tables/${table_id}/records`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${tenant_access_token}`,
      },
      body: JSON.stringify(data),
    }
  )
    .then((response) => {
      return response.json();
    })
    .then((data) => {
      console.log("新增成功");
    })
    .catch((error) => {
      console.log("新增失败");
    });
};

```

##### 6.编写Automa脚本任务流

![image.png](https://p3-xtjj-sign.byteimg.com/tos-cn-i-73owjymdk6/d2a7298a0ffe4f0fb788af14246169e6~tplv-73owjymdk6-jj-mark-v1:0:0:0:0:5o6Y6YeR5oqA5pyv56S-5Yy6IEAgemhhbmdzYW50eA==:q75.awebp?rk3s=f64ab15b&x-expires=1744202633&x-signature=AtaaDSWwz3Uu90S3ghPHS3SlJBc%3D)
任务流步骤：① 使用js采集获取目标网页信息并将获取的数据拼接在url中跳转飞书开放平台（非飞书开放平台调用新获取tenant\_access\_token和新增记录接口会跨域）-> ② 使用js获取url中拼接的采集信息调用新增记录接口同步至多维表格，然后跳转回原页面

采集信息代码

```
const data = {
  xxx: document.querySelector("xxx")?.innerText || "",
  url: window.location.href || "",
};
window.location.href = `https://open.feishu.cn?params=${encodeURIComponent(
  JSON.stringify(data)
)}`;

```

同步新增代码

```
const app_id = "xxx";
const app_secret = "xxx";
let tenant_access_token = "";
const getToken = () => {
  return new Promise((resolve, reject) => {
    fetch(
      "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          app_id,
          app_secret,
        }),
      }
    )
      .then((response) => {
        return response.json();
      })
      .then((data) => {
        console.log(data);
        if (data.msg === "ok") {
          console.log("tenant_access_token 获取成功");
          tenant_access_token = data.tenant_access_token;
        }
        resolve();
      })
      .catch((error) => {
        console.log("tenant_access_token 获取失败");
        reject();
      });
  });
};

const showMask = (text) => {
  const maskDom = document.querySelector(".sync-add");
  if (maskDom) {
    document.body.removeChild(maskDom);
  }
  const divDom = document.createElement("div");
  divDom.className = "cx-pm-sync";
  divDom.style.position = "fixed";
  divDom.style.top = "0";
  divDom.style.left = "0";
  divDom.style.right = "0";
  divDom.style.bottom = "0";
  divDom.style.background = "#fff";
  divDom.style.zIndex = 99999;
  divDom.style.width = "100%";
  divDom.style.height = "100vh";
  divDom.style.padding = "20px";
  divDom.innerHTML = text;
  document.body.appendChild(divDom);
};

const table_token = "xxx";
const table_id = "xxx";
const syncPm = async () => {
  showMask("同步新增中...");
  await getToken();
  const collectData = JSON.parse(
    decodeURIComponent(window.location.search.replace("?params=", ""))
  );
  if (collectData) {
    console.log("获取采集数据失败，同步已终止");
    showMask("获取采集数据失败，同步已终止");
    return;
  }
  const data = {
    fields: {
      xxx: collectData.xxx,
    },
  };
  fetch(
    `https://open.feishu.cn/open-apis/bitable/v1/apps/${table_token}/tables/${table_id}/records`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${tenant_access_token}`,
      },
      body: JSON.stringify(data),
    }
  )
    .then((response) => {
      return response.json();
    })
    .then((data) => {
      console.log("同步新增成功");
      showMask("同步新增成功");
      window.location.href = pmInfo.url;
    })
    .catch((error) => {
      showMask("同步新增失败");
      console.log("同步新增失败");
      window.location.href = collectData.url; // 跳转回原页面
    });
};
syncPm();

```
