---
人员: 
  - "[[和菜头]]"
tags:
  - articles
日期: 2025-02-12
时间: None
链接: https://mp.weixin.qq.com/s/1EvmDRD9sR2YHs4yLzgaWQ
附件: https://mmbiz.qpic.cn/mmbiz_jpg/Ia6gU9JNtkpL3TsWtV0ianwqOsZJnCHEEq2af1diaJcutU9TUK0JmC5pjg5RVe7ic8YohoywZXRqeXibG4icYVMJquA/0?wx_fmt=jpeg)
---
## Document Note

## Summary

## Full Document
![Image](https://mmbiz.qpic.cn/mmbiz_jpg/Ia6gU9JNtkpL3TsWtV0ianwqOsZJnCHEE4VgMWia5hDTCtic7rjXiaZJVD4CBzLNMbCepfez36H5rVXwvtjPBIBEng/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
如果你是 deepseek 玩家，那么以下这几个字你最近应该很熟悉：「**服务器繁忙，请稍后再试**」。

deepseek 的官方网站和 APP，从春节前到现在一直在努力应对网络攻击，以及用户高速增长。我看到有传闻说，为了降低服务器负载，一个用户每小时可以使用深度思考功能的次数是 5 次，也有人宣称是每 24 小时 5 次。这话无论真假，反正每个人看到最多的就是那句话。

但 deepseek-R1 本身是个开源模型，也就是说谁都可以拿去自己部署，大家并不是非得访问官方网站或者官方 App。比如说我自己，就同时在 deepseek 官网和其他几个第三方平台上使用，一个不行就切换另外一个，总有能用的。我觉得与其大家在 deepseek 官网上挤得水泄不通，不如发挥开源软件的优势，主动分流出去。以下，就是我自己使用的几个第三方deepseek-R1 模型平台：

**1、腾讯元宝：**https://yuanbao.tencent.com

有网站，也有 App。如果是访问网站，注册登录之后在页面左侧找「对话」。点开之后，在页面下方出现的对话框左下角，点三角形符号▼，在弹出的窗口里选择Deepseek R1就可以了。它默认的是混元模型，写着「Hunyuan」，你需要切换一下。

腾讯元宝的 deepseek R1 **支持深度思考，也支持联网搜索**。目前无论是混元还是 deepseek R1应该是免费，估计是普通用户免费，调用 API 另算。我自己用感觉速度还行，不能和官网的全速状态相比，但我认为是网页优化问题。完全可以多让用户等一会转菊花，然后猛地吐出答案，看上去就会让人感觉快很多。

2、**Perplexity** https://www.perplexity.ai/

如果你能访问 perplexity，那么你就可以用和上面类似的方法选择 Deepseek-R1 模型。即便你不是付费用户，每 24 小时你也有 5 次免费使用的机会。不过要注意一点，这里的 5 次是所有可选模型加在一起的使用次数，不要想着每一个模型刷 5 次，就可以得到20 次免费机会。

perplexity的 deepseek R1 **支持深度思考，也支持联网搜索，还是无限制版。**目前来说，它是我用过的第三方 deepseek-R1 中速度最快的一个。

3、**Cherry Studio+硅基流动** （动手爱好者）

这个组合在今年春节期间就开始流行，其实是要收费的，但是平台主打一个拉新，所以用了邀请制，邀请双方都可以获得2000 万 Token，足够用一阵子的。

使用这个组合需要一点动手能力。Cherry Studio 是个客户端，需要去下载。它的好处是可以接入多个 AI 平台的 API，提供所谓 All-in-one 服务。客户端都是免费的，GitHub 上有项目专页，不要被骗，要求付费下载就不对了。

Cherry Studio 就是个壳，需要添加 AI 模型的 API。硅基流动是个 AI 大模型平台，里面收录了很多开源大模型。所以，在下载好客户端，注册硅基流动之后，需要在 Cherry Studio 里绑定硅基流动的 deepseek-R1对应的 API---幸好，两家已经深度合作了。

访问硅基流动官网，在左侧工具条中部找到「账户管理」。点开，选择「API 密钥」。点开，选择「新建 API 密钥」。然后你就会看到一长串\*\*\*\*\*\*\*\*\*\*\*\*，在这一行最后点击「复制」。

回到Cherry Studio客户端，在窗口左侧工具条最下，找到齿轮⚙️符号，点开。在新窗口点「模型服务」，于是可以看到一列模型名称。选择「硅基流动」，点击「On」打开。然后在展开的窗口里复制刚刚得到的 API 密钥进行绑定。

回到「模型服务」选项，它的下方有「默认模型」，打开之后在「默认模型助手」里都选择Deepseek-ai/deepseek-R1。接下来，就是去点开左侧菜单的对话气泡图标，选择和「助手」对话了。

这个组合的速度是最慢的，据说硅基流动春节期间接下了泼天富贵，但是也很吃力。它们的 deepseek R1 仅**支持深度思考，不支持联网搜索。**

但是总有人热爱折腾，总有人喜欢 All-in-one。所以，也写在这里。注意一点，在这个方案里Cherry Studio客户端不是必选项，你完全可以在硅基流动的网站上直接选择使用各种模型，就是眼花缭乱一些罢了。大多要付费，但是你现在有 2000 万 Token，怕毛？

**Cherry Studio**：https://github.com/CherryHQ/cherry-studio

**硅基流动邀请**：https://cloud.siliconflow.cn/i/ULVvdr6F

其实，目前市面上不止有这些方案。我之所以介绍它们，是想说明不要整天去挤deepseek 官网，明明是开源项目，全世界各地第三方平台都在部署，完全可以自己去找一个顺手的去用。都开源了，还整天挤官网，挤得大家随时动弹不得，那开源的意义又在哪里呢？怎么说，又是官网才是原装货，用起来才有 deepseek 的「灵魂」？

最后，AI 领域的情况一天一个样。现在是 deepseek 官网过载，等这些第三方平台知名度扩大之后，也许就轮到它们塞车了。**同时，它们现在的服务方式、使用限制、付费条件也可能随时会变。**一切都有可能，所以明智的方法是自己多找几处备选，保证自己随时能有一个可以用的就好。

让马儿跑起来！

题图标题：**《跑起来》**

创作者：**和菜头的小肉手**

AI算法提供：**Midjourney V6.1**

Prompt：*****a racing horse, --sref 308641418 --ar 16:9 --s 1000 --p*** --v 6.1**

 **槽边往事****和菜头 出品**

****【微信号】****Bitsea**** 

**个人转载内容至朋友圈和群聊天，无需特别申请版权许可。**

**请你相信我，**

**我所说的每一句话，**

**都是错的**

> **禅定时刻**
> 
> 

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/Ia6gU9JNtkpL3TsWtV0ianwqOsZJnCHEEicVQ2gSqI63ia5RvZfwLoNA5f0SCMbTZzZXtebCqiaowicqWgV5ZHW6DAQ/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
**《斑马，斑马**》****

**和菜头的小肉手**

**Midjourney V6.1**

>  **《槽边往事》专营店营业中**
> 
>
