---
人员: 
  - "[[flomoapp.com]]"
tags:
  - articles
日期: None
时间: None
链接: https://help.flomoapp.com/thinking/smart-notes.html
附件:
---
## Document Note

## Summary

## Full Document
![](https://resource.flomoapp.com/101/images-391.png!webp)
最近看了少楠推荐的《卡片笔记写作法》。

虽然听他提到过很多次，但内心还是觉得不就是一本工具书嘛，做为一个知识工作者这类书看太多了，往往是有启发但带来实质的改变不多。

但这次很惊喜的是《卡片笔记写作法》在只看到绪论部分时，就带给我很多颠覆性的观点。

读到后面更是发现，它将我之前知道的那些读书、做笔记方法和理念融合的非常好，它不再是一个工具，而是一个高效的流程和系统，**并且是一个有可能改变我们人生成就的笔记写作流程。**

废话不多说，开始了解这个神奇的笔记系统。

///

今天的这篇文字比较多，有5000+字，在前面放个目录：

1. 卢曼的开挂人生&卡片式笔记法的诞生
2. 卡片笔记写作法的简单流程&进阶流程
3. 工具推荐：Notion & flomo
4. 最最最重要要是，形成总体工作流程
5. 结语：简洁是最重要的原则

但请放心，主要是介绍的部分，挺好读的。

#### [#](https://help.flomoapp.com/thinking/smart-notes.html#_01-卢曼的开挂人生-卡片式笔记法的诞生) 01. 卢曼的开挂人生&卡片式笔记法的诞生

（足够清楚这段经历的小伙伴可以跳过）

卢曼最早是一名德国的公务员，每天工作结束后，他就找借口回家，做自己最喜欢的事情——阅读和关注自己感兴趣的哲学、组织理论和社会学内容。

在做笔记的过程中，他意识到，**一个想法、一条笔记只有在它的上下文语境中才有价值，而上下文并不一定是它的出处。**

所以，他开始思考一个想法，那就是如何让笔记与不同的上下文相联系并起到相应的作用。

![这张图生动的展示了信息连接的重要性。](https://resource.flomoapp.com/101/images-393.png!webp)
他没有再将笔记添加到现有的类别或相应的文本中，而是将它们全部写在小纸片上，并在纸片的角上编号，然后将它们收集到卡片盒中。卡片盒成了他的对话伙伴、主要的创意来源和生产力引擎，对他组织和发展他的思想大有裨益。

此后，他的人生就像开了挂一样。

卢曼将盒子里的想法整理成手稿，交给了德国颇有影响力的社会学家赫尔穆特·舍尔斯基。

舍尔斯基把它带回家，读了这个学术外行写的东西后，很快跟卢曼联系，建议他到新成立的比勒菲尔德（Bielefeld）大学担任社会学教授。

**这时候卢曼并不是一名社会学家。事实上在德国，卢曼甚至连做社会学教授助手的资格都没有** 。但卢曼没有拒绝这个机会，反而是在卡片盒的帮助下，在不到一年的时间，就完成了博士论文和定职论文，这期间他还上了社会学的课。

不久后的1968年，卢曼就被选为比勒菲尔德大学的社会学教授，并且终身担任这一职位。

29年半以后，卢曼完成了《社会的社会》这本分为两卷的大部头的最后一章，轰动了学术界。这是一个激进的新理论，不仅改变了社会学，而且激起了哲学、教育学、政治理论和心理学领域的热烈讨论。

30年间，他出版了58本著作和数百篇文章（不包括译本），许多都成为各领域的经典之作。甚至在他去世之后，他办公室里留下的快完成的手稿被整理出来，又有六七本关于宗教、教育或政治等不同主题的书以他的署名出版。

这段卢曼的经历很重要，他的人生印证了卡片式笔记的重要价值。在后面我会说到，想要让卡片笔记写作法发挥它的威力，需要我们在整个阅读和写作的行为习惯上都配合它，**只有了解到卡片式笔记写作的巨大价值，我们才会更愿意做出如此深入的改变。**

![Niklas Luhemann 一生的 24 个盒子](https://resource.flomoapp.com/101/images-394.png!webp)
#### [#](https://help.flomoapp.com/thinking/smart-notes.html#_02-卡片笔记写作法的简单流程) 02. 卡片笔记写作法的简单流程：

考虑到有些读者看完书不知道怎么上手，这里我总结了一个简单流程：

1，平时学习的时候不只是阅读听课，一定还要多记录自己的想法（写的时候，先不去评判想法的好坏）；

2，将摘抄和想法全部记录到一个笔记工具中，比如flomo，且不做分类；

3，时不时回顾翻阅自己写过的知识卡片，发现关联的时候就将他们放到一块儿；

4，当一个主题的卡片积累多了，就尝试将它们串成一篇完整的具有洞见的文章。

卡片式笔记写作法就是这么简单，相比于传统的写作法，卢曼更强调在白纸上写作之前，我们应该做更充分的准备（写了大量相关的想法卡片）。

他认为输出一篇完整的文章，不应该在脑子里完成，我们的大脑特别不擅长这类线性的叙事，写文章（对一堆知识卡片整理编辑）应该是在纸上进行的。

![](https://resource.flomoapp.com/101/images-395.png!webp)
**另外，卡片笔记写作流程非常强调记录想法。**

下面这张图大家肯定看过很多次了，但回想一下，你在学习的时候最多的状态是哪一种？

![卡片笔记写作法就是这个理论最简单实现方式](https://resource.flomoapp.com/101/images-396.png!webp)
再次看到这张图让我想起一位朋友，他很喜欢阅读，时不时就会发我一些好的文章。

有次见面我就问他，你刚才发我的那篇文章讲了什么？它好在哪里？朋友努力的想了半天，才发现自己刚看过就不记得文章讲了些什么了，只能说出一些大概的关键词。

学习金字塔中说的阅读是比较专注状态下的阅读，我很怀疑，平时在手机上阅读的时候学习的留存率能否有5% 。

但是卡片笔记写作使用的方法（其实就是著名的费曼技巧的运用），能让学习效率提升到50%以上。

**也就是说我们只需要使用一个简单的技巧，每次学习完用几分钟的时间，就可以使我们的效率提升10倍不止。**

在这个需要终生学习的时代，日积月累，是多么夸张的一个优势啊。

**◎ 重点强调**

多记录自己的想法，试着每天都写几张卡片。

不用分类、不要管是不是写的好、不用管以后怎么用，将当下的想法写成卡片即可。**信赖卡片笔记写作法的流程会让你的想法变成金子。**

![](https://resource.flomoapp.com/101/images-397.png!webp)
#### [#](https://help.flomoapp.com/thinking/smart-notes.html#_03-进阶流程-融入知识工作者的心得技巧) 03. 进阶流程：融入知识工作者的心得技巧

所谓进阶其实基本流程是一样的，只不过因为我每天大量都是在处做知识相关的工作，会有一些心得技巧可以很好地融入流程当中。

你要是看了有感觉也可以试着融入自己的流程。

##### [#](https://help.flomoapp.com/thinking/smart-notes.html#_1-临时笔记-边学习边记录重点) **1，临时笔记：边学习边记录重点**

这一步我相信很多读者平时都有在做，我就只分享一些让做这件事更轻松愉悦的小技巧：

◎ 如果听课内容比较简单，通常我会边听边写完整的想法卡片。**每次记一大堆笔记事后整理也是一个很重的负担**，直接当堂整理完清清爽爽的，会让这次学习体验非常愉悦。

◎ 如果遇到比较困难的内容，就只需要先将重点记录下来，**把理解的工作放到后面来做。** 这样会大大减低我们的畏难心理，可以让我们在学习困难内容的时候更轻松更有乐趣。

**这就是流程的好处，可以讲将一件事情放到最适合的时候去做，让本来困难的事情变得轻松。**

◎ 信赖自己的感觉，专注于那些让你有很多思考的知识。不要总担心错过重要的知识，于是硬着头皮去啃很多自己还没有体验的知识。

现在每天好的知识应接不暇，挑自己正需要的学习就好（学起来特别快），其它的如果真正重要以后总会有机会遇见的。

◎ 很推荐用电脑在网页端用微信读书阅读书籍，在电脑上可以直接将有感觉的摘抄复制到笔记软件中，这样书看完就可以直接把书扔了，对着有摘抄写想法就行，这样阅读一本书特别轻松。

##### [#](https://help.flomoapp.com/thinking/smart-notes.html#_2-文献笔记-用自己的语言翻译原材料) **2，文献笔记：用自己的语言翻译原材料**

> 文献笔记要非常简短，精心选择，并使用自己语言记录，对引文要格外挑剔，不要只是抄写，而不去真正理解其含义。
> 
> 

**如果你想真正理解某件事情，你就必须把它转化为自己的语言。思考既要在自己的脑子里进行，也要在纸上进行。**

如果有一件事是专家们一致同意的，那就是你必须将你的想法外显化，必须把它们写下来。理查德·费曼和本杰明·富兰克林同样强调这一点。

《卡片笔记写作法》非常强调用自己的语言再表达一遍，这是让学习效率飙升的关键。而文献笔记在卡片笔记写作流程中的定位，是一个帮助我们理解和把握文本的工具。

以前我总是阅读大量的文章、书籍、材料，笔记软件也放了很多很多摘抄。但我作为一个写作5年的公号博主，可以说95%的摘抄都只是摘抄，它们再也没有出现过在我的思考和文章里。

下次，在大家摘抄之后，放入文献笔记之前，**请在摘抄后面用你自己的语言翻译一遍，这会迫使你充分读透刚刚摘抄的文字，并且让你下次需要用到的时候更快记起并使用。**

我会在处理临时笔记的时候，会反问自己一个问题，这段话或者这个章节讲了些什么？然后用总结和复述的语气写出来，如果熟练的话，可以想象要把新看到的这个知识讲给别人听的状态。

这个步骤的初期更推荐自由写作，就是想到什么写什么，想到多少写多少。想到的多就多写点，没什么想法的时候也可以不写。想要坚持做一件事情就一定要让这件事变得快乐。

关于快乐的自由写作，推荐一本很好读的书《写出我心》，我之前也写过详细介绍的文章：[推荐一种很爽的写作方法  (opens new window)](https://mp.weixin.qq.com/s?__biz=MzI2NzgwMTg1Mg==&mid=2247483879&idx=1&sn=cc0b8df22648d06538f6b2d9a980db02&scene=21#wechat_redirect)

##### [#](https://help.flomoapp.com/thinking/smart-notes.html#_3-永久笔记-将要进行写作或者深度思考的主题) **3，永久笔记：将要进行写作或者深度思考的主题**

> 仔细阅读你在第一步或第二步所做的笔记，并思考它们与你自己的研究、思考或兴趣所在的相关内容有何关联。永久笔记不是为了收集，而是为了衍生想法、论点和讨论。
> 
> 

对于用自己语言翻译过一遍的文献笔记，我自己有几种处理方式：

1.只是觉得自己增加了一部分新知，并没有更多的想法。那就保存到文献笔记中，设定好关键词方便要找的时候找得到；

2.有更多的想法，就把想法写下来，然后想想有没有和之前永久笔记盒中的主题有关联，有的话就放到那个主题下面。

3.没有的话，就建立一个新的主题（序列），将想法放入其中，期待后续有更多想法加入进来。

这样，我们就会在永久笔记盒中有了很多主题，这些主题都还缺少材料，不用现在就着急将他们补充完整，而是在后续的阅读中，不断按照流程一步步做下去，让永久笔记盒中的笔记越来越多、越来越完整。

直到某一次再次添加永久笔记的时候，突然发现有一条清晰的叙事线出来了，这时候只需要将相关的材料复制到自己熟悉的编辑器中，对文本进行编辑优化，最终产出一篇优秀的文章。

最后，整个流程看起来好像笔记很复杂很累，但其实用到的都是我们常用的学习习惯和工具，所以稍微适应之后很容易上手和变得顺手，每次学习的时候都会真自然地一路做下来。

卡片笔记写作法最牛掰的地方就在于，**只要坚持这个刻意练习，就可以不断的“自动”输出很多完整的具有洞见的文章。**

这种正反馈反过来会激励我们更多的记录自己的想法，这才是一套高效的系统。

![永久卡片笔记样式@Introduction to the Zettelkasten](https://resource.flomoapp.com/101/images-398.png!webp)
**◎ 温馨提示**

初期最好是在精神状态好的时候做以上流程，尤其是思考和写的部分。

我前几天晚上21点多听完少楠的直播分享，听的很有感觉但当时我有点累，只能勉强记下几个关键词。然而第二天早晨的时候，想到昨天学习到的一些概念和观点，一口气写了2000多字。

**真的，想法枯竭的时候就去玩耍和休息吧！**

![](https://resource.flomoapp.com/101/images-400.png!webp)
#### [#](https://help.flomoapp.com/thinking/smart-notes.html#_04-工具推荐-notion-flomo) 04. 工具推荐：Notion & flomo

很认同书中的一句话：**工具是为了让事情更简单而不是变得更复杂。**

所以，这方面大家只需要根据自己的习惯来就好了，尤其是有双链功能的Notion和Obsidian这种使用难度比较高的，在没有熟练之前只会成为麻烦，新手不推荐使用。

**flomo**也是今年用的爱不释手的轻量化笔记，Notion适合放有主题的内容，而flomo非常适合用来存放各种突发奇想。也不知道以后会不会用到，但都是矿，说不定哪天挖出来就是宝藏。

**这种极放松极自由表达的感觉真的只有flomo能带来，这就是产品的气质赋予的，在这个无比焦虑的时代很难得。**

任何超过2天没有去动过的笔记我都会第一时间把它删掉或者存到Notion中，不让暂时没有灵感和积累的文字影响我思考，留下的都是我有把握可以写出新思考的文字。

**Notion**是我今年开始弃印象笔记后启用的笔记软件，非常适合用存放需要长期保存的笔记，并且自带简单的引用系统，目前够用，期待以后能有插件可以实现更多可视化的功能。

Notion笔记初期觉得很难用很嫌弃，后期觉得很亮眼，其背后的思路远优于其它笔记软件。（虽然还是很不爽Notion中的某些细节）

![可以切换到列表视图后按标签检索](https://resource.flomoapp.com/101/images-402.png!webp)
#### [#](https://help.flomoapp.com/thinking/smart-notes.html#_05-最最最重要要是-形成总体工作流程) 05. 最最最重要要是，形成总体工作流程

准确的说，**卡片笔记法不是一种做笔记的技巧，更不是笔记收纳盒，而是一个帮助我们深度思考和写作输出的高效流程。**

我上面卡片式笔记的方法和工具写的非常详细，以至于有点啰嗦的程度，但这很重要，这些都是我在将卡片式写作融入我的写作习惯当中。

只有当所有相关的工作都相互衔接成为一个整体，所有的瓶颈都被消除、**并且每个环节都能相互促进时**，卡片式写作开挂般的能力才会显现。

这种**二阶效应**，在书中用的是麦克莱恩集装箱改变世界的案例，这个案例在罗辑思维的第84期《集装箱改变世界》中有详细讲述，非常精彩。

毫不客气的说，我们能享受全球化的红利很大程度上仰仗于集装箱的货运流程。

但其实集装箱的发明是早就有了的，直到麦克莱恩做出了大量的努力，推动码头工人、推动整个产业链上下游的企业、从出货的仓库、工人、运送的货车、港口码头、轮船，都为集装箱货运做出优化，才使得效率有这么惊人的提升，才能让国际货运成本直接降低了95%的程度。

卡片式笔记的巨大价值，同样需要我们将这个流程贯彻到我们日常的各种行为当中。

我虽然很早就意识到工作流的重要性，**但只注意到了建立流程，忽视了将流程中的各种摩擦消除，也没有改变自己整个工作习惯来配合这套工作流，** 所以之前的工作流并没有给我带来多大的价值。

在这本书多次强调下，我才突然意识到这点，瞬间清醒，也因此在这里也特别强调配合流程做出改变以及消除流程中的所有摩擦的重要性。

**什么时候算是卡片式写作流程成功？**

就看你有没有开始非常轻松愉悦的完成流程中的每个环节，并且能源源不断自发涌现各种成型的文章主题。轻松输出文章的同时，这种正反馈反过来激励你写下更多笔记。

#### [#](https://help.flomoapp.com/thinking/smart-notes.html#写在最后) **写在最后：**

书中还有很多惊艳的理论，比如不要自己费力，拼命消耗意志力去做事，这样是不长久的，**要让让工作推动你前进。**（第二章第四节）

还有，不同的任务需要不同的关注度、在兴趣的驱动下完成任务、放弃做计划才能成为专家、培养抓住要点的能力……

这些重要的法则和我最近的很多思考神同步，在以后的更新中会不断分享。

最后的最后，不要被前面复杂的讲述吓到，**卡片式写作是一个强调简单为重要哲学的方法。**

现在，你只需要在理解上面基础流程之后，开始每天写3条或者6条笔记卡片，并收纳到自己的笔记卡片工具当中关联起来就可以了。

我自己也需要用更长的时间，来践行和检验卡片式笔记，希望过一段时间我能分享关于卡片式笔记更深的思考，我们下期再见~

1.《卡片笔记写作法：如何实现从阅读到写作》[德]申克·阿伦斯（Sönke Ahrens）；

2.什么是 Zettelkasten 卡片盒笔记法？- 土嗨英语的回答 - 知乎

https://www.zhihu.com/question/384309878/answer/1120682799

3.罗辑思维第84期：改变世界的箱子：https://m.igetget.com/share/course/article?id=RQLYWyjMZoa0J1y6dJp4wvzDbO26Bq >
