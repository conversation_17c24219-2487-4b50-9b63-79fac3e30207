---
人员: 
  - "[[flomoapp.com]]"
tags:
  - articles
日期: None
时间: None
链接: https://help.flomoapp.com/thinking/how-to-review.html
附件:
---
## Document Note

## Summary

在 2022 年初的计划中，我们提到了深度年（Depth Year）的概念，希望能用一年时间把一些已有的东西深入打磨。在 Android 完成离线，iOS 完成编辑器重构后，作为 flomo 重要的组成部分，flomo 101 （也就是 help. flomoapp.

## Full Document
> 对应功能：[每日回顾](https://help.flomoapp.com/advance/lucky.html)
> 
> 

> 在 2022 年初的计划中，我们提到了深度年（Depth Year）的概念，希望能用一年时间把一些已有的东西深入打磨。在 Android 完成离线，iOS 完成编辑器重构后，作为 flomo 重要的组成部分，flomo 101 （也就是 help.flomoapp.com ）也开始了深度修订的过程。  
>  早期 flomo 101 因为时间仓促，许多概念和案例没有打磨充分，希望利用这次修订弥补之前文章中的缺陷和遗憾。预计每篇修订幅度在 20% ~ 60% 左右，即使曾经看过的同学，再看一遍相信也会有收获。
> 
> 

![https://flomo-resource.oss-cn-shanghai.aliyuncs.com/101/image-65.png!webp](https://flomo-resource.oss-cn-shanghai.aliyuncs.com/101/banner101_resize.jpg!webp)
奥运五环想必大家都见过，但如果现在让你直接在纸上画一下，一定会有所迟疑：到底哪几个圈圈是连在一起的来着？（可以试试看能否毫不迟疑地画出来 😜）

为何这个我们司空见惯的标志，居然在回忆的时候并不笃定呢？

其实这涉及到我们「记忆」的三种方式：

* 编码：把信息输入大脑
* 存储：将信息保存在大脑的过程
* 检索：当需要的时候把信息从大脑中取出

你会发现，虽然我们曾经把「奥运五环」这个信息输入过大脑，但是很少有机会去「检索」它。我们在「[为何要写卡片笔记](https://mp.weixin.qq.com/s/jDmu56HkrwUGM6GFhPKstw)」中提到，不要收藏内容，而是尽量用自己的话记录，才更容易被记住。这固然能加强编码和存储的强度，但是也不能忽略了「检索」的价值。

![来源：EfratFurst](https://readwise.io/reader/imgproxy?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/re_review_000_resize.jpg%21webp&hash=1daaac2eddc302e3d7947d2b4f7428b1)
我们如果只是记了许多笔记却不再回顾，就像从不做「检索」一样，导致许多知识在用的时候想不起来，当时投入的时间也就被浪费了。

**为自己设计一个回顾的机制，让记录的东西真的能用起来。**

某日我曾在 flomo 记录了一篇 [关于芒格多元思维模型误区的文章](https://mp.weixin.qq.com/s/2W1vHDKBLSG1d63uH55d9A)，不过也只是顺手摘抄一遍而已。后来通过回顾意外地再次看到，感觉内容无比陌生…，但又觉得还是有启发，于是强迫自己再次把思考写在下面，以及写下了具体的应用场景。读文章的时候要做仔细地分析，**让其和已有的知识网络关联的更加紧密。**

虽说好记性不如烂笔头，但更关键的除了写下来之外，还要经常回顾，常看常新。

![Untitled](https://readwise.io/reader/imgproxy?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/re_review_001.png%21webp&hash=2bff77a6012cb728dcdaa14aacb43bee)
###  利用好 flomo 的几种回顾方式

先分享一下我在使用 flomo 时回顾的习惯：

![Untitled](https://readwise.io/reader/imgproxy?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/re_review_002_resize.jpg%21webp&hash=457ff8284b40e5b0a06db287ba05c72e)
在手机首屏上，放置了每日回顾的小组件，回顾条件为 #Books （即读书的记录）。

这样每次解锁都能看到过去做的读书笔记，并且由于遵循了卡片笔记的记录原则（用自己的话写），所以回顾的时候，并不需要重新理解大段内容，而是回顾一个个知识点，所以很容易在一些碎片化的时间里面重温。

PS：如果你导入过得到App 内的笔记，那么回顾机制是更好发挥过往笔记价值的重要方法。[如何导入请点这里](https://mp.weixin.qq.com/s/b0jF4nEExnYpAiY76o4aVA)。

另外还设置了微信回顾：每天晚上 10 点，会通过微信回顾推送「日记」里面的内容。

这么做，一来是当个提醒工具（😜 开发者滥用自己设计的功能），二来是翻看下往日自己的一天如何，有哪些良好习惯应该保持，有哪些槛当时觉得很难迈过。以及更关键的是，当时做的决策，在今天看是否正确。

接下来，正式介绍一下「被动回顾」和「主动回顾」。

####  被动回顾

Android 最新版本（2.2.0）支持 3 种回顾方式（iOS 用户别急，搞完 iOS16 适配就会跟上）

![Untitled](https://readwise.io/reader/imgproxy?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/re_review_003_resize.jpg%21webp&hash=b121a804e5f9c7e0b0525d6ba678b5a7)
**小部件回顾**

可以放在手机的主屏或者负一屏。因为解锁完就能看到，且每日可以手动刷新 24 次，适合回看偏知识类的内容，加强自己的记忆。

为了满足对视觉型共建者的回顾，新版本我们还支持了图片回顾。这样一些摄影美术作品、每日穿搭或装修参考等偏视觉需要的 MEMO ，也能重新发挥价值。

![Untitled](https://readwise.io/reader/imgproxy?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/re_review_004_resize.jpg%21webp&hash=1cd39566a8652392f7fc8fd6a5b80da5)
**微信回顾推送**

由于服务号的推送很强烈，依旧还是每日三条不同规则的限制（这个梗取自「三省吾身」，当然这个三是刻意为之）。

由于推送的频次低，更适合推送一些需要沉浸思考的东西：如过往的日记、一些待思考的问题、曾经做过的决策记录等。同时由于需要思考，时间最好在清晨或者晚上，才不至于被轻易地划掉。

![Untitled](https://readwise.io/reader/imgproxy?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/re_review_005_resize.jpg%21webp&hash=d0fc936a48c837984ef04604f8f66aca)
**推送（Push ）回顾**

这是新增加的回顾方式，打扰强度和数量都介于上述两者之间。根据过往经验，建议尽量不要设置太多条（虽然上限是 24次/天），最好是用来回顾偏知识类的内容。

当然每个人的习惯不同，再加上刚开发完我们也在摸索，如果你有好的用法，也记得告诉我们。

对了，由于国内厂商对 Android 系统的改造，如果推送一直没有触发，请检查是否给 flomo 开了推送和自动启动权限。请放心，我们没有接入第三方推送，不会流氓地唤起其他产品。

![Untitled](https://readwise.io/reader/imgproxy?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/re_review_006_resize.jpg%21webp&hash=b1750a0b4a490b8a93d3ef91435750bc)
####  主动回顾

其实在我们的规划中，flomo 除了被动回顾，还应该有主动回顾。

[在南添老师的分享中提到](https://mp.weixin.qq.com/s/m4lE22Czam-1Xxgn3w6bsA)，他会在每次想要输入的时候都会先找到对应的标签，然后快速浏览下之前记录的内容，强化下之前的记忆，然后才开始继续输入。**这样每次记录，都是一次回顾的机会。**

不要小看这种方式，配合南添老师自己对记录的要求 —— 只记录事实，假如每天记录 1 张卡片，那么每年至少回顾了 300 次，这会成为你大脑中一笔不小的知识财富。

除了这种记录时的回顾，还有一种主题性的回顾。

我在[小报童](https://xiaobot.net/)上开了一个小册，叫《[产品人的 100 本非必读书](https://xiaobot.net/p/pm-play-book)》，其中大量的笔记都来自于日常读书中在 flomo 记录的卡片。每隔一阵，我都会从近期读书笔记的标签下，把对应的内容再浏览一遍，然后整理至这个小册中。

**给自己设定一个周期性回顾的契机，把过往的记录再次整理归纳。**

###  回顾背后，其实需要一套系统

那么回顾只是看一眼加强记忆么？

如上面的分享，这固然是一种方法，但还可以考虑下面的几个操作：

发现了么？其实回顾并不是一个「功能」就解决的问题，而是需要你来搭建一套属于自己的记录 - 回顾系统：

* 任何系统都不能帮你决定「该记住什么」。你必须自己决定要回顾的目标什么，这样有意识的行为，才能让回顾更有价值。而这背后，则是关于「领域」的选择（详见「[不知道积累什么知识？](https://mp.weixin.qq.com/s/SfjJtbcojOmWpQIYTMrQyQ)」）。

最后，请别着急。因为记忆的塑造周期，本来就很长。正如《为什么学生不喜欢上学》的作者丹尼尔 · 威林厄姆教授（Daniel Willingham）所说：

> 你应该这样看待记忆：它是思想的残留物。这意味着你对某件事情想得越多，你以后就越有可能记住它。
> 
> 

What you did do was think about those things. And here's how you should think about memory: it's the residue of thought, meaning that the more you think about something, the more likely it is that you'll remember it later.
