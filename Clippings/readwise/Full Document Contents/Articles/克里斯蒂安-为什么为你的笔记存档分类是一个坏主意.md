---
人员: 
  - "[[克里斯蒂安]]"
tags:
  - articles
日期: 2015-05-16
时间: None
链接: https://zettelkasten.de/posts/no-categories/
附件: https://zettelkasten.de/img/blog/201505160917_constructionsite-thumbnail.jpg)
---
## Document Note

## Summary

A Zettelkasten is a personal tool for thinking and writing that creates an interconnected web of thought. Its emphasis is on connection and not mere collection of ideas.

## Full Document
我的一位朋友最近寻求入门帮助。对他来说，克服最初的不确定性非常困难：你怎么知道你已经准备好开始填写你的笔记存档了？在开始之前您应该准备哪些类别？

我的回答让他大吃一惊，而且非常简单：不要。不用准备。不要发明类别。就让它来吧。

当你开始时，记下前十几个音符是一次冒险。没有铺好的路，一切皆有可能。而且，事情一开始就需要花费大量的时间和精力。

通过实验，这种情况将会改变，你会更快。当你读到第 100 条注释时，你就已经为自己确定了惯例和规则。但在你自己的笔记惯例形成之前，你必须在每一步上做出有意识的选择。

为了将最初压倒性的复杂性降低一个数量级，一个非常流行的做法是在做笔记之前充实类别。有了类别，归档笔记和检索它们就会变得更容易——至少推理是这样的。

让我向您提出一些反对这一理由的论据。

#### 为什么要对类别说“不”

类别系统遵循[常识](http://web.archive.org/web/20140822053423/http://takingnotenow.blogspot.com/2009/10/darnton-on-commonplace-books.html)。据说，“那里”有一个主题层次结构，您可以用类别来表示。我不相信事物存在自然顺序，但我们可以同意这一点：纵观人类历史，共享分类法已被证明是有用的。有些分类法仅限于您的特定工作场所或您的技能，例如传入邮件应归档的位置，有些分类法在语言社区之间共享。它们帮助以普遍理解的方式检索信息。

分类有助于协调工作，并且是协调分工的必要条件。当我们区分最基本的材料时，比如为建筑选择木板或金属板，一切就开始了。

类别通常分为子类别。保持层次结构的顶层简单，并在较低层进行分支。如果您找不到放置某物的位置，从顶部开始应该有助于找到最合适的存储桶。

当您创建知识系统时，从通用的类别层次结构开始似乎是合理的。您可能已经对自己感兴趣的领域有了一个心理分类法，或者您可能想要复制[杜威十进制分类](http://en.wikipedia.org/wiki/Dewey_Decimal_Classification)之类的东西，为*一切*做好准备。

创建类别是一个自上而下的过程。您从结构开始，然后将材料归档。注释必须适合结构。如果他们不这样做，就必须做出妥协。

#### 有机生长与大脑–Zettelkasten-Fit

拥有 Zettelkasten 的关键在于高效。大脑与 Zettelkasten 的契合度很重要。您的知识管理系统越适合您的思维方式，您放入其中的内容就越容易使用。

我们的大脑不处理类别，至少一开始不处理。它可能会发明类别来提高效率，但我们的生活并不是从预先存在的类别开始的。

在这种情况下，众所周知的[空石板](http://en.wikipedia.org/wiki/Tabula_rasa)就是一个很好的比喻。当你学习新东西时，你会收集信息并随后理解它。如果你知道一些相关的事情，你的大脑就会利用现有的信息来加速这个过程。这可以称为[同化](http://web.archive.org/web/20160305015224/http://www.learningandteaching.info/learning/assimacc.htm)：通过利用现有东西的方式学习新东西。使用现有的神经通路。当你学习一门新语言的第一个单词时就是这种情况：你翻译并记住。[然后是适应](http://web.archive.org/web/20160305015224/http://www.learningandteaching.info/learning/assimacc.htm)模式，我们开始改变我们的思维方式，以更好地理解我们感兴趣的对象。那时你就可以放弃你的第一语言来理解新语言。（所有这些现在都非常粗糙，并且经过手动简化。）

当知识增加时，我们所知道的事物网络就会有机增长。

另一方面，人造建筑却没有增长。它们是建造出来的。

一组共享类别就像这样：它构建一次并永远使用。

像这样的刚性结构不太可能改变。它们必须被分解或更换。另一方面，我们的大脑永远不会停止吸收新信息并扩展。不必将其关闭。神经通路随着使用而增强，因忽视而减弱。它们永远不会被切断作为学习新东西的先决条件。他们不会被拆散。

因此，如果您对知识管理系统进行建模以适应您的大脑工作方式，那么您最好不要从自上而下发明类别层次结构开始。相反，你最好开始收集笔记并看看会发生什么。让你的 Zettelkasten 中的事物成长，就像你让你的大脑发挥其作用或有机生长一样。

主题簇会自行出现，尤其是围绕关键字或标签。生成的存档符合您的想法，因为它根据您的兴趣而增长。此外，事物的标签方式对*你*特别有意义，而不是对其他任何人。这都是关于*个人信息管理的*，因此个性化是必须的，增加个性可能会让事情变得更好。

如果您在设置知识管理系统时遇到困难，请停止设置任何内容。只需添加信息即可。将文本存储在您喜欢的文件中，如果您不确定要使用哪个软件，请将它们放入文件夹中。开始做一件事总是比不做要好。如果不亲自动手，就无法分析进入完美系统的方法。只有经验才能揭示瓶颈在哪里，以及您是否真的会使用（或错过）超级昂贵的应用程序*Y的超级棒功能**X*。

我选择的唯一顺序是创建日期。我的 Zettel ID 被添加到文件名之前，并且由于 ID 是日期时间戳，因此单个存档文件夹中的所有笔记均按日期排序。[我这样做是因为野口归档系统](http://markforster.squarespace.com/blog/2008/6/9/noguchi-filing-system.html)的一件事对我来说很有意义：人类可以很好地记住事件的时间，因此您应该按日期订购材料。我想要一种快速、低技术含量的方式来回答诸如“我在 2011 年春季做了什么？”之类的问题。– 之前的日期就实现了这一点。就是这样。但我可以轻松地没有它。

所有这一切都是非常解放的：您不必提前想出一组足够详细的类别。当你完全放弃类别时，你甚至不能在设置过程中做任何错误，因为你根本没有设置任何东西！

一切都从一个词开始。创建一个注释。保存。继续添加更多东西。然后看看几个月后会发生什么。

如果支撑结构灵活，则很容易纠正航向。相信你的创造力会随着时间的推移找到支持性规则。
