---
人员: 
  - "[[AI沃茨]]"
tags:
  - articles
日期: 2025-01-28
时间: None
链接: https://mp.weixin.qq.com/s/kmDXGiKWLWNfUiWJohU-bg
附件: https://mmbiz.qpic.cn/mmbiz_jpg/YEhakvKZjXk8icSloJku90RbOXYVWb1pyj06CyXRmKAcmbmktFYoZ3XAYPIFZHx4jYxxS7cGUOWenBrjC2yVUmg/0?wx_fmt=jpeg)
---
## Document Note

## Summary

这回真成神秘的东方力量了

## Full Document
![Image](https://mmbiz.qpic.cn/mmbiz_gif/YEhakvKZjXk8icSloJku90RbOXYVWb1pyZcTDGW2dqohibTvpZj7325n1v5jf9Qb0S6VAicOIkqFC8BFQNOb67bhQ/640?wx_fmt=gif&from=appmsg)
***DeepSeek，可能是个国运级别的科技成果***-- 《黑神话：悟空》冯骥

DeepSeek 这几天可太火了，

吓到奥特曼宣布 o3-mini 免费了；

吓得英伟达市值蒸发$5890亿，把一个星际之门赔进去了。

牛到中日美澳 App Store 下载榜排名第一。太多热搜就不一一盘点了，连我奶奶都开始问我要怎么用 DeepSeek了。再加上有很多很多人来问我，啥时候能出一期 DeepSeek 的使用指南，GPT 的已经看腻了。

所以我觉得农历新年第一天应该来点大动作！让大家都丝滑用上R1。

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/YEhakvKZjXk8icSloJku90RbOXYVWb1pyZZ8tU2Jq2smtewvIB8dRjzm1X3zsXsQXa4SErAKTzuVdlZ45icTsFww/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
DeepSeek 的基础使用上跟其他大模型没太大不同，网页版以对话界面为主，同时可以通过 API 接入到各种应用中。这几天我将目前衍生出来的所有玩法总结成了下面10类：

**深度思考+联网搜索、多模型联动、无限制翻译、中文写作、Agent编程（Cursor穷👻套餐5.0）、电脑and手机本地运行R1推理模型、R1蒸馏模型、低成本复现R1**

能用上 DeepSeek 的场景太多太多，一小段话根本概念不全，Here we go！

PS: 文章用到的所有工具都会整理到飞书文档，大家在公众号后台发我`ds`就行

一、基础使用

我先用一段很短的文字来科普一下 DeepSeek 的基础玩法，让阅读门槛降到最低，大家都可以分享给自己的亲朋好友。

DeepSeek，也可以说这次火出圈的主要是 DeepSeek 开源版的R1，跟 OpenAI 的o1、o3一样，都属于推理模型。简单来说，推理模型跟一般的语言模型最大的不同是它们会“思考”，给出答案前先思考。就是这样一个“简单”的步骤，能给模型能力带来了很大的提升。

R1 不是第一个推理模型，发布的时间也不是最早的。在我看来，R1的重要意义在于`便宜`、`开源`、`能跟 o1 掰手腕`，还提供了一条“通天大道”！更重要的是，R1 变相揭秘了OpenAI技术细节，排除了 DPO、MCTS等模型优化概念，告诉所有人，传统的强化学习（Reinforcement learning）照样是力大砖飞，打破 OpenAI 的技术障碍。

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/YEhakvKZjXk8icSloJku90RbOXYVWb1py55GzSKaTPjE0ibxR6kkPKGUCMwhIlqRv2NFjq0LlHTFKuGt0qQItNyQ/640?wx_fmt=jpeg&from=appmsg)
很多人关注点在于`用很少的卡也能训练出效果差不多的模型`，但实际上使用传统 RL 将模型提升到 o1 水平意味着其他所有人都走错了。而且 DeepSeek 还发现训练过程模型自动就能学会反思，意味在这种方法上堆更多的卡就有可能直达人工通用智能（AGI）或者超人工智能（ASI）。

使用 DeepSeek 很简单，访问`https://www.deepseek.com/`

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/YEhakvKZjXk8icSloJku90RbOXYVWb1pyOu9H5bz6icx7N7XGfEKqNgVVQhlbGdhcqIlEjULIphQeqlN2V0xCA6A/640?wx_fmt=jpeg&from=appmsg)左侧是对话入口、右侧手机App、右上角是API 
对话界面相当简单，我在图上就已经划分出了每个区域的功能。

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/YEhakvKZjXk8icSloJku90RbOXYVWb1py0Wat8UGcqMz201iaZtglcCS2npkkiapI3hoLd8WQ34nvzP86BhWjtJIg/640?wx_fmt=jpeg&from=appmsg)
DeepSeek 的 API 价格基本上是 OpenAI 的3%左右，后续在多模型联动、无限制翻译、本地运行R1推理模型等多个场景都可以接入 API，所以我这里也做了一个简单的录屏，帮助大家获取 API Key。当然视频的 API key 仅供展示，我已经作废了。

![Image](https://mmbiz.qpic.cn/mmbiz_gif/YEhakvKZjXk8icSloJku90RbOXYVWb1pyKrKiaQhxciapH20TlwkicaYmhlJ8u76o4W6g4Jn9iaibDyic8NI1d8myZr0w/640?wx_fmt=gif&from=appmsg)
到这一步，你就已经学会 DeepSeek 的基础使用了，接下来我们马上来玩点进阶的，将 R1 的能力彻底解放出来。

二、深度思考+联网搜索

DeepSeek R1 是现阶段第一个支持联网搜索的推理模型。使用方式同样简单，在网页端同时勾选`深度思考`和`联网搜索`就行。

跟以 Preplexity 为代表的 AI 搜索不同，AI 搜索是先将复杂问题分解，然后分步骤解决问题，进而提高问答质量。**而 DeepSeek 会先进行联网搜索，然后利用 R1 进行思考，再给出答案。**

我们来测一下最简单最困难的问题：

**“总结一下昨天重大的AI新闻”**

不出意外，DeepSeek已经成为我的 AI 搜索Top1，没有之一。之前的`联网搜索`能很好解决某个专题的搜索，比如说“星际之门是什么?”、“o1是什么？”、“o1和o3有什么区别？”，但对于时间刻度来说，问10次有9次时间线都是乱的。DeepSeek 应该是也留意到这个问题了，从它的思考过程上看，R1 会反复强调信息的时效性，也就是“昨天”。同时，推理模型的优势还支持它判断这条信息应不应该纳入后续的思考过程。

二、多模型联动

这个也是最近才出现的模型玩法，号称是`推理模型的正确打开方式`。

`多模型联动`的方法论是2类模型（聊天模型、推理模型）同时使用，聊天模型（DeepSeek V3、GPT、Claude等都行）通过多轮对话来细化你想要的细节，然后将一个能满足你所有需求的提示语，发给推理模型，推理模型能一次性输出结果。

视频里我并没有用任何提示语的技巧，前后区别仅仅是将我跟聊天模型的对话后得到的上下文作为输入的一部分，一并发给 R1。这就已经可以快速得到我想要的效果。

之所以这样做，是因为跟推理模型对话最重要的是更长的上下文，最好是一个问题相关的所有上下文，通通丢进去。这时候推理模型的思考时间就体现出新的价值了。关于这个方法的细节我之前已经做过一个专题，感兴趣的可以看看。

[别再跟模型对话了，我找到了OpenAI o1的正确打开方式](https://mp.weixin.qq.com/s?__biz=Mzg3MTk3NzYzNw==&mid=2247492234&idx=1&sn=6be49740969d93782089b4c486465d27&scene=21#wechat_redirect)

最后简单总结一下，

**「推理提示词」的目的在于引导模型如何思考；** 

**「指令提示词」的目的在于告诉模型我要什么。**

当然这个方法不局限于 DeepSeek R1，你还可以用 Grok 搜索相关的最新背景信息，然后丢给 Gemini Deep Research，在这个基础上浏览互联网上的大量信息，生成一个研究报告，最后让Claude解读和讨论这个报告。（热血组合技）！

PS：方法总结来源于：即刻-VION\_WILLIAMS、即刻-Odysseys.eth、X-@karminski3

四五、无限制翻译 & 本地部署R1

从这里开始，我会同时介绍 API 和本地模型。

![Image](https://mmbiz.qpic.cn/mmbiz_png/YEhakvKZjXk8icSloJku90RbOXYVWb1pychRIzJlDraWde60icAUV4ER6SqMy0Xsj6WjLe9HOrZw7myRGZUKFibTw/640?wx_fmt=png&from=appmsg)
翻译可以说是日常使用最多的情况，我的AI翻译之路可以分为三个阶段：

* **一阶段**：畏畏缩缩，API太贵，只敢选择局部段落翻译
* **二阶段**：绞尽脑汁，将各家免费API聚合起来，分散压力（用 Gemini API 来翻译网页的话会因为一次性发送的请求过多被封）。
* **三阶段：免费，还能享受 DeepSeek 的中文优势。**

还是先打开我们的老朋友，`沉浸式翻译`，它可以翻译PDF、word等文档，同时还支持翻译网站。打开`设置`后选择`翻译服务`，找到`deepseek`后就可以填入上面申请的 API Key。

搭配@宝玉大佬的意译提示语，

**“请尊重原意，保持原有格式不变，用简体中文重写下面的内容：”**

这时候翻译的内容就已经能接近人工校正后的翻译读物级别了。

![Image](https://mmbiz.qpic.cn/mmbiz_png/YEhakvKZjXk8icSloJku90RbOXYVWb1py1LficBGNkYNfHzUFzL0vFSCe6aJOoBAACUBkWibbhjAxEY1f8W2oXEicw/640?wx_fmt=png&from=appmsg)
接下里我要用本地模型取代API key，做到真正意义上的无限制、无费用AI翻译。（后面的代码、写作、对话等也可以用到，一鱼多吃）

首先，我们打开 ollama，https://ollama.com/library/deepseek-r1。

按照你的电脑内存大小来选择对应的推理模型或者蒸馏模型，个人建议电脑内存在32g内，运行14b以下的模型，我电脑是 32GB 的 M1 Pro，选择了 deepseek-r1:7b 版本。

打开电脑里的任意命令行、终端后，就可以运行下面命令完成安装。（如果不知道如何打开命令行，可以带上你的电脑系统和型号，问问deepseeek😃）

```
## 安装命令  
ollama run deepseek-r1:14b  
## 安装成功后的检查命令  
ollama ls  
## 让沉浸式翻译能访问ollama  
export OLLAMA_HOST="0.0.0.0:11435"  
OLLAMA_ORIGINS="*" ollama serve  

```

![Image](https://mmbiz.qpic.cn/mmbiz_png/YEhakvKZjXk8icSloJku90RbOXYVWb1pyicEFX2DF4reCE3zPCQFQExUmRFiatKibTlvicVKDrsdpUibgDkTTEbkQvuQ/640?wx_fmt=png&from=appmsg)看到这样的输出就是成功了 
按照下面的视频里的操作，回到沉浸式翻译的`翻译服务`,选择 `OpenAI`，分别设置：

* api\_key: ollama
* 自定义模型: deepseek-r1:7b
* 自定义 URL 地址: http://localhost:11435/v1/chat/completions

实机演示翻译速度相当之快。

六、中文写作

拿 DeepSeek 来写作绝对是降维打击，半个中文圈的人都看来写古诗或者短篇。

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/YEhakvKZjXk8icSloJku90RbOXYVWb1pyQ3PutgNWnibzmQuCia54FnJUd9HIZC1PgP6D7SAib1L2f3SNHHgrLYkGg/640?wx_fmt=jpeg&from=appmsg)来源 @歸藏 
先来一个好玩的，有了联网搜索后，你可以任意模仿网络上任意一个博主的写作风格

**请参考鲁迅的风格，写一段介绍 DeepSeek R1 能力的段落**

DeepSeek鲁迅的答复：

**这世上本没有智能体，用的人多了，也便成了路。诸君莫要看这DeepSeek R1生得方头方脑，倒像极了茶馆里跑堂的伙计——它虽不穿长衫，肚里却藏了四库全书的学问。那些个"实时搜索"、"多模态交互"的洋名词，在它不过是雕虫小技，真本事在骨子里：你道它是机械听差？它偏能解你眉间三寸愁云；你当它是铁皮鹦鹉？它倒识得人间冷暖百态。昨日王掌柜对着手机吼了半宿，要寻三十年前老照片，这厮竟从数据废墟里扒拉出泛黄的记忆来，教人疑心它偷藏了月光宝盒。所谓"个性化推荐"，原不过是阔人们消遣的玩意儿，偏它学得市井烟火气，连胡同口张婶腌咸菜的方子也能说出个子丑寅卯。依我看，这劳什子最妙处，倒不在会讲西洋话，而是把那些个"人工智能"的幌子撕破了，露出底下活生生的人味儿来。（大家觉得够鲁迅吗？）**

还有一个更牛🐂🐝的玩法，用不同的写作风格直接重写其他语言的内容，我之前尝试过GPT、Gemini、Claude等模型都达不到理想的效果。没想到DeepSeek抢先实现了！

请使用鲁迅的风格用中文重写下面的内容，同时保留原文核心内容和结构。

看来我之后的写作速度能加倍了，25年计划实现日更！

七、Agent 编程

上次我使用 **cursor+codeium+cline3+openrouter+simple-one-api+markdown+auto-switch-prompt** 给 Cursor 装配上 Tab 代码补全、AI Agent、全系大模型接入、开发进度管理、状态回滚等功能。

[4款插件2个API，我把Cursor“迭代”成了满血超频版程序员](https://mp.weixin.qq.com/s?__biz=Mzg3MTk3NzYzNw==&mid=2247492154&idx=1&sn=cf88a7ebba6fe42c1db9e92a57425d21&scene=21#wechat_redirect)

但是还是没能解决 Cursor 的 Apply 功能，也就是将 AI 生成的代码直接写入到文件里。这次我挖到了Roo Cline，这款插件是从 Cline 分支出来的，现在已经改名成 Roo Code 了。

先插一嘴，R1用来写代码的性能，搭配 Cluade Sonnet 已经超过了o1，达到了第一名。

![Image](https://mmbiz.qpic.cn/mmbiz_png/YEhakvKZjXk8icSloJku90RbOXYVWb1pytziayVY5MRswibIomryESWKmnMQz5waZX7jOd98x0nRY7uMvThmgQpnA/640?wx_fmt=png&from=appmsg)
由于操作过程步骤较多，我都放到了下方的视频当中，基本流程就是在Cursor 的插件市场里面搜索 Roo Code，配置上 DeepSeek 的 API key就可以生成代码并写入文件了，到这一步我的 Cursor 穷👻套餐算是基本圆满了，Cursor Pro 的功能都被包圆了🎉

八、手机上运行DeepSeek

这两天 DeepSeek 压力过大，有时候API和网页都用不了，这时候还有骚操作。一款名叫 fullmoon 的免费应用支持直接部署本地模型。

全程两分钟就完成应用和模型的安装了。

在手机上安装 1.5B 版本的R1，这样你就可以随意使用R1，实机演示的生成速度超快，用来简单写作，概念类问题的提问和学习，以及当作临时的替换模型，简直一绝。

九、复现R1

到这一步，我们已经完成了DeepSeek的网页版、API、本地模型等使用场景，那下一步是什么？

我想应该是`开源模型正超越专有模型`，目前我最关心的项目有3个：

1. **港科大宣布完成了 R1 模型的复现和开源。**

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/YEhakvKZjXk8icSloJku90RbOXYVWb1pySYSiaIpaQpkxW5yA1uHkHDXRPMLRSJ31KOEicQibWe04bJVyIfmyXFZng/640?wx_fmt=jpeg&from=appmsg)
他们从 Qwen2.5-Math-7B 开始，直接开始强化学习。使用了8K数学领域的训练数据，提升效果显著，部分验证R1的威力

项目链接：https://hkust-nlp.notion.site/simplerl-reason

2. **大洋彼岸也有在复现 R1 的，TinyZero 项目成功用30美元，基于verRL框架训练出一个复现了思考顿悟能力的3B模型**

![Image](https://mmbiz.qpic.cn/mmbiz_png/YEhakvKZjXk8icSloJku90RbOXYVWb1pyp3NCG91e5KxhFGRj6wpUQz0Xv4SGls3fic2ncdJyMgw7wx6IM98n8hQ/640?wx_fmt=png&from=appmsg)
项目链接：https://github.com/Jiayi-Pan/TinyZero

3. 最**后一个也是我最关心的，是赛博模型开源大户HuggingFace发起的项目`Open-R1`**

![Image](https://mmbiz.qpic.cn/mmbiz_png/YEhakvKZjXk8icSloJku90RbOXYVWb1py8VVZ0Fdyc7bEHqH57iclpibbbBZXEv0a3utvkwAe0GSKMFyGhXNjdhTA/640?wx_fmt=png&from=appmsg)
也称为 DeepSeek-R1 的开源复现计划，也是目前我看到最完整的复刻流程。

* 第1步：用DeepSeek-R1蒸馏高质量语料库，来复制R1-Distill模型。
* 第2步：复制DeepSeek用来构建R1-Zero的纯强化学习（RL）pipeline。这可能涉及为数学、推理和代码整理新的大规模数据集。
* 第3步：通过多阶段训练，从基础模型过渡到RL版本。

Open R1项目首先要实现的，是用 R1 数据蒸馏小模型，看看效果是不是像DeepSeek说的那么好。

项目链接：https://github.com/huggingface/open-r1

写在最后

呼，可算是将这篇 DeepSeek 使用指南提升到了我理想的状态。

相信不管你是普通用户、专业用户、或者是第一次接触大模型的用户都能在这篇文里收获些什么。有了这10大技巧，预计性能还不到 o1 的 o3-mini 算是凉了，怪不得奥特曼打算提前开放给免费用户使用，估计要上 o3 来镇场子了。

R1后时代会发生什么？我希望是推理模型大爆发、模型能越做越小、强化学习和蒸馏技术能有更多的玩法，催生出更多的 R1 Time。

有一点我觉得很有宿命感的是，当初 OpenAI 在所有人看好 Bert 的时候，坚持 GPT 这条路，最终诞生成了 GPT3、3.5、4、4o、o1、o3 等系列。而现在DeepSeek坚持了强化学习这条路，R1 也成功诞生出来了。

文章的最后，我想用 DeepSeek 创始人梁文锋的一段采访作为结尾：

英伟达的领先，不只是一个公司的努力，而是整个西方技术社区和产业共同努力的结果。他们能看到下一代的技术趋势，手里有路线图。中国 AI 的发展，同样需要这样的生态。

很多国产芯片发展不起来，也是因为缺乏配套的技术社区，只有第二手消息，

所以，

中国必然需要有人站到技术的前沿。

@ 作者 / 卡尔 @ 动手学AI知识库 / learnprompt.pro

最后，感谢你看到这里👏如果喜欢这篇文章，不妨顺手给我们*点赞👍｜在看👀｜转发📪｜评论📣*更多的内容正在不断填坑中……

![Image](https://mmbiz.qpic.cn/mmbiz_png/YEhakvKZjXk8icSloJku90RbOXYVWb1pyxJULRMfhFBQo6k6U1I1dwA3077ZxFCAJAekCNf9S7wq5jEXhrmggkA/640?wx_fmt=png&from=appmsg)
