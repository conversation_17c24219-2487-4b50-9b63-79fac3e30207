---
人员: 
  - "[[docs.google.com]]"
tags:
  - articles
日期: None
时间: None
链接: https://docs.google.com/spreadsheets/d/1KuIr3VezFs1fvV4mtyImBAanVBDCjHoE/edit?pli=1#gid=582119409
附件: https://lh7-us.googleusercontent.com/docs/AHkbwyImq6u68BHLPlE9sSAyahUdsDLrpvoWVLnqc4Xk03lfghMzNd9Z7GftoUei7pmtYhba_V6sR4S3dM5W3f9IeubxMzMyh4Ew=w1200-h630-p)
---
## Document Note

## Summary

这篇文章介绍了PikPak，一款在电报TG上热门的网盘APP，提供强大的离线功能和方便的机器人服务，使用户可以轻松保存资源到自己的网盘。用户可以在网盘里观看视频的原画质，尽管有时会出现卡顿情况，建议使用梯子进行观看。此外，PikPak还提供第三方的Windows、Mac版本，并每周五下午6点发布会员兑换码。

## Full Document
It looks like saved a private Google Doc to Reader. Unfortunately, these private Google Docs cannot be parsed into Reader like other documents on the web. However, you can get this document into Reader two ways.

First, you can make it public.

Second, you can export it as an EPUB and upload to Reader. Here's how:

1. **Open the document** in Google Docs.
2. **Go to** `File > Download` and select `EPUB Publication (.epub)`.
3. **Save the file** to your device.
4. **Upload the EPUB** to Reader.

If the Google Doc is public and you're still getting this message, please report a parsing issue.
