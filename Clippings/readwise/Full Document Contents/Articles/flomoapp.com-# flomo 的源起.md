---
人员: 
  - "[[flomoapp.com]]"
tags:
  - articles
日期: 2020-12-12
时间: None
链接: https://help.flomoapp.com/about-us/about-us/flomo-origin.html
附件:
---
## Document Note

## Summary

## Full Document
一分钟有多少念头？在一次骑车的时候，15 分钟内我大概捕捉到了 20 多个想法。但过了一天，这些有趣的想法都消失不见了；每天，我都会阅读大概十几篇文章，将其中有价值的收藏，但一星期过后，似乎什么都想不起来了；兴冲冲的开了公众号，参加培训营，希望能提高写作能力，但是面对空白的文档，还是一筹莫展。

别担心，这种困惑不是你一个人。

#### [#](https://help.flomoapp.com/about-us/about-us/flomo-origin.html#flomo-是什么) flomo 是什么

flomo 是我和好友 [lightory  (opens new window)](https://lightory.notion.site/) 两个人一起创造的一款「思维工具」，主要目的是帮助你「记录想法的川流」。目前支持 iOS 、Android、网页，可以跨平台多端使用，也可以绑定微信服务号输入。

![](https://resource.flomoapp.com/101/images-20.png!webp)
![](https://firebasestorage.googleapis.com/v0/b/gitbook-x-prod.appspot.com/o/spaces%2F-MVEe4qiW5TBO3wosktr%2Fuploads%2FTMLWtLLVyAZHh3vxoCtx%2Ffile.gif?alt=media)
用接地气的话翻译一下就是，flomo 是一个只给自己看的微博，或者说是一个加强版的文件传输助手。像是一个对话框一样，脑海中蹦出什么想法，只需要丢进去就可以了。

其实稍微留心下身边的人就会发现，多数人没有记录想法的习惯。不是因为他们懒惰，而是今日的工具和方法，都进入了一个误区。或者在聚焦如何设计更好的「写作体验」，或者设计更丰富的「建立知识之间的连接」。

而大多数笔记产品都忘了，能成为作家的人少之又少，而需要思考的人却越来越多。相比如何组织知识和写出文章，我们更缺乏的是更多的记录，以及更多有效的思考。

#### [#](https://help.flomoapp.com/about-us/about-us/flomo-origin.html#flomo-的起源) flomo 的起源

我是 Notion 的忠实用户，很惊叹于 Notion 背后的故事。他们的初心和愿景，让我作为一个古典产品经理感到汗颜。所以在他们的愿景的驱使下，不但经营了一个基于 Notion 比较庞大的共享数据库「产品沉思录」，也给 Notion 带来了超过 200 位用户的新增。

但在日常推荐 Notion 给好友的过程中，我总有一种很强烈的挫败感 —— 这个工具到底该怎么用？比印象笔记好在哪里？block 是什么？database 又是什么？无论是之前写的《P.A.R.A 方法》也好，还是《何谓渐进式笔记》也罢，大家都会觉得很有道理，但是第二天却依旧重复昨日的习惯。

![](https://resource.flomoapp.com/101/images-21.png!webp)
**麦克卢汉说：我们塑造了工具，尔后工具也在塑造我们**

作为一个十年产品汪的直觉，我开始静下心观察身边的同事，究竟是什么工具塑造了他们今日的行为？通过观察其实让我还挺意外的 —— 除了产品经理和开发，可能会尝试一下下 Notion，更多的还是印象笔记或sublime等本地工具；而运营客服部门的同学，用有道云笔记的比印象笔记稍微多一点。更多的人会直接用电脑的备忘录和记事本，更让我意外的是，居然很多人会开个小号发给自己，以及发给文件传输助手。

**但几乎没有人不用数字化记录工具。**

进一步调查他们如何使用这些工具，发现一个有趣的现象：大多数人用这些工具的时候都很少分类，而且极少有完整的文档，标题基本上就是日期或者随手写了个什么。极少数人有整理归档的需求，每次问个东西要么是搜索，要么按照时间轴向前追溯。只有公司要求的文档格式，才稍微会记录一些。

![](https://resource.flomoapp.com/101/images-22.png!webp)
通过这些观察，我们渐渐得出一些观点：

* 随着移动互联网普及，知识工作者越来越多，势必需要一个数字化工具来长期管理信息和知识。
* 大多数思维工具都聚焦在如何撰写文档，整理知识。但却忽略了，大多数人的问题都不在整理，也不在于如何写文档，而在于许多想法都没记下来，是输入量不够。
* 随着每个人在公司的平均周期越来越短，每个人势必不能只依赖于公司的知识管理工具，而需要一个面向个人的工具伴随自己终身。

但到这里为止我们也没有轻易出手，而是在思考。什么样的工具形态能让大多数人输入无压力，记录更频繁。

slack（或者说文件传输助手）的形态进入了我们的视野，毕竟聊天应该是最没有压力的一种输入方式了。我们进行了两周的实验，只用slack的频道和Thread来记录想法和收集信息，发现虽然整理起来没有那么方便（后续还是在notion输出的文章），但明显记下来的东西变多了。

![](https://resource.flomoapp.com/101/images-23.png!webp)
有了对用户需求的定性和定量调查，但还不够。因为对于思维工具来说，重要的不仅仅是做出来一堆功能，而是去思考产品背后的理念和哲学是什么。其实 flomo 的理念也并非原创，而是站在前人的肩膀上，结合今日现状进行新的抽象和归纳。在这里着重介绍两位对我们产品设计有巨大影响的前辈。

![](https://resource.flomoapp.com/101/002.jpg!webp)
关于尼克拉斯 · 卢曼的卡片盒记事法在少数派已经有很多文章了，在这里就不多赘述。研究卢曼对我来说不仅是学习一种新的笔记方法，而是去学习他对问题的思考方式，对一些惯性的观念进行了重新的审视。

![](https://resource.flomoapp.com/101/images-24.png!webp)
**写作不是思考的结果，而是思考发生的媒介。** 写作可以很好地提高思维能力，因为它迫使你从更深的层次去阅读。就像学游泳一样，你必须通过实践来学习，而不仅仅是通过在纸面上的学习。

**没有人会从零开始一件事情。** 关于创造力最坑爹神话之一就是它从无到有发生的。空白的页面、白色的画布、空荡荡的舞台 —— 我们最浪漫、最普遍的艺术主题似乎在暗示，“从零开始”是创造力的本质。但正确的方法是，在你选择你要写的东西之前，你必须下功夫研究并积累。

**收藏往往没有作用。** 多数时候我们都会陷入「收藏者谬误」，感觉收藏就是学习到，但实际上「知晓某事」并不是「知道某事」。收藏更像是把信息搬运（而非加工），途中没有增加任何知识。

**为什么分类不重要。** 我们的大脑不会按照分类来工作，至少一开始不会。当知识增长时，我们所知道的事物网络就会有机地生长（而非搭建了某种结构，更像是植物。而非建筑物）信息被个人主观的理解，贴上标签建立连接，成为知识。每个人的体系都不一样，所以一定是个性化的，不断生长的。传统的分类无法满足这种生长的状态。

用全栈工程师定义 Andy Matuschak 似乎已经不够，他曾经在 Apple 参与 iOS 系统的建造，尔后在可汗学院领导研发工作，他经常游走于学术界和硅谷之间。

![](https://resource.flomoapp.com/101/tu-pian--5.png!webp)
「EvergreenNote」是 Andy 的工作笔记 —— 类似卢曼的卡片盒笔记法，每个「EvergreenNote」像是一张卡片，但是以一种很独特的方式呈现出来的。但重要的不是 Andy 工作笔记的形式，而是他提出的问题和笔记建议：

**知识工作者如何刻意练习？** 运动员和音乐家比知识工作者更严格的追求基本技能的精湛技艺。但是知识工作者对磨炼基本功并不认真，阅读随意，笔记随便，无法随着时间推移来积累，而且往往是临时性的实践。知识工作很少涉及刻意实践，典型的工作环境很少有刻意训练的环境，就像是玩游戏来锻炼身体一样。EvergreenNote 可以当做知识工作的基本单位，即每天记录了多少单位的笔记。

更好的记笔记”没有抓住要点；重要的是“更好的思考”。绝大多数的写作都集中在一些所谓“生活整理”式的框架上，专注于回答这样的问题: “我应该如何组织我的笔记? ”“我应该用什么样的日记? ”……这些问题集中在错误的事情上。**我们的目标不是做笔记，而是有效地思考。**

洞察力的飞跃取决于在这些主题上积累了大量的先前思考。有时候，这种积累完全发生在我们的潜意识中，但这有助于我们设计我们的外部认知系统，使我们的日常思考能够积累

为何要把读到的写下来？积累标签、保存 pdf 文件和制作书签看起来像是一种进步，但是我们系统地高估了它的价值。理解需要努力的投入。你不太可能从一个文件夹中得到太多的理解。我们收集素材是因为它很容易，也因为它消除了我们再也找不到我们要找的东西的焦虑。但实际上，我们往往只是把事情弄得更糟，把重要的材料埋在数以吨计的次要物质中，我们只是“不想失去”这种观念与知识工作应该积累的观念形成对比。

除此之外，阳志平老师、奥野宣之的《如何有效阅读一本书》、以及 fonter 的《[Notion 的支撑者  (opens new window)](https://mp.weixin.qq.com/s?__biz=MzA4MjkwMDcyOQ==&mid=2647743399&idx=1&sn=8684046cb4a74b5dd7651693b4f8398c&scene=21#wechat_redirect)》都对我们设计产品有莫大的启发。

基于上述调查和前辈们的观点，我们把所有的精力都聚焦于记录想法，而非整理文档。所以我们把整个产品分为两个大阶段 —— 也许需要若干年才能完成。

* 降低输入摩擦，提高输入动机
* 建立回顾体系，协助整理归纳

目前我们只是达到了第一阶段，所以就针对这部分的思考进一步展开分析。

让用户能尽量多的无压力的记录下来自己的想法。但不鼓励收藏和导入，因为这样本质上还是知识的搬运工。

* 降低摩擦：微信是大家使用最频繁的工具，而在输入框输入是摩擦最小的场景，所以我们开发了服务号输入。在提供 API 之后，目前已经能支持 iOS 快捷指令、Alfred、Automator 等快捷输入方式。
* 无压输入：flomo 的输入框更像是微博，并且仅支持一点点Markdown 语法，不支持图文混排。尽量消除排版的可能。
* flomo weekly：通过每周的周刊，推荐相关的使用心得和笔记理念给 flomo 用户，降低大家使用 flomo 的门槛，学到更多使用情境。

量化用户的输入反馈，让用户能感知到自己记录后带来的价值。但不会做分享或签到激励，扭曲用户的动机。

* 参考 Github / 豆瓣的贡献墙设计，帮用户即时看到自己积累的 MEMO 数量，获得记录的成就感。
* 建立每日回顾体系，通过服务号根据条件下发历史记录的内容，提高用户对过往记录的感知。

所以许多用户反馈我们不支持 Markdown 语法，不能修改字体和颜色，不能一键收藏的原因，便在上面的思考里面了。

在上周的 flomo weekly 上，我写到 flomo 的愿景：

我们敬佩 Notion 背后的初心，但我们不打算拷贝 Notion 的功能做成 All in one；我们也赞叹 RoamResearch 信息架构之精妙，但我们也不打算借鉴 Roam 的双向链接；我们感慨石墨背后编辑器的耗费的精力，但我们不打算成为另一个文档编辑器；当资本和创业者都在关注企业协同的时候，我们选择关注个体。

**我们只是朴素的关注着你，未来一年，未来三年，未来五年的你。**

当你需要复盘和回顾自己的时候，希望能在 flomo 里面找到过去的自己。而不是把他们丢在公司的 wiki 或者企业 IM 的聊天记录里。

**我们不仅关心你的过去，还关心你的未来。**

想象一下，当我们拥有了成百上千的 MEMO，根据你的需要摊开在一张空白的桌面上，根据你的需要自动排列和组合，然后围绕一个主题合并成一篇文章，归档输出。这样你就再也不用担心思维的枯竭，而许多丢失的创造力，也能因此寻回。

我们在路上了，欢迎加入我们，同路人。
