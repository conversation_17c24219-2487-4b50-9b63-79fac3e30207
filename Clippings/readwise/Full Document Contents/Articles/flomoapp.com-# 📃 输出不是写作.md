---
人员: 
  - "[[flomoapp.com]]"
tags:
  - articles
日期: None
时间: None
链接: https://help.flomoapp.com/thinking/output.html
附件:
---
## Document Note

## Summary

## Full Document
许多刚开始用 flomo 的共建者们，很快就能感受到记录无压力的欣喜。但是随之而来的会有一些常见的困惑：

* 现在这么零零碎碎记下去，将来怎么输出啊？
* 输出（写东西）好辛苦啊，有什么更简单的办法？
* 我该如何和 xx 软件对接来输出啊？

其实有许多词汇，背后都是一个复杂的概念，我们并非全然的理解，仅是有一个模糊的大众共识而已 —— 比如「输出」这个词。

可能看到这个词对应的就会想到：写文章，写公众号，录播客，拍视频，日更，月更，写书 —— 继而又会想到：我文笔不好怎么办，写的东西没人看怎么办，没时间写怎么办，写错了被人嘲笑怎么办，害怕镜头怎么办 —— 最终就会变成了：既然输出好复杂好困难，现在记录这些东西似乎也没什么意义，我去看看有什么更方便的工具吧……

这并非是指责大家做错了什么，而是我们对「输出」这个词的理解可能不够周全 —— 谁说每个人都必须成为文采飞扬的作者呢？难道输出只是自媒体人的特权么？

所以这篇文章就想和大家分享下关于输出的一些思考，不全面，但希望能给你提供一种截然不同的观点。

#### [#](https://help.flomoapp.com/thinking/output.html#输出是为了更好地决策) **输出是为了更好地决策**

阅读这篇文章的，大多数应该都是知识工作者了。知识工作者本质应该关注于产出质量而非数量，就像我们关心老师教会了学生什么，而不是教了多少人。

知识工作者的「产出」，往往就是一个更好地「决策」。西蒙赫伯特在《人类活动中有限的理性》中提到，**人们是如何做决策的：**

* 决策不是生活中广泛问题的综合选择，而是某些具体的事件。决策看似同等重要，但默认会假设和其他事项无关（比如买汽车和晚上吃饭是两件事）
* 当你决策时，无论多么重要，对未来的具体详情都不是了然于胸的，无法遇见所有可能性。
* 决策付出的努力，大多数耗费在了解事实上（外在的事实和内在的主观偏好）

那么写 MEMO 和决策有什么关系呢？举个简单的例子：

随着年龄渐长（对了，我是楠叔，请别把我当做小姐姐……），我还没有配置任何个人保险，这对于家庭来说有一定的风险。但和大多数人一样，我一直提不起来兴趣研究 —— 毕竟这个行业的口碑并不好，而且爸妈经常被忽悠，天然的就对这个事情有一些偏见，所以就一直没有「决策」。

后来「有知有行」学习投资第一课时，又一次看到了关于「保险」相关的知识，当时已经有了在 flomo 做随笔记的习惯，简单的摘抄如下：

![](https://resource.flomoapp.com/101/image-59.png!webp)
但这个记录并没有引起我的什么「决策」，只是在脑子里种下了一个小种子 —— **保险不是怕死的人才买（可笑吧，我居然之前是这么想的）**，而是为了整个家庭的安全，而且作为家里的主要收入来源，保护好我自己便是照顾好家人。

过了些日子，和老朋友在江边散步，意外的聊起来了这个事儿，他比较爱研究保险和投资之类的东西，滔滔不绝的给我科普了一个多小时，当时记录的 MEMO 如下：

![](https://resource.flomoapp.com/101/image-60.png!webp)
在这次谈话中，他算是把基础的保险常识给我讲清楚了 —— 尤其是对于医疗险和重疾险的作用，以及一个错误的观念纠正：**保险额度不是越高越好，而是根据自己的收入量力而为**（注意：这里不构成任何投资建议，请自己判断）。

以上只是两个例子，还有一些零散的积累，让我慢慢地对这件事有了一个 **「决策」产生** ：年龄变大加上开始创业，必须在今年内配置上一些保险降低家庭财务风险，哪怕不全不完备，但也总比无保险强 —— 毕竟没人知道明天和意外哪个先来。

上述过程中，我没有「输出」任何文章、播客、视频，**但是却由于一点点的积累，让我做出了一个「决策」**，即配置人身保险。对于我们来说，这就是一种输出，而如果没有日常的积累和记录，恐怕永远抽不出时间来学习相关知识，也不会意识到问题的必要性。

**所以，作为知识工作者，你的工作成果就是决策。但问题在于，你不能强迫自己思考的更快，只能提高决策质量，而不能加速。所以我们能看到：**

* 决策质量的前提，便是有充分的信息；
* 充分的信息背后，是来自于日常的不断积累；
* 不断地积累，源自于无压力的记录

试想上述内容不是 MEMO，而是保存了几篇上万字的文章，或者几堂付费课程，恐怕也很难吸收；而和朋友的闲谈，如果没有及时的记录，恐怕也早就忘记。

**这便是日常用 flomo 记录的意义，不在于写出漂亮的文字，而是做出更好地决策。**

除了上述说的「输出 = 做出更好地决策」之外，当我们确实要「写东西」时，也会有一些误区，比如常见的问题是：我记录的这些东西将来用不到怎么办；我已经写了几十条 memo 了，还不知道怎么输出，我还应该记录么？

之前看到冰哥（XDash）翻译的《[Walking as a Productivity System | 步行作为一个生产力系统  (opens new window)](https://mp.weixin.qq.com/s?__biz=MzA4NDk5OTgzMg==&mid=2650591453&idx=1&sn=936bae5caf69984e9aea45139fd3bc01&scene=21#wechat_redirect)》，其中有一段话很打动我：

> 如果你想写一本80000字的小说，而你只写了80000字，这也许是可以接受的。但是如果你写了30万或者40万字，然后把它减少到8万字，几乎可以肯定这会是一本更好的小说。
> 
> 

我们在 flomo 中记录的东西，势必有许多的内容是「用不到」，或者「孤零零」的。这并非有什么问题，就像我们的人生一样，总要做一些「无用」的事情，才能找到自己的意义，毕竟「**功不唐捐**」。

从另一个角度来说，也许并不是我们不知道怎么输出，而是我们积累的并不够，看到的问题不够全面，无法形成自己的观点。倘若当一个领域积累了超过 1000 张 MEMO 的时候，许多问题就百忧冰解了 —— 毕竟在[之前的周刊中  (opens new window)](https://mp.weixin.qq.com/s?__biz=MzI0MDA3MjQ2Mg==&mid=2247484068&idx=1&sn=ad68b0d3b0c0e01b10ce03ab48fffa21&chksm=e92120c5de56a9d31046e2709301a235a55ce7a3cfaad67b4619022e496ad5a55572813b8f39&token=270560288&lang=zh_CN&scene=21#wechat_redirect)也提到过，所谓专家也不过是只有五万个知识块（ 假定一条 MEMO 是一个知识块 ）

**一个事实：任何漂亮的输出工具，都不能帮你解决输入过少的问题。而多数时候，难以输出的问题往往是后者。**

#### [#](https://help.flomoapp.com/thinking/output.html#一些输出的例子) **一些输出的例子**

会有很多共建者提问：我记录了很多东西，没有找到「输出」的模式。其实大家大可不必把输出当做篇篇 10w+ 的文章，而是将近期的 flomo 内容按照主题汇总，然后在任意平台/工具上开始这样简单的输出形式（或者说是拼装更贴切），坚持半年以后，你会惊讶地发现：原来我的输出能力这么强。

下面是一些共建者利用 flomo 积累并输出的案例供大家参考，相信有更多 flomo 的共建者们已经开始了，如果你坚持了三期以上，也欢迎给我们投稿 —— 没必要在文中提到 flomo，只要是利用 flomo 帮你输出的，就好。（加微信 Plidezus）

**放下对未来的恐惧和焦虑，把当下的所思所想记下来，才是最关键的。**
