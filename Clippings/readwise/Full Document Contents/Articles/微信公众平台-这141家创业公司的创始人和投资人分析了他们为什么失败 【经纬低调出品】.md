---
人员: 
  - "[[微信公众平台]]"
tags:
  - articles
日期: 2016-03-15
时间: None
链接: https://mp.weixin.qq.com/s?__biz=MzA3ODk5OTEzOA==&mid=638964824&idx=1&sn=cb2624e250f8e37b9bca50fb90a6a2ab&mpshare=1&scene=1&srcid=0405Fiuk2HI7WdFnpE3hN0UT#rd
附件: http://mmbiz.qpic.cn/mmbiz/xtkZm6onx7XZxrhqjoSXrxldyh68wPicUKCsMHSyVgC8vdcbJGkCiaUicicVwgDkYCR9XOPY7ocpUozkh4pnno6KwQ/0?wx_fmt=jpeg)
---
## Document Note

## Summary

关于失败，很少有人愿意公开谈起。

## Full Document
今天的文章内容有点沉重——我们打算跟大家聊聊失败。关于成功的方法论有着趋同性，多半与“天时地利人和”有关。而关于失败，却很少有人愿意公开谈起——也许因为野兽总是不想将伤口暴露在外。

探讨失败的意义，可能远远大于成功。因为面对挫折，即使自认为最无畏的人也会有这样的时刻：“你已经近乎绝望，失去了所有的创造力。”无论现实多么惨烈，反思和总结仍有其必要性，因为无论如何我们总要往前走。

以下，我们通过关键词的形式对国外创业公司失败的理由进行了一些总结（见下图）：

![图片](https://imgproxy.readwise.io/?url=http%3A//mmbiz.qpic.cn/mmbiz/xtkZm6onx7XXlianZRVViaZk6vChd1lUOMiboHzMnAKQ9dDN4XK1sdaeY41WbKT8O3Gn2TCslfOFQCJKOGBQnPWcw/640%3Fwx_fmt%3Dpng%26amp%3Bwxfrom%3D5%26amp%3Bwx_lazy%3D1%26amp%3Bwx_co%3D1&hash=fad64ea02563de9a531dd3b36544a927)
结合个案，这些关键词与时间周期的特殊性存在重合，比如在2015年，因为融资问题猝死的公司大量增加。而刨除时间周期的特殊性，商业模式、产品、用户仍是最重要的因素。同时一个公司是否能按既定计划达成阶段性目标也是其能否存活下来的重要原因。

也有一些失败理由是“个性问题”，这也是创业最残酷的地方——有时候一些看起来很小的问题就足以压垮整个团队。还有一些失败的理由让主页君内心崩溃，比如：“（我们失败的原因是）团队里没有一个人是我们这个app的用户——我们都不用它，我们都不喜欢它。我们只喜欢这个创意，这真是个悲伤的故事。”——喂，说好的只投身于自己热爱领域的初心呢……

无论如何，“给前行者以希望，给后来者以经验”，是我们分享以下这篇文章的初衷。正如阳光与暗影总是相互伴生一样，有时候总是经历过绝望，才能发现耀眼的希望之光。Enjoy：

爱迪生曾经这样评价他的失败经历：“我知道了五万种不成功的方法，因此我离成功近了五万步。”

根据CBinsight的研究，2014年到2016年被列为失败的创业公司，往往发生在融资20个月，融资大约130万美元之后。以下我们按失败的属性维度，来看看这些公司的创始人与投资人如何看待其失败。或许也能带领大家少走一些弯路。。

（注：本文分析意见由创业失败的公司创始人与投资人编写，其中亦有部分来自于竞争者及早期员工；本文原为按时间顺序梳理公司情况，但经纬把文章调整为按共性来划分。）

结合这一百多家公司的失败原因，其共性主要聚焦在以下几点：资金（包含融资与收入）、商业模式、产品、用户获取、运营、团队，其他还包含一些个性化的因素。以下我们按出现频率高低来展现创始人们的反思：

 **资金** 

回过头来看这些失败的公司，现金流管理非常重要。一定要计划好每一笔钱，不要“想当然”，也不要等资金即将用尽才启动融资。

**1.Deliveree King**

“我们已经布局了15个城市，但是仅仅是这样我们很难继续维持下去。而且我们没有得到投资，这项业务需要资金来扩大范围，但我们没有资金，我们很难突围。”

**2.**Top 10****

“酒店行业的规模、覆盖面和预算都十分巨大，因此是一个非常具有挑战性的领域。Top 10这个公司已经做得很不错了，在这种艰难的领域内做出了创新。但最终，由于竞争过于激烈，我们没有资金来扩大规模，只好决定关闭公司。”

**3.**Prismatic****

“四年前，我们想打造一个个人化的新闻阅读器，以此改变人们获取内容的方式。对于你们很多人来说，我们的确做到了。但我们也明白了，内容分发是多么艰难的一项事业，我们没能很好地发展，因此失去了持续的资助。”

**4.**Pixable****

我们已经达成了我们之前想要达成的目标，尽管我们有了940万活跃用户，5800万月视频播放量，我们还是很难再筹集资金继续发展。

**5.**QBotix****

我们这个支离破碎的团队里的每个人都知道，我们没剩多少时间，我们将要面对一系列麻烦。但值得赞扬的是，他们每个人都仍旧十分专注，十分努力，工作直至最后一刻。我要感谢他们对公司和彼此的奉献。很遗憾，虽然我们取得了一些成就，但我们没有时间和资金来完成这项工作了。

**6.**Zirtual****

到底是哪里出了问题呢？简而言之：烧钱。烧钱是个复杂的问题，但硅谷里的公司都不怎么担心这个问题，因为在经济发展良好的时候，他们很容易得到投资。

**7**.Circa****

我们本来打算使CircaNews通过实施一个我们深思熟虑后的战略来盈利，但我们没能在资源用尽之前得到重要投资。

**8.**Kato****

经济萧条席卷全球，我们没能幸免。我们的产品没能得到大公司的青睐。

**9.UDesign**

我们低估了这个项目的复杂性，又高估了我们的能力，以为我们能够在经费不足的情况下完成它。

**10.**Patterbuzz****

一切都发展得很好，但我们始终有一个问题：我们的资金总是不够。这就是为什么我们失败了，因为我们没钱了。

**11.Nebula**

我们失望地发现，这个市场要再等几年才能成熟，然而作为创业公司，我们没能力等到那时候。

**12.****Wardrobe Wake-Up**

我们没能在发展的关键时刻得到投资，因此我们没有资源来满足我们的需求。

**13.****Melodic**

深思熟虑之后，我们决定要关闭这家数字资产交易公司。简而言之，我们没能发展我们的产品，无法说服投资人继续支持我们今后要在发展和维护上的花销。但是，我们正在研发新产品，我们将把我们的资源专注于其上。

**14.**Kinly****

我们没有解决资金问题。

**15.****College Inside View**

我高估了我拿到投资的可能性，低估了人们对于新产品的不信任和不情愿，还低估了设计的重要性。

**16.**99dresses****

我知道这是我们拿到钱最好的机会：曾经给我们投资的几位天使投资人也很有兴趣再次投资，只要我们能找到一家VC来主导这轮投资。但我们现在没什么钱了，我们也没什么时间来找别人了。

**17.****Backchat**

我们没能快速地适应瞬息万变的市场，带来的结果很快就在使用率上显示出来了。我们的产品是收费的，一开始一切都貌似顺利进行，直到我们用完了所有资金。

**18.****Wantful**

我们未能高速发展，而这正是下一阶段的投资人所要求的，即使他们对我们的工作始终充满热情，最终还是要回到数据上来。

接下来的度假季本应该是我们发展的关键时期，但上周我们失去了一个战略合作伙伴的投资，我们没时间寻找替代的资本，也没时间去寻求其他的机会。

**19.**D******isruptiveMedia**

我们需要大量的资金，才能启动全球范围内的生产和物流工作。我们很难和大型跨国公司达成协议，而且从技术上来说，参与他们的生产线也几乎是不可能的，因为他们根本就没有API。很难说这个项目到底能不能启动，因为我们没有钱了，只好半途而废。潜在投资人对一个正在衰退的市场也没有兴趣。我们所得到的资金也不够用。

**20.**T******urntable.fm**

我没有从那些失败了的音乐创业公司身上学到教训。音乐公司需要巨大的投资，这一产业真是太难运作了。我们把四分之一的资金都花在了律师、版税和服务之上，这限制了我们的发展。我们不得不关闭公司，因为我们没法在全球启动。

**21.****Skyrocket**

有一天，世界改变了。不再有投资进入我们的公司。除了在销售上的落后问题，我们还出现了新的产品问题。那时我就开始怀疑自己了，我的热情以及长久以来的信心都被磨灭了。

**22.Serendip**

每天处理上百万条信息，列出相关的歌单，再通过网页或者是手机app（没有安卓版，我知道…）推送给我们的用户，这其中的成本太高了，作为一个小小的创业公司，我们无法承受。

**23.****Lookery**

我们败给了Facebook。几年来我一直在懊悔，让一家创业公司依赖手机用户是一个多么糟糕的主意啊。事后想想，从这方面来说，我们的产品和Facebook没什么差别。要想成功，需要动用一切资源，其中之一——显而易见就是外部投资，但我们居然选择在没有投资的情况下开始工作了。我们应该找点钱来逃脱Facebook的掌控。

**24.****Blurtt**

我感觉自己要爆炸了，我是Blurtt最无畏的领导人，但问题是你已经近乎绝望，失去了所有的创造力。我开始感觉非常疲惫。

如果你没钱进行多次尝试，不要创业。第一次创业就成功的几率大概和中乐透差不多。

**25.****Admazely**

我的presentation做的挺好的，但是之后的问答环节就很糟糕了。投资人看起来抱着怀疑的态度，我走出房门的时候精疲力竭。那天我的联络人没有给我打电话，我就知道大事不好了。

**26**.******Springpad**

我们没有更多的资金，也没法升级成能够自给自足的公司。

**27.**Findory****

我学习到便宜是好事，但太便宜就不是了。如果在我们需要用钱的方面太过节约，以此避免经费使用过快，那对我们的公司一点好处也没有。

我重新学习到了团队的重要性，团队可以取长补短。虽然学习新知识很有趣，但把所有事情都揽在自己身上，会浪费太多的时间在缺乏经验而导致的无谓的错误上。

我还认识到了一个好顾问的重要性，特别是天使投资人和律师。一个创业公司需要有人能够提供专业而可信的知识，还有必要的人脉。你需要这些人来帮助你。

**28.Stipple**

我们开始有些收入了，但是发展得不够快。我们还没能盈利。与很多公司一样，我们到了A轮的关键时刻，却没能得到更多投资。我们甚至都没法从市场得到现金流。

**29.****Parceld**

没有人喜欢侵略性太强的人，但回头看看，“侵略性”这种说法用来描述“坚持不懈的”投资请求还挺合适的。我父亲曾告诉我，特别是作为女性，永远不要不敢说出自己想要的东西或者是提醒别人他们未兑现的诺言。现在人们都很忙，很健忘，日程很紧，我发三份邮件可能都会石沉大海，但发个四封五封（当然不是每天发）可能就能被看到了。不过，我再也不会知道这个办法好不好用了。

**30.**Saaspire****

现金流是最重要的。如果你想要开发一个产品，而你的收入是来源于别的地方的，那么你得先让这些收入稳定下来，才能专注于开发产品。

**31.Gowalla**

很不幸，一旦你的主要绩效指标与投资者眼中的价值紧密相连时，当第二名就很痛苦了。你的天花板已然到位，你未来融资或销售的能力也已经有了一个极限了。

想要存活下来就得提高绩效，我们尝试了一切方法来促进发展，有些还挺有用的。

**32.****Shergle**

你的创意是否只能在升级公司之后才能获利？如果是这样的话，去旧金山或者硅谷融资吧。在英国和欧洲投资市场上没有那么多人会将资金投入没有保障的研发部门的。如果你想在英国创业，那就找一些从第一天开始就能赚钱的点子吧。你也可以照样用免费增值模式来吸引用户。Shnergle在成为大公司之前是不可能赚钱的。失败！

**33.**T******igerbow**

不要从不懂创业公司投资的人那里筹钱。我们是从朋友和家人那里筹集了一小笔钱的。在很多时候我们都是互相支持的，但也有一些例外。我们从这些投资者身上并没有得到什么增值，而这些人，对投资创业公司以及公司运营的风险和挑战毫无认识的人会把你逼疯的。

**34.****Quirky（创意产品社会化电商）**

你控制着整个电商的运作，控制着开发、生产、营销和销售，甚至能得到90%的利润，但你的成本依然很高——特别是和那些并不参与这些环节的公司相比。“Kickstarter能日进斗金的原因就在于，他们什么都不做，只是提供一个网站作为平台。”

**35.****LinkManagement System**

销售是最重要的是，这是很多创业公司都忘记了的事实。我们也忘了。所以我们失败的原因有六点：

① 我们什么也没卖出去

② 我们什么也没卖出去

③ 我们什么也没卖出去

④ 市场还未完全开放

⑤ 我们太过专注于技术

⑥ 我们的商业模式是错误的

**36.**RewardMe****

除非万事俱备，否则不要随便升级公司。金钱才是王道，而且你得尽量发展，直到你发现你的产品能符合市场需求。

![图片](https://imgproxy.readwise.io/?url=http%3A//mmbiz.qpic.cn/mmbiz/xtkZm6onx7XXlianZRVViaZk6vChd1lUOMtn6bcSvosY9NLDVvqqntibLHmR9PG6CCMH25FiaOj0hyTUY7UXcls44A/0%3Fwx_fmt%3Djpeg&hash=f4afa670d7d1f261108033d05903a717)
 **商业模式** 

以下这些公司要么是没有找到合适的盈利模式；要么是在没有规模化前太早启动收费，而忽略了它的生意本质需要依靠一定规模的用户数。商业模式是很现实的问题，在考虑赚钱之前，你需要非常了解产品面对的用户，了解他们的需求，才能设计出让他们消费的盈利模式。这种模式要能持续才能成为你公司的商业模式，否则一时的减员削减开支也是没用的。

**1.**Campus****

我们试着改变公司的商业模式，寻求新的方法，但我们还是没能让Campus成为一家能够盈利的公司。

**2.**Poliana****

一个悲伤的故事：你很难用一个本应该免费的东西赚钱。

**3.****Bitshuva**

你做了多少工作不重要，重要的是你的服务值多少钱。

**4.**T******reehouse Logic**

创业公司不解决市场问题就会失败。我们有个很大的问题没有解决，我们本可以找到一个解决方案的。我们有优秀的科技，强大的关于购买行为的数据，我们的团队名声在外，有许多专家和顾问，但我们没有的是一个能够解决问题的科技或商业模型。

**5.**Rivet& Sway****

如果管理一个企业能被浓缩到只需要专注一件事的话，我就能改变结果。马后炮是再容易不过的了，我可以做很多事来力挽狂澜。让我来列个单子：

* 占领各地区的市场：西雅图，纽约，西海岸
* 尽快采取新的模式
* 等到完全必要时再扩大规模（我们把工作外包得太快了）
* 注重公关
* 多睡觉

**6.**Tutorspree****

虽然我们有了不少成果，但我们还是没能做出一套可扩展的商业模式。Tutorspree没有扩张，因为我们只依靠单一渠道发展，而这一渠道突然发生了根本性的改变。

**7.**Manilla****

在过去的三年里，Manilla得到了多项大奖、也广受用户赞赏，但我们却没办法扩展公司来进一步盈利。

**8.**Samba Mobile****

Samba关闭的主要原因就是数据成本过高，而且还在不断上涨，使我们现行的提供移动宽带交流的模式无法运营下去。

**9.**inBloom****

一项重要的创新项目只是因为公众对于数据滥用的担忧而被喊停，这是令人感到羞耻的。

我们努力工作，热情地支持着合作伙伴的工作，但我们意识到这一领域还太新，要得到公众的接受需要大量的时间和资源，没有人能承受得了。

**10.**Zumbox****

我们公司的所有人都致力于“数字化信件”这一概念，并且相信未来这将会成为你们收发信件的方式。但是，这一想法运作需要的时间和资金都远超过市场能够投资的量。

**11.**Delight****

消费是在为信息而不是为数据买单。消费者愿意为信息多付一些钱，对数据则不是那么感兴趣。你的服务将使你的客户在他们的股东面前看起来充满智慧。

追踪那些不活跃的用户，特别是你的服务并没有给用户设立一个中间值的时候。我们的系统本应该更加智能，能够收集不同用户的信息。

**12**.Readmill****

许多关于电子书的问题都还未解决，我们在试图创造一个阅读平台的道路上失败了。很不幸，在苹果的平台上以较高的价格出售书本是不可能的。我们也考虑过书本的订购模式，但发现它也不可行。最后，虽然我们所有的用户都付费了，但这对于继续我们的发展仍是杯水车薪。

**13**.Cusoy****

我并不想要一家创业公司，而是想要一个真真正正的能创造收益的公司。Cusoy并不能满足我的这个目标，除非它有一支全职团队，通过一两年的融资和至少三五年的奋斗来回答这个“如果／何时”的问题，也就是Cusoy到底能不能盈利的问题。

**14.**Flowtab****

我们雇用了丹佛当地的运营经理，并且马上就在当地最污的一家脱衣舞俱乐部以及其他两家酒吧开张了。当时我们每单得到了1200美元的收入，这就是Flowtab得到的唯一一笔收入了。我们简化了我们的销售过程，但是还是很难在其他酒吧进行市场营销，毕竟我们人不在那儿。

**15.**M******ass-customized Jeans**

在收回我们（父母们）最初的投资之前，我们是不会关门的。这就意味着我们的会从早上9点运营至下午5点，然后在晚上赚钱。这种模式刚启动的前几个月运行得挺顺利的，但是完全没有可持续发展的能力。

**16.**Travelllll.com****

如果你的赚钱策略是打广告，那么你得面向大量的对象进行营销。从一大堆人手里赚一点小钱和从一小部分人手里赚大钱都是有可能的。但从一小部分人手里赚一点小钱就不怎么正常了。如果你不是卖东西的，那你可得注意了。我们没注意。

**17.**RealTimeWorlds****

Dave Jones的APB没有任何商业模式，却获得了成功。他说：“如果你围绕着一个商业模式运作，你就注定失败。”——一派胡言。

 **产品** 

产品开发有两种，一种是我懂用户，我满足用户需求；另一种是老子牛掰，老子引领用户需求。无论哪种，你一定要记住，好的产品首先要让你和你的团队感到兴奋。如果你们都觉得很一般，那么市场也不一定会买单。不要把市场当傻子。

**1.ratemyspeech**

我最后才知道我们一开始的创意就是有缺陷的，大部分人（95%以上）都根本不关心自己的presentation做得怎么样。

**2.Kolos**

我们做了许多正确的事，但没有用，因为我们忽略了一个创业公司最重要的，最应该关注的问题：一个正确的产品。

**3.Allmyapps**

我们花了些时间才意识到，我们在启动上就有大问题…

**4.Seismic Video**

这是在七年前，那时还没有iPhone和安卓，所以它是超前于时代的。当时我们得用AdobeFlash在浏览器上播放视频，体验糟糕的要命，我想吐槽都无从下口。移动互联网已经变得更好也更重要了。

**5.Calxeda**

我们发展的太快了，用户赶不上我们。用户还没准备好接受我们的技术。我们在运行系统还未完全成型的时候就已经向前冲了。

**6.GameLayers**

我认为我们的PMOG游戏缺少那种能激发玩家热情的核心力量。“在网页上留下一条有趣的注释”的概念又太过抽象，大部分玩家都没法搞懂我们在说什么。回头看看，我认为我们该清除一些不必要的装饰，收起我们的骄傲，做出一些操作简单又容易带来欢乐的游戏。

**7.Unifyo**

我们起初致力于建立一个高度自动化的用户体验系统，专注于为终端用户和中小型企业提供方案，帮助他们的成长，但我们无法与大企业产生共情。

**8.Plancast**

社交网络本质上是为了在家人朋友们之间发布消息的系统，而用户在一特定系统上分享内容的次数，对于这一系统未来将会创造的价值来说是至关重要的。不像其他社交媒体是分享状态、感想或是照片的，我们的平台主要是分享计划，而计划只会偶尔被分享。许多人只是不会参加那么多的活动，许多人也不会那么确定自己会参加哪些活动。因此，用户不会养成一个每天或每周上传计划地习惯。

**9.Formspring**

创业者们：创造你们自己的产品，而不是别人的。最成功的产品应该是和产品本身以及用户的期待相吻合的。当你每况愈下时，你很难无视那些用了其他的策略和战略而飞速发展的创业公司。无视他们吧，这是唯一一个你能做自己应该做的事而不是追随他人的办法。

**10.Flud**

“我们的确没有对我们的首款产品做足够的测试，”Ghoshal说。这个创业团队在没有内测的情况下就发布了他们的产品，没有花时间做QA、情景测试、任务测试等等。当1.0版发布时，各种bug在他们的脑子里嘶吼，完美的用户体验姗姗来迟。

**11.On-Q-ity**

有了对的技术和错的市场，仍然是一次错误，这也证实了关于创新的一个陈词滥调。CTCs在未来将会大热，这点我们是能够保证的，但我们没有足够的资本等到市场赶上技术的脚步。CTCs会在5到10年的时间里发展成为治疗癌症的常规步骤，这对于我们这些参与了On-Q-ity的人来说，显然是喜忧参半的。

**12.CondomKey Chains**

毫无疑问，我有了一个大发明。就像爱迪生和灯泡，比尔盖茨和电脑操作系统，我引发一次改变社会的革命，同时也会给我带来财富和名誉。我差点成为美国第一个销售安全套钥匙链的人了。

**13.Wesabe**

在一个糟糕的数据聚集方法和Wesabe让你做的大量工作之间，在Mint上获得更好的体验就容易得多了，而且这种体验还来的很快。我之前提到的所有东西，不依赖单一资源提供商、保护用户隐私、帮助用户在财务方面做出积极改变等等，都非常棒，都是我们该追求的东西。但如果你的产品太难用的话，这些东西就都没有意义了。因为大部分的人并不那么在乎长期效益，他们只在乎眼前可见的未来。

**14.Imercive**

我们坚持一个错误的战略太久了。我认为这是因为——我们不愿意承认这个主意没有我们原先想的那么好，或者是我们没法让它实施起来。如果我们能对自己诚实一点，我们就能尽快转向，而且也会有足够的资本来实施一个新的战略。我觉得我作为CEO作出的最大的错误就是没能尽早从错误里走出来。

![图片](https://imgproxy.readwise.io/?url=http%3A//mmbiz.qpic.cn/mmbiz/xtkZm6onx7XXlianZRVViaZk6vChd1lUOMraZq58MN8AeJRBf0zhho5ZqL5WUmX7EGYrrnkMcGEAR7wITGuqQkNQ/0%3Fwx_fmt%3Djpeg&hash=f0cb24a999efdfa86a2304b7e0de525e)
 **用户** 

唯有尊重用户，你才能超越用户。

**1.Rdio（数字音乐服务提供商）**

Rdio的问题在于太早考虑持续发展的问题了。创业公司的一个典型错误就在于过分担心自己能否创造利润，但其实他们还没进入到高速发展的时候。这也是此类商业模式的一个问题，因为内容许可协议的关系，企业可运作的余地很有限。无论我们做什么，很大一部分的收入都要归唱片公司。你必须得用大量的用户来弥补它，这也是为什么你能看到Spotify要遍及全世界。

**2.**Selltag（二手交易平台）****

这家公司最大的问题在于用户参与与用户保留。“在交易平台的创业中，创业者都会面临‘先有鸡还是先有蛋’的问题。我们试着通过营销来保留买卖双方用户，但这还不够。可能会有很多人想在我们的平台上卖东西，但要找到买主就复杂得多了。”

**3.****Shopa**

消费者们对自己的购物需求守口如瓶，这超出了我们的想象。尽管我们吸引到了一百万用户，但他们对和好友买到同一条连衣裙的担心超过了对折扣的渴望。

**4.****Why Own It**

并不是所有人都会想要把这个app推荐给朋友或家人，因此，我们陷入了“鸡与鸡蛋”的问题之中。

**5.****Talentpad**

我们没能在这个大市场下创造出相应规模的企业。我们的团队始终热切希望我们能够在大范围的用户中造成巨大的影响，但我们没能想出达成这一目标的方法。

**6.****VoterTide**

我们没有花足够的时间来与顾客交流，并且还在滔滔不绝地讲我们自认为的优点，当我们意识到问题的时候已经太迟了。人们很容易骗自己说自己的产品超棒，但你得关注你的客户，并且按照他们的需求来做出调整。

**7.**P******umodo**

团队里没有一个人是我们这个app的用户——我们都不用它，我们都不喜欢它。我们只喜欢这个创意，这真是个悲伤的故事。

**8.****Findlt**

创业和改变世界都不是容易的事。在这一过程中，我们了解到大多数的Findlt用户其实并不需要这个软件，这让我们在今后要继续努力来解决这个问题。

**9.****Teamometer**

不要做太多的计算，类似：30美元乘以1000个客户再乘以24个月，哇，赚翻了！

哎，傻孩子，你根本都不懂让1000个客户每个月付费长达两年有多难。我的建议是：先找到第一个客户，再找10个，再找更多。

除非你先找到10个客户，否则你什么也证明不了，只能在哪儿加减乘除。

**10.**G******roupSpaces**

我们总是犯同样的错误，就是太早扩张了。我们渴望达成一个巨大的数字来为今后的融资做准备，同时也被我们在学生市场上得到的广泛认同所激励，我们花了大把资源用于发展我们的付费营销渠道和分发渠道，而我们本可以将这笔资源用于用户获取的。我们失败了，因为我们在新的市场上没有足够的PMF，只好把精力从产品转向寻求与市场的契合度上。这耗费了我们大量的时间。

**11.**Zillionears.com****

人们实际上并不真的喜欢我们产品的全部，我们的用户也不觉得我们的服务真的有那么好。实际上，有些参与了销售环节的人甚至都不喜欢我们的“灵活定价”系统。他们想要帮助那些艺术家，因此省下几块钱并不能让他们有多高兴。他们本来可以在别的地方免费得到这些音乐的。

**12.**HelloParking****

我们从未提出清晰的假设，从未发展实验，也很少与我们的终端用户进行有意义的谈话。虽然我们在这一产业中有几位不错的顾问，但我们本应该见见所有我们能联系上的人的。更糟糕的是，我们几乎都没走出我们的办公室。

**13.**S******onar**

我们的教训：

如果你正在创建一个社交网络，那么发展是唯一需要注意的事。用户的参与是挺重要的，但除非你你的发展跨越了一个门槛，否则你是不会得到下轮融资的。

**14.****RiotVine**

这不是好点子坏点子的问题，这是让人们谈论起来的问题。让你的产品的一些方面能够引起人们的谈论，并且让它变得独一无二。

![图片](https://imgproxy.readwise.io/?url=http%3A//mmbiz.qpic.cn/mmbiz/xtkZm6onx7XXlianZRVViaZk6vChd1lUOMiadmegH0SQ1nBLsCzKqJkhCh8qxibkbFs56OOsJV3Egc5QCC6lO1iaDbw/0%3Fwx_fmt%3Djpeg&hash=7857331b1e3808ff9c8693a8294a97ba)
 **团队** 

一定记住要互补，不要只具备一种能力。

**1.Kior**

各方对谁应该承担责任的问题出现了分歧，但他们一致认为这家公司在人员配备方面做得很差：公司实验室里有很多博士出身的研究人员，却没什么技术和运营人员。这种不平衡导致了它的失败。

**2.Standout Jobs**

我给StandoutJobs太早筹到了太多的钱（大约是180万美元）。我们那时候还没有合法筹集这么多钱的许可。其中一个原因可能是创始人团队还没找出决策者。这是个错误，如果创始团队不能自己推出产品，或者不能在一小部分自由职业者的帮助下推出产品，那他们就不应该创业。我们本可以找一个联合创始人，但我们没有。

**3.**UntitledPartners****

招聘很难，而且我们自己没什么经验，只能依赖投资者来帮助我们作出决定。我们在招聘全职员工、兼职员工和合同顾问时作出的正确决定和错误的一样多。最大的建议就是：一旦数据显示你的一个职员的不能胜任工作，不要迟疑，马上换人。

**4.Decide.com**

决定你做事的方式，然后雇佣那些和你的做事方式一样的人。不同的观点的确很有价值，但在创业公司中，雇佣那些同意你的观点的人所得到的好处也就是快速的发展会比得到不同的观点来的更重要。

**5.**Vitoto****

超出专业领域的产品：团队里从未有人打造出一个成功的产品过。我们都有在大公司的工作经验，从销售到商务都有。但我们没有关于视频用户的经验，我们到了一个完全不是我们强项的领域里。

**6.**N******ouncer**

一个月前，在我的天使轮投资进行到一半时，我打算复习一下我已达成的成就，想象哪儿还需要改进，让我的公司成为一个可盈利的公司。我也正在一个朋友的帮助下寻求风险投资。最后，我问我自己什么是我成功最重要的因素，答案是合作伙伴和开发者。我花了一年的时间来找寻他们，但始终没有合适的人选。我意识到，钱不是根本问题。

**7.****BricaBox**

当你的合伙人离开公司时，你就要开始面对一场狂风暴雨了。不要把事态恶化，好好想想如何处理手中的大笔股份。对于那些拿着血汗股份的创始人来说，最好的办法就是等待一段长长的授权期。

**8.**B******oompa.com**

我和Ethan做了一个“僵尸队伍”测试来看看是不是有人想要在一个工作强度极大的项目中工作，不管是创业公司或是别的什么。这个测试是这样的：如果僵尸突然在地球上大量出现，你会相信队伍里的伙伴们会做你的后盾吗？如果他们被咬了，会诚实地告诉你吗？更重要的是，如果你将队伍里唯一的枪交给他们，他们会是更好的射手吗？

如果任何一题的答案是“不”的话，你就知道他们更适合在格子间里工作了，而不适合和你一起创业。

**9.**Devver****

对于一个远程办公的团队，最重要的问题就是管理上的缺失。在一个州里管理工资单、解雇人员、保险等琐事就已经很麻烦了，横跨三个州来管理这些东西简直是噩梦，即便我们已经请了一个服务公司来帮忙。显然，当你的创业公司发展壮大时，就可以找别的公司来帮忙处理一些麻烦事了，但对于一个小团队来说，这是最麻烦最让人分心的事务了。

**10.**CryptineNetworks****

无论你和你的朋友关系如何，多么信任彼此，你的意图多好，金钱都会横亘在两人之间，更何况每个人都会高估自己的贡献。另外，创始人对于公司的情感是很复杂的。因此，要想从创始人手中拿回股份，这一谈判既不合理，也不简单。然而，股份行权计划（vestingschedules）降低了谈判的困难，并且能使公司回购股权。我让自己傻傻地跌进了“这事不会发生在我身上”的陷阱，然而没有哪家创业公司能够一次就成功的。这些小问题导致了团队的分裂。如果你觉得创业公司不用考虑股权问题，那你就太天真了。

**11.**Lumos****

我们从未用过市面上的家庭自动化产品，我们也不是物联网领域的专家。当你在某个领域是新手时，你就没法意识到你决定中的不足，就像那个著名的达克效应所说的那样。

 **运营** 

细节无小事，但这些公司却忽略了。

**1.****Wishberg**

2013年4月，我们得到第一笔投资时，我们为自己设下了很高的目标。作为一家创业公司，数据是最好的伙伴。在年末重新审视这些目标时，我们意识到我们根本没有按目标行事。我们这个团队总是想要追求更大的梦想，却没有关注更小的挑战。

**2.******Secret****

很不幸，Secret并没有达成我刚刚创办公司时的愿景，因此我认为关闭它对我、对投资人和对我们的团队来说都是正确的选择。

**3.****Dijiwan**

一个好的产品创意和一支强大的技术团队并不能保证一个公司的成功。我们不能忽略公司的发展进程以及运营问题。

**4.****PostRocket**

我们刚刚创办PostRocket的时候，我们不仅想要帮助企业在Facebook上成功营销，还想创造出色的产品和服务来支持它。我们一直没能达到我们为自己定下的目标。

**5.**Canvas Networks****

一个貌似已经成功了的产品会失败，这真是让人吃惊，但这种事每天都在发生。虽然我们的产品已经很符合市场需求了，但我们没能好好运作它。要发展一个企业是很困难的，而发展一个只有一个app，而且正处于创业路上的企业是特别困难的。

**6.****HowDO**

我们的目标是将我们的热情转化成一个可持续发展的平台。我们缺乏发展所需的资源，因此没能完成这种转化。关闭平台是一个艰难的决定，但我们已经考虑过其他所有方法了。

**7.****Backfence**

超本地化是非常难的，不要自己哄自己，你不只是打开大门然后就冲击某个临界点，这就是我们从我们的信仰一跃中学到的。建立一个社区需要大量的工作。仔细看看那些超本地化网站，看看那儿有多少帖子，特别是那些由社区用户发表的帖子。所有超本地化社区网站的运营者都会告诉你，你得花上很多年才能真正发展出一个活跃的线上社区。而要盈利就得等上更多年。不幸的事，种种原因让Backfence没法持续发展下去了。

**8.**S******ednaWireless**

经济原因只是其一，另一个问题是我们没能实施我们的计划。外部原因如印度的硬件生态和内部原因比如团队的专业度不足都是我们失败的原因。如果有钱的话，我们或许还能再坚持一段时间。

 **法律风险** 

在界限不明的行业，尤其要重视潜在风险。这里面需要一套组合拳，你如何用一系列公开的行为向政策制定者施展你的善意，这是门艺术。做得好你将制定行业准则；做得不好，有可能像以下的公司一样万劫不复。

**1.Flytenow**

“2015年12月18日，美国哥伦比亚特区上诉法院驳回了我们要求撤销联邦航空局对Flytenow以及其他用于共享飞行经历网站的禁令。Flytenow成立两年多了，是为了让航空爱好者们能够分享飞行的快乐，见识真正的飞行员，或者是组团去开飞机。不幸的是，我们别无选择，只能关闭Flytenow。不过，我们还会像飞行员一样战斗，直到重回天空。”

**2.****Carrier IQ**

“人们早已对大公司或其他组织为了自身利益如何处理客户隐私抱有怀疑，而我们的软件在用户不知情的情况下追踪用户，对公众造成了很大的冲击。因此，不出所料，在事件披露之后，出现了大量宣布某某公司使用（或未使用）这一软件收集信息的声明，法院受理了大量关于侵犯隐私的案件，立法机关正起草法案来加紧这方面的控制。许多诉讼都貌似已经结案了。AT&T并没有收购整个公司，我们也明白它将不会对CIQ的任何案子负责了。”

**3.****Homejoy**

公司CEO认为，失败的决定性因素在于他们所面临的诉讼，这四宗诉讼是关于他们的员工是否应按照正式工和合同工来分类。虽然这一分类最终并未真正实行，但还是严重影响了融资。“这种麻烦事很多，虽然都只是些小事，但却激起了大浪。”。

**4.**V******atler**

我们2014年夏天在旧金山创建Vatler——提供贴身管家服务。我们认为这项服务的需求很大。然而我们接到了警察局的电话，说我们的许可证未经通过，还警告我们的业务中有很大部分是非法的。两周之内，我们失去了大量的业务和30%的收益源，根本没有继续发展的希望了。我们试着让一些饭店支付账单，但没有帮助。我们的经营模式不再有效，9月7日，也是在旧金山，我们宣布倒闭。

**5.****Grooveshark**

我们与唱片公司协商决定，即刻停止我们的运作，清除服务器上的所有数据，并且交出我们的网站、手机应用的所有权以及知识产权，包括专利和版权。

**6.**O******rdr.in**

我们活在美国梦之中，直到一个“专利怪“——一家唯一的业务是起诉合法企业以求取高昂协调金的公司——用一场官司把我们打倒了。

**7**.This Is My Jam****

当这些平台走向成熟和巩固，用户从网站上流向app时，更加复杂的许可条例和地理上的限制告诉我们“对不起，这个歌曲不能在这儿播放”，这种现象变得越来越普遍而非偶尔的例外了。

 **技术** 

记住，有时候技术过剩，让产品变得复杂亦是灾难。

**1.Laguna Pharmaceuticals**

涵盖了大约600名患者第三期实验才刚刚进行两个月，研究人员就发现了副作用，这使公司无法像之前那样大范围推广这一药品。“我们其实十分惊讶，因为二期研究时一切都十分顺利。”公司CEO说。他拒绝透露更多关于副作用的事，只是将其描述为“安全信号”。“正常的做法应该是继续研究，”他说，“但它不会有商业价值了。因此比起尝试突破，我们还是决定停止。”

**2.****Nirvanix**

云服务很棒，外包很棒，但不可靠的服务一点儿也不棒。没有人比你更在乎你的数据，这是底线。没什么能代替尽职调查和深思熟虑，要避免依赖。

**3.****MyFavorites**

在开发手机app的同时开发网页app是个很傻的选择，特别是当我们还是没有开发出收藏选项，也没有让任何人测试过我们的app的时候。我们简直是在以一个很滑稽的方式烧钱。当我们为了SXSW发布的最后期限手忙脚乱时，我手下有7个人在一起做这个产品。我们打算专注做一个iPhoneapp，但是这把我和我们的程序员难住了，因为我们没法用这个app，我们的手机都不是苹果。

**4.****Meetro**

我们本可以试着修补Meetro的，但我们的团队已经要向前走了。就凭我们毫无发展的现状来筹集资金基本上时不可能的。另外，我知道为了让我们的队伍更加团结，我们得转移一些注意力。但包括我在内，大家都觉得被打败了。我们知道要解决软件中的问题得重写代码，但没有人想要做这件事。

**5.eCrowds**

当产品变得越来越复杂时，它运作起来就不那么快了。在我看来，所有的网页app都应该有很快的运行速度，所以人们不能接受我们的app的速度，特别是它开始在公共网站上运行时。我们花费了大量的时间来加速它的运行，但我们失败了。这教导我，我们必须一开始就应该将一个基准工具纳入到我们的发展周期里，因为我们的产品属性要求这一点。

**规模效应**

在很多同质化竞争的行业，规模效应是非常重要的，也是最大壁垒之一。能做好，其实不容易。

**1**.Brawker****

我们的发展速度没有达到预期，我们的服务也未能覆盖到我们所希望的范围。

**2.**Pr******otoExchange**

很不幸，我们努力想要改变制造业的面貌，但我们没有达成最初的目标。因此，我们选择关闭公司，重新评估我们在制造业中的地位。

**3.****Balanced**

我们发展的速度不够快，没能在支付领域成为一个大型、创新、独立的公司，只好决定不再继续发展。

 **营销** 

营销技巧是用户和产品之间最有效的一座桥，只有真正懂产品和用户心理的人才能做好它。这其实是决定你商业成败的“最后一公里”。

**1.****Dinnr**

这可能是Dinnr的命运，我们从未得到过什么好机会。无论我们做什么来救活它，在网站宣传或采取别的营销策略，都像是在无谓地激励一个奄奄一息的人。

**2.****Everpix**

创始人一直都知道他们犯错了，他们花了太多时间在研发产品上，因此在发展和传播方面的时间就少了。他们的第一份融资演讲稿极为普通。他们太迟开始营销了。

他们的失败在于他们将自己置于那些科技巨头，比如苹果和谷歌的对立面，而这些公司都提供着完善并且免费的Everpix替代品。虽然他们的产品用起来并不难，但的确还需要一些学习，而且用户还得相信一个名不见经传的创业公司，把自己的人生回忆交给他保管。这太难了，而Everpix也未曾做出过能使它变得容易的努力。

“有一个成功的产品，并不是能使一家公司成功的唯一因素。”

**3.****Overto**

对于网络公司来说，决定生死的是用户数量。刚开始的时候用户数量是系统性增长的，接着我们就撞到了天花板。这时候就该做些营销工作了。但很不幸，我们团队里没人擅长这块工作，更糟糕的是我们没有那么多时间来填补这段空白。

 **其他** 

**1.Wattage**

我觉得我们的失败可以总结为四个字：能力不足。

**2.GigaOm**

商业，就像人生，不可能和电影一样，不可能每个人都得到童话般的结局。

**3.Digital Royalty**

我们经历了一些改变，一些在我们的掌控之中，一些脱离了掌控。为了不丢失我们的核心价值观，不污染我们的文化主心骨，我们决定暂停业务。

**4.37Coins**

作出关闭公司的决定是很困难的。虽然我们心怀远方，但我们已无法输出高质量的产品来与市场匹配了。

**5.ComboCats Studio**

可能只有我这样吧，我低估了UI的工作量，即便我看到了模型。次次如此。这个东西很难一开始就正确。

**6.Twitpic**

我们没有资源来从Twitter这样的大公司手中保护我们的品牌，尽管我们始终相信这个品牌是属于我们的。因此，我们还是决定关闭Twitpic。

**7.GreenGarStudios**

如果你还记得我的融资演讲里曾这么说：“我们已经赚了一百万了，让我们来聊个十亿的吧。”也许有天这番豪言壮语仍能成真，因为我还在为此不断努力。但是，这个梦想可能不是和我的GreenGar一起实现了。

**8.Outbox**

巨大的复杂系统似乎不可战胜，但其实不是这样的，它们都是我和你这样的人创造的。

政府或大公司最主要的资源就是时间，然而这是创业公司最缺少的东西。

你可能觉得政府组织很糟糕，你错了，他们超级糟糕。

如果没法找到你需要的硬件，那就自己造一个，这并不难。

在客户身上可能会花掉很多时间和精力，但非常值得。

人生苦短，只要追求那些你最想要的东西就好。

**9.Argyle**

我们是一个在大地方建立的小公司，但那儿不是硅谷。这是把双刃剑，因为在硅谷，你得花两倍的钱来雇佣员工和支付房租，但在硅谷之外，人们对于融资的态度又完全不同。

**10.Exec**

许多O2O企业都在问我创建Exec的经验：

单位经济比纯软件业务要重要得多；

送货员人员流动的速度是非常快的；

需求是很尖锐的；

动员顾客是很难的；

我们是不能自己去跑业务的。

**11.MochiMedia**

没人希望看到这种结局，也的确有人想要以更高的价格收购Mochi，但我们还是解散了。他们并不打算做出什么合理的决定，他们肯定不像我们一样为这个公司考虑。我们试着坚持下去，但是最后还是没能改变他们的决定。

**12.Salorix**

很可惜，所有的好事都会走到尽头，这个公司也一样。迅速衰败也是件好事，总比慢慢死去好。

**13.Intellibank**

专注与简化常常比幻想未来的未来的未来更难。正是因为如此，许多创业公司无法集中精力。我们需要时间来修剪一个想法，这是很重要的，就像马克吐温曾说过的那样：“如果我有更多的时间，我会写一封更短的信。”

**14.ArsDigita**

① 花了2000万美元才拿回与我在当CEO时的相同数量的收入。

② 婉拒了微软的希望我们称为第一个有.NET产品的创业软件公司。

③ 在第四版完成之前就废弃了第三版产品。

**15.EventVue**

我们的错误：

• 没有专注于学习

• 没有注重对EventVue的营销

• 一开始招募人手的时候做出了太多妥协，没有足够关注雇员的能力和竞争力

**16.IonLab**

我与7个人谈了谈。其中三个对于我的决定表示同情和同意，另外四个力劝我坚持下去。是什么让我做出了决定呢？前三个都曾经创业过，而后四个没有。后四个人不能真正了解我们的情况，即使他们也是为了我好。

**17.Kiko**

创造一个能够激发你生产力的环境。在家办公可能挺方便，但通常在一个专门的空间办公会更有效率，同时还能让你保持家庭和工作的平衡。

**18.Monitor110**

七宗罪：

① 缺少一个决策者

② 没把技术部门和产品部门分开

③ 太早做太多公关

④ 有太多钱

⑤ 没有贴近消费者

⑥ 没有及时按市场做出调整

⑦ 在公司内部和董事会内部对战略的分歧太大

**19.NewsTilt**

• 我和Nathan之间有重大的沟通问题

• 我们本身对新闻和新闻产业没有足够的热情

• 开发新产品需要改变，但我们做不出改变

• 以上三点消磨了我们创业的热情

**20.****SMSnoodle**

我出生起就不爱听别人的建议。如果你是个技术控，那你不听建议也会有很多别的机会，你不会满足于任何程度的发展。别听你的心说什么，听听看你的脑子说什么。如果你是一个网站创业者，你只有6个月来开发第一版网站，更简单一点的那种只能用2到3个月。你可以之后再进行调试，但要尽快开始工作。

**21.**Zen99（财务软件公司）****

我们在用户获取上出现了问题，而且我们最强的领域又有竞争者。我认为，获取用户最好的方法就是建立起一个1099报税单用户网络：Intuit公司（Zen99的竞争者）数十亿的报税之中，许多都是来自于这些用户的。不幸的是，Intuit开发了一款相同的软件来与我们竞争。当你的最佳用户获取策略帮助了一家正与你竞争的公司时，你不会好过的。

**22.****InqMobile**

Inq在过去的几年里取得了一些成果，我们的技术被采纳到另一家公司一个更好的应用之中了。因此我们要做出这个艰难的决定，就是关闭Inq。

**23.****Diffle**

当你的公司倒闭的时候，你是没法理性思考的，你当时的想法和几个月后的可能完全不同，你对你今后的发展前景也会有不同的看法。

因此，你得在一无所有之前关闭公司，这样你就能考虑接下来该做什么，而不是手足无措。

希望这些公司的经验对你们有用，一如既往，希望读公号的你们，好运。

*本文原发于CBinsights.com，经纬创投编译*

 **也许你还想看：**

[时机重要过一切](http://mp.weixin.qq.com/s?__biz=MzA3ODk5OTEzOA==&mid=265050209&idx=1&sn=3dcd5b257c67dd892879e52234a64296&scene=21#wechat_redirect)

[一个创业公司的失败、迷茫与重生](http://mp.weixin.qq.com/s?__biz=MzA3ODk5OTEzOA==&mid=286036330&idx=1&sn=5bc68cb5b6a16fcbd91aeabb94c71ff7&scene=21#wechat_redirect)

[90%创业公司失败是因为没做好这三件事](http://mp.weixin.qq.com/s?__biz=MzA3ODk5OTEzOA==&mid=213693402&idx=1&sn=fa39fe3790b8b7a9c424571201b28c19&scene=21#wechat_redirect)

[“在创业上成功，在家庭里失败”](http://mp.weixin.qq.com/s?__biz=MzA3ODk5OTEzOA==&mid=323603418&idx=1&sn=99022369d561a7b2bcb63f45c5d002d6&scene=21#wechat_redirect)

[你还在烦恼招聘吗？这十个方法你都用了？](http://mp.weixin.qq.com/s?__biz=MzA3ODk5OTEzOA==&mid=516534884&idx=1&sn=464f7c1e451f4fb04d2d4fceed562e41&scene=21#wechat_redirect)

[除了红包，你还能用什么抓住别人的心？](http://mp.weixin.qq.com/s?__biz=MzA3ODk5OTEzOA==&mid=535007436&idx=1&sn=313f25dc0947de99b74f52abddde85cd&scene=21#wechat_redirect)

[即使是红包，也不能随随便便地发](http://mp.weixin.qq.com/s?__biz=MzA3ODk5OTEzOA==&mid=572197896&idx=1&sn=ca86f28a40073e149f86e233bc8275ff&scene=21#wechat_redirect)
