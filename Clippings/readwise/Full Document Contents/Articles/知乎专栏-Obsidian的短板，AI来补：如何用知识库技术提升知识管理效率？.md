---
人员: 
  - "[[知乎专栏]]"
tags:
  - articles
日期: 2025-03-07
时间: None
链接: https://zhuanlan.zhihu.com/p/28802518672
附件: https://static.zhihu.com/heifetz/assets/apple-touch-icon-152.a53ae37b.png)
---
## Document Note

## Summary

为什么你的Obsidian需要知识库在所有笔记软件中，Obsidian（以下简称Ob）是我用得最顺手、最持久的一个。虽然它并不完美，但它的高自由度和灵活性让不会让我有任何压力感。我试过其他一些笔记工具，比如Logseq和An…

## Full Document
在所有笔记软件中，Obsidian（以下简称Ob）是我用得最顺手、最持久的一个。虽然它并不完美，但它的高自由度和灵活性让不会让我有任何压力感。我试过其他一些笔记工具，比如[Logseq](https://zhida.zhihu.com/search?content_id=254783024&content_type=Article&match_order=1&q=Logseq&zd_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ6aGlkYV9zZXJ2ZXIiLCJleHAiOjE3NDQ5MDMwNTIsInEiOiJMb2dzZXEiLCJ6aGlkYV9zb3VyY2UiOiJlbnRpdHkiLCJjb250ZW50X2lkIjoyNTQ3ODMwMjQsImNvbnRlbnRfdHlwZSI6IkFydGljbGUiLCJtYXRjaF9vcmRlciI6MSwiemRfdG9rZW4iOm51bGx9.Nl7Eeh8wkQpXk4_xcLlD65uCLI8KrthbsjSVLTmJdIk&zhida_source=entity)和[Anytype](https://zhida.zhihu.com/search?content_id=254783024&content_type=Article&match_order=1&q=Anytype&zd_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ6aGlkYV9zZXJ2ZXIiLCJleHAiOjE3NDQ5MDMwNTIsInEiOiJBbnl0eXBlIiwiemhpZGFfc291cmNlIjoiZW50aXR5IiwiY29udGVudF9pZCI6MjU0NzgzMDI0LCJjb250ZW50X3R5cGUiOiJBcnRpY2xlIiwibWF0Y2hfb3JkZXIiOjEsInpkX3Rva2VuIjpudWxsfQ.0nbPTD0Pdj4wLRirSSrW2o-mwcmTQLsXWGbkdNCxb7M&zhida_source=entity)，但最终都因为不适应而放弃了。Ob的“散漫”风格让我可以自由地组织笔记，既不需要强迫自己每天写日志，也不非得设计一套复杂的属性体系。笔记可以是精美的建筑，也可以是“豆腐渣工程”，这种自由度让我感到非常舒心。

然而，Ob也有一个明显的短板——信息管理的颗粒度太粗。如果你同时使用过Ob和Logseq，你会发现Logseq在细粒度信息管理方面非常出色，但它的排版功能较弱，缺乏“笔记感”。最终，我选择了Ob的高自由度，但始终对细粒度信息管理的缺失感到遗憾。

##### 知识库如何弥补Obsidian的不足？

之前一直很关注知识库技术的应用，但限于openAI跟claude价格和付款问题，也只能望洋兴叹。我一直执着于知识库的原因之一，就是希望解决Ob信息颗粒度不够细的问题。幸运的是，随着[DeepSeek](https://zhida.zhihu.com/search?content_id=254783024&content_type=Article&match_order=1&q=DeepSeek&zd_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ6aGlkYV9zZXJ2ZXIiLCJleHAiOjE3NDQ5MDMwNTIsInEiOiJEZWVwU2VlayIsInpoaWRhX3NvdXJjZSI6ImVudGl0eSIsImNvbnRlbnRfaWQiOjI1NDc4MzAyNCwiY29udGVudF90eXBlIjoiQXJ0aWNsZSIsIm1hdGNoX29yZGVyIjoxLCJ6ZF90b2tlbiI6bnVsbH0.D0jRZFcbXxy-Untm2kx-IlgX2pNwJ-PJd7GRVjcsutA&zhida_source=entity)的出现，这个空缺似乎可以被弥补了。

举个例子，我平时读书时会摘录书中的句子，但由于疏于打标签和写批注，等到需要引用时，往往找不到想要的段落。类似的情况也发生在我的笔记中，很多素材和想法写完后就被遗忘在角落里。

在Logseq中，这些细小的段落可以通过query查询建立规则和联系，比如生成所有打了`#概念`标签的段落，方便统一回顾。Ob的Dataview插件也能实现类似功能，但限制较多，尤其是它不支持多行字段，使用起来并不顺手。

知识库技术提供了一种更优的解决方案。它可以通过更直觉的问答方式调用对应的知识片段，而不需要手动编写复杂的query查询。虽然目前知识库的召回效果还不尽如人意，但通过一些“笨”方法，我仍然在细粒度笔记信息管理上取得了一些进展。

##### 知识库工具的选择与优化

不同知识库工具在检索模式、模型支持和参数数量上存在较大差异。如果只是用于常规笔记管理，Obsidian Copilot插件基本够用。但如果需要扩展到书籍、论文或网络搜索等场景，就需要更强大的工具，比如Cherry Studio、AnythingLLM或Perplexity。我个人推荐Cherry Studio，它的界面更友好，易于上手。如果对召回精度和内容复杂度有更高要求，可以考虑Dify或[Coze](https://zhida.zhihu.com/search?content_id=254783024&content_type=Article&match_order=1&q=Coze&zd_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ6aGlkYV9zZXJ2ZXIiLCJleHAiOjE3NDQ5MDMwNTIsInEiOiJDb3plIiwiemhpZGFfc291cmNlIjoiZW50aXR5IiwiY29udGVudF9pZCI6MjU0NzgzMDI0LCJjb250ZW50X3R5cGUiOiJBcnRpY2xlIiwibWF0Y2hfb3JkZXIiOjEsInpkX3Rva2VuIjpudWxsfQ.nCNlx64hnvo4dj_V_lbF9IWLlhOYQ1QvaTnoDUys_zQ&zhida_source=entity)这类智能体工具。

以下是我用过的几种工具的功能对比：

* **Copilot插件**：仅支持设置请求文档分段的数量，功能较为基础，适合日常笔记管理。
* **Cherry Studio**：支持设置分段数量、分段大小、重叠大小和匹配权重，兼容多种文件格式，如文本、PDF、Word等。
* **Dify**：功能最强大，支持自定义分块模式、父子分段和多种检索模式，召回效果更佳。支持上下文记忆对话工作流。

三个工具的知识库参数设置界面如下：

![](https://pic1.zhimg.com/v2-2692fcfa747c06390d99d4f062927d56_1440w.jpg)Copilot知识库参数
![](https://pic2.zhimg.com/v2-e9b0ea9fd1c9431d0effb2d36792cb2d_1440w.jpg)Cherry Studio知识库参数
![](https://picx.zhimg.com/v2-ba2ce9cc993b275639c2dd6450fc48ef_1440w.jpg)Dify知识库参数(1/2)
![](https://pic4.zhimg.com/v2-ad4343adb3c1733146387332e7f65f31_1440w.jpg)Dify知识库参数(2/2)
不过，Dify的学习成本较高，且大模型接口不够稳定，处理大数据时容易出现报错，运行速度也较慢。因此，对于个人用户来说，暂时不建议入坑，可以等待工具更加成熟稳定后再尝试。

##### 优化数据质量的“笨”方法

在使用Dify工作流屡屡受挫后（主要是模型api频繁time out），我决定从数据质量入手，通过AI自动化段落标签和格式调整来提升召回效果。

召回效果不佳的主要原因往往是关键词关联度不足。通过“十字标签法”结合关键词的方法，可以显著提高知识库的召回效果，下图是Copilot的召回效果，Cherry Studio和Dify的效果应该会更好一些。

![](https://pica.zhimg.com/v2-8eeaf356d3a9c31abbb08706899daec6_1440w.jpg)Copilot插件基于关键词和内容类型的召回效果
##### 十字标签法怎么用

这个方法来自古典老师，我没看过实际的出处，朋友圈里看到这个分享，觉得很有意思，就赶紧记录下来了。这个方法主要是通过领域和内容类型两个维度来定位自己的知识素材的，尤其适合细颗粒度的知识碎片管理。我在十字标签法的基础上设计了一个书摘标签助手的prompt：

```
你是一名Boox电子书书摘助手及标签分类专家。会对导出的书摘进行进一步的格式优化，并为每个书摘生成适合的标签。章节位于时间及页码上方，如无则留空。用户会提供书名，作者信息。批注一般用【批注】标注，无则留空。

书摘模板要求：根据以下Jinja2模板输出书摘。

{% set min_length = [content|length, page|length, date_time|length]|min %}
{% for i in range(min_length) %}
----------------
书摘[i+1]：
{{ content[i] }}

章节： {{ chapter[i] }}
书名： 《{{ 用户提供 }}》
作者： {{ 用户提供 }}
标签： {{ tags[i] }} {{根据标签生成要求生成}}
批注： {{ remark[i] }}

{{ date_time[i] }} | 页码：{{ page[i] }}
-------------------
{% endfor %}

标签生成要求：
1. 如有批注，根据批注的理解打标签，如无批注，根据书摘内容打标签。
2. 标签格式： #领域/{领域} #类型/{内容类型}/book #keywords/{关键词1} #keywords/{关键词2}
3. 每个书摘至少包含1个领域标签，1个内容类型标签，以及2个关键词标签。其中：
    - 领域：指的是可以帮助用户持续探索、积累知识、或是计划要生长作品的一个话题范围。让用户有一个自己要探索的课题的牵引和注意力的聚焦，能够始终朝着自己感兴趣的方向积累。例如：写作、个人成长、心理学、个人知识管理、人力资源、哲学、健康、运动等。
    - 内容类型：指的是属于什么类型的信息，帮助用户有意识地积累不同类型的信息，有针对性地积累不同类型的内容素材，在未来写作、准备分享课程、演讲的时候，能够更便捷搜索到自己储备的内容。例如：故事、案例、方法、观点、原理、概念、思维模型、金句等。
```

这里使用书摘的范例是因为书摘的格式比较固定，但被切割后会丢失关键信息（书名、作者），属于知识库召回时一个比较明显的痛点。通过调整格式重新赋值，可以避免召回时丢失关键信息的问题。

当然，理论上上述流程更适合用智能体自动化工作流，前提是它稳定不报错……实测prompt稳定性远优于拆分任务的智能体工作流。咱这杀鸡任务，就不用牛刀了。

##### 结语

AI技术为Obsidian带来了新的可能性，尤其是在细粒度信息管理方面。虽然目前知识库技术还不够完美，但通过选择合适的工具和优化数据质量，我们仍然可以显著提升笔记管理的效率。如果你也对Ob的颗粒度问题感到困扰，不妨试试AI辅助的知识库工具，或许会有意想不到的收获。

・广东
