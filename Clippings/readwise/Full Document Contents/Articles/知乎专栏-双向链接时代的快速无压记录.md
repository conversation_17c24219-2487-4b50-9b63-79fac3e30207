---
人员: 
  - "[[知乎专栏]]"
tags:
  - articles
日期: 2021-10-29
时间: None
链接: https://zhuanlan.zhihu.com/p/427336729?utm_campaign=shareopn&utm_medium=social&utm_oi=26716063924224&utm_psn=1563898992722866177&utm_source=wechat_session
附件: https://pica.zhimg.com/v2-a02e0f2789062b72449b302f6b8153f3_720w.jpg?source=172ae18b)
---
## Document Note

## Summary

• 知乎的编辑器实在太烂，所以后面如果要更改内容也懒得在知乎改，建议直接戳原链接 • 字数两万，没有废话 • 本文使用比 _____ on vscode 更适合写作的思源笔记编写并发布引子 在这篇文章的开头，先来晒一点自…

## Full Document
> • 知乎的编辑器实在太烂，所以后面如果要更改内容也懒得在知乎改，建议直接戳[原链接](https://link.zhihu.com/?target=https%3A//www.yuque.com/deerain/gannbs/ffqk2e)  
> • 字数两万，没有废话  
> • 本文使用比 \_\_\_\_\_ on vscode [更适合写作](https://link.zhihu.com/?target=https%3A//www.yuque.com/go/doc/31315052)的思源笔记编写并发布

#### 引子

在这篇文章的开头，先来晒一点自己的远古黑历史：

![](https://pic1.zhimg.com/80/v2-2b1ff04bc0d285ded32f25dbd8942cb8_720w.jpg)
我错了，这罪我早就想谢了。

![](https://pic3.zhimg.com/80/v2-0a0b4b8e49b3210cfcf08bd0641b83f2_720w.jpg)
其实今天回想起来，双向链接笔记的跟风热潮并没有成功地给广大用户带来一种新的思考方式。因为如果以我今天的理解来评价的话，绝大多数 roam research 的模仿品只学了一点皮毛，学得好一点的模仿品只有寥寥几个，而且即便是学得好的也都有一些关键点没学到位。换句话说，如果做产品的人自己对 roam research 的双向链接都只理解了一点皮毛，传达给用户的当然也就是皮毛。

不用说，这种环境当然会在很多人心中催生「双向链接无用论」，但即使是觉得双向链接有用的人，大多也都还抱持着「互相链接」「网状」这种在今天的我看来根本没入门的理解。

当然这件事肯定不能怪广大用户，毕竟 roam research 有很长很长一段时期连登录进去都困难。我自己对于这种新产品当然是有很大的兴趣去深度尝试，但是天天盯着这个加载界面船锚发呆也不是个事，所以就转向一些早期模仿品去体验双向链接。

![](https://pic4.zhimg.com/v2-91ee39acbc01c33071ecb4353878f893_b.jpg)
但是在当时，对 roam research 模仿得比较好的几个产品一个比一个不稳定，天天出现各种难以忍受的魔幻 bug，于是做得稳定的产品就能吸引到更多用户，包括我。可偏偏做得稳定的产品没一个把双向链接学明白了的，再加上「比起 roam research 主要是差一个细粒度引用」这种言论占据了主流，所以我自己在早期也对双向链接有很大误解，经历了很多曲折才扭转过来。

这篇文章有两大贯穿全文的重点：

其一是通过双向链接笔记工具来实践真正高效的 daily notes 快速无压记录流程。仅通过这一个场景就能向大家证明，哪怕完全不提诸如「网状」「联系」之类的噱头，双向链接依然有很大的价值。不过这篇文章里对于双向链接的诸多使用技巧只会提一小部分，其余的在下一篇文章里单独写，双向链接无用论已经到了可以终结的时候了。

其二是「快速无压记录」这件事背后的深刻原理。快速和无压绝不仅仅是前期的事，它们**必须**要有后期功能的支撑，更需要使用者对时间管理有足够的理解，这些理念我之前从没看见有别人提到过，所以这篇文章里要一次性写满。

#### 简单高效的 daily notes 流程

#### 基本的 daily notes 流程

开始进入正题。我们先用极短的篇幅来快速过一遍快速无压记录的 daily notes 流程。

以 roam research 为例，在有一条新的笔记想要快速记录下来的时候，直接跳转到 daily notes 页面，在第一行写下它，同时给这条笔记加上一个代表这个笔记归属与哪个主题的锚文本，姑且称它为「传递型双链」，它的作用就是把内容传递给 `[[Egypt]]` 这个页面：

![](https://pic2.zhimg.com/v2-5794cdf71ffa6faad4727fbe529d4925_r.jpg)
记录结束。

从这个例子大家已经能看出，这个页面叫做 daily notes 是因为它真的是用来记录笔记的，但人们往往容易把这个页面误会成 diary。

这种记录方式本身是不是特别简单？这是当然的，一篇关于「快速无压记录」的入门教程怎么可能写一大堆具体操作步骤呢。

大家可能会看到 Conor 或者其它人的教程里有一些跟我不一样的操作，但是这些形式差异根本不是重点，只讲这点流于表面的东西也绝对不可能让人信服。所以我不会列举一大堆差异形式，这篇文章后面会用很长的篇幅来说明快速无压记录背后的深刻原理，以及双向链接是如何从具体功能上来支撑这套方法的。

#### 后期乌云：查看与整理

看完上一节，心里有无数疑虑是正常的。

比如说，按照传统笔记软件的思维，跟同一个主题相关的笔记会分散在多个文档里，根本不利于后期的整理和查看。但是在 roam research 中，只要点进 `[[Egypt]]` 这个页面，就可以直接在底部反向链接区域看到所有链接到这个页面的笔记。

通过反链，汇总查看 daily notes 的问题可以轻松解决。

![](https://pic1.zhimg.com/v2-8199425ee43fbeb1420c9a4f02023e68_r.jpg)
如果要整理所有关于这个主题的笔记又怎么办呢？也很简单，roam research 的反向链接是具备完整编辑功能的，既可以直接修改，也可以直接把反链里的内容直接拖动到正文里，然后靠大纲编辑器的整理能力来把这些内容整理成一篇完整的长笔记。如果完全不需要整理只需要查看，那反链和正文就是一回事。

![](https://pic2.zhimg.com/v2-839e09c77126069072e66178cc80e2f9_r.jpg)
如果想让某些笔记继续留在 daily notes 中，也可以不把它的本体拖动到正文，而是把它的块引用拖到正文里：

![](https://pic4.zhimg.com/v2-9bf406f00a459802cdd843046e393a63_r.jpg)
这个小例子也充分体现出了「块级引用」这个功能的存在必要性。在 daily notes 流程中，跟同一个主题相关的笔记虽然可以直接汇总查看，但存储上终究是分布在很多个不同的文档里，所以各种细粒度操作就成了非常自然的需求。

另外再补充一个小技巧。锚文本除了上面提到的传递型双链，还可能是出现在句子中用来表现关联性的，姑且称之为「关联型双链」：

![](https://pic3.zhimg.com/v2-ebe10bcc6f0b98d036c65f80a8dd4b4e_r.jpg)
在整理的时候，第二条记录可能仅仅是做为参考，并不需要真的把它的块引甚至本体拖动到正文中，但是随着反链的数量变得很多，从视觉上有可能并不好分辨这两者，还需要看一眼具体文字才能分辨。如果这种锚文本全都出现在 daily notes 以外的文档里，可以靠反链的筛选功能过滤掉它们。但是，就像上图那样，关联型双链同样会出现在 daily notes 里。

所以我在 roam research 里才会把传递型双链写成上面的形式。在整理的时候，只要一条内容来自 daily notes、并且形式上是 `[[Egypt]]` 这个锚文本独占一个节点并带上一些子节点，就可以不细看内容、直接把它拖进正文。

```
- [[主题]]
  - 关于这个主题的一些笔记
  - 这里可以写上很多个节点和子节点
```

我个人的习惯是把绝大多数反链内容都转移到正文中再开始认真整理，因为转移之后往往还要进行大量的二次编辑，块引用和块嵌入编辑起来都很不舒服（比如不方便退格合并），并且把传递型双链的本体移到正文之后，反链里剩下的就全是一些关联型双链，很利于查看。把这些笔记的本体留在 daily notes 中有的时候是有一些上下文语境的，但是我愿意为了手感和整理便利放弃它。但总之，每一条笔记在整理的时候都可以自由选择是否留在 daily notes 中，所以这个整理过程是非常灵活方便的。

另外，如果实在是有整理强迫症，想要定期整理一下这些反链，怎么去判断哪些笔记需要整理一下反链了呢？roam research 早就准备好了对应的功能，对所有页面按引用数排序就行了，假如某个页面有几十条反链，那肯定是包含了大量待整理的传递型双链。

好起来了，daily notes 的查看和整理问题都解决了。

这里只提到了一点最基本最直观的整理方式，毕竟**弄清楚这个问题最大的意义并不是让我们学会具体怎么整理，而在于让我们相信它是可以整理且方便整理的**，这样我们才能放心大胆在前期使用 daily notes 来进行快速无压的记录。

做到「快速」其实非常简单，但做到「无压」很难。**直到今天**，**所有**（所有）打着「速记」招牌的笔记类 app、也包括手机自带备忘录，它们都有一个共同的缺陷，那就是简单的前期记录方式缺少后期功能的支撑。如果我已经明白了这个 app 的特点就是记录一时爽、整理火葬场，我又怎么可能放心无压地在前期按照它给我预设的方式来做记录？今天，在双链方面学到位了的双链笔记距离彻底扯下这些速记 app 的遮羞布只差一个做得足够好的手机客户端。

#### 前期对比：双链 vs 传统，究竟谁更折腾？

之前在论坛上讨论这套快速无压记录方法的时候，就被问到一个问题，**那就是在大纲双链笔记的 daily notes 流程中，针对我当时在帖子里举的一个例子，新增一条关于「logseq 的配置技巧」这个主题的内容需要为这个节点加上一个名叫 [[logseq/配置]] 的锚文本，可是假如使用没有双向链接的传统笔记软件、甚至用 typora 这种纯粹的编辑器，也可以先搜出 logseq - 配置.md 这篇文档，然后把新增的内容写进去，既然大家都是差不多的逻辑，那大纲双链笔记上这套 daily notes 流程到底有什么优越性呢？**

我觉得这是一个直击本质的好问题，所以在这篇文章里，一定要给大家再次展示一下这两种记录方式的详细对比。当时我在论坛上是拿 logseq 举例的，在这里直接给大家重述一遍。

##### 虚假的折腾

假设有这样一个场景，今天我逛了一下 logseq 论坛，看到别人说新版本通过修改 `:ui/show-empty-bullets?` 变量可以显示或隐藏内容为空的 bullets，我觉得这个设置可能会有用，于是我想把这个配置方法在笔记软件里快速记录下来。

然后我打开 logseq 做记录，步骤如下：

**第一步**：跳转到 daily notes 页面

**第二步**：在 daily notes 的第一行直接写下

```
- [[logseq/配置]]
  - `:ui/show-empty-bullets?` 变量：显示或隐藏空节点
```

**第三步**：记录完毕，切出 logseq 去做别的事

##### 真正的折腾

还是同一个情境，要是我用 typora 来记录，步骤如下：

**第一步**：为了最快捷地打开 `logseq - 配置.md` 这个文件，当然是要好好利用各种搜索型效率软件了，于是我在 utools/Everything/Albert/Alfred 里搜索 `logseq 配置`，结果发现我并没有这个文件

> 这个时候第 1 个差别就已经体现出来了，在 logseq 里，我永远不用回忆 `logseq/配置` 这个页面是不是已经存在，直接在 daily notes 的第一行开写就行了

**第二步**：搜索未果，我开始考虑换个关键词搜索，也许是我记错了文件名呢？万一我以前创建的文件名其实是叫做 `logseq - config.md` 呢？换了几个关键词搜索，发现我以前似乎真的没做过这方面的记录

> 第 2 个差别出现了，在 logseq 里，我不需要考虑自己之前是不是已经用别的名字创建过 `logseq/config`，我只需要直接在第一行写下 `- [[logseq/配置]]` 就行了，别的什么都不用考虑  
> 第 3 个差别：就算我以前已经有了 `logseq/config` 这个 page，那也不需要管它，因为 `logseq/配置` 和 `logseq/config` 这两个页面一定会在 `logseq` 这个父页面下共同展示，我可以在将来合并它们（*这里其实是我为了不打断语言表达故意口胡了一下，实际上 logseq 没有把 roam research 的合并页面功能学过来，这个功能看似不重要，但其实它是 roam research 最核心的功能之一，roam edit 和葫芦笔记是学到了的*）

**第三步**：既然以前没有对应的 md 文件，那我就只能创建它了。创建一个新的 md 文件虽然不是什么高难操作，但是我在创建的时候就需要给它在文件系统上安排一个具体的位置，安排位置这个事情因人而异，但无论如何都是多了一层麻烦

> 第 4 个差别：在 logseq 里没有烦人的新建文件流程，就算我根本不对 md 文件分类，新建文件这个步骤还是烦人

**第四步**：终于新建好了 `logseq - 配置.md` 这个文件，但是我已经忘了刚才想记录的是什么

> 第 5 个差别：用 logseq 记录，到达路径很短，心智负担几乎为零；用 typora 记录，到达路径很长，心智负担巨大

**第五步**：我查看系统剪贴板，终于回想起了要记录的东西，开始记录

**第六步**：终于记录完毕了，我逛完论坛关闭了网页，然后又开始了读文献时间，读了一阵子之后，我有一条突发的阅读心得想快速记录下来，于是我唤出了 utools/Everything/Albert/Alfred，再次重走上面的曲折心路历程

> 第 6 个差别：如果是用 logseq 记录，我只需要再次在 daily notes 页面的第一行写下：

```
- [[文献/文献名]]
  - 这个思路真是妙鸭
- [[logseq/配置]]
  - `:ui/show-empty-bullets?` 变量：显示或隐藏空节点
```

对比完毕，这两种方式看似差不多，实际上是天壤之别。请各位摸着良心说，**究竟谁更折腾**？

在 roam research 出现之前我就经常说，追求简单没问题，不想折腾没问题，但是 A 和 B 两个选项究竟谁才是简单且不折腾的那个，还是需要用合适的方式来公平对比一番才能下结论。说一句会得罪很多人的大实话：直接用 notepad/typora/word/texmacs 这些单文件编辑器来做知识管理的折腾程度至少是双链笔记的十倍以上，而且效果也烂得伤心。

就算不说这些单文件编辑器，传统笔记软件也做不好快速无压记录。先不说更困难的「无压」，它们连「快速」都做不到，特别是在为某个主题追加内容的时候。想为某个文档追加一点内容，竟然需要先把它搜出来、跳转过去、滚动到底部、写完再跳转回来，现在这套操作怎么看怎么难受。笔记的核心首先是记，有了素材才能谈整理，如果长期积累一些不方便的小操作，对记录积极性是一种伤害。

无论是使用一套知识管理方法还是一个笔记软件，一定要回归人性。主打“复杂强大”和主打“返璞归真”都没有问题，但如果不符合人的天性就等于自虐。

有人可能会说，上面只对比了前期记录阶段，还不足以下结论说传统笔记比双链笔记更折腾。没错，上面确实只对比了前期记录阶段，但是继续看完这整篇文章你就会知道，**后期整理阶段的差距更大**。

#### daily notes 凭什么能做到快速无压？

#### ⭐ 分布在时间轴上的压力

「快速」还是很好理解的，而「无压」就比较有讲究了，但是没关系，只要一次性分析清楚背后的原理，将来实操就再无疑虑。

daily notes 流程之所以让人做到无压记录，最主要是因为它遵循了一条 pkm 领域比较流行的原则，那就是让内容自然生长，不要在记录的时候提前考虑怎么把笔记整理得井井有条。

我知道，在任何一个讨论 pkm 的场合，都会有无数的比卢曼、梅棹忠夫、钱钟书、李敖更厉害的人根本不认同上面这句话（顺便一提，纳博科夫不算）；而即便是认同这句话的人，对它的理解也处于一种「听着很有道理但是我不知道为什么有道理」的状态。

所以，我想用一个更直观的新模型来解释它：**记录和整理笔记的所有心智负担并非集中于一点，而是分散在整个时间轴上，而且这些心智负担的总量也不是恒定的，换种工具或换套方法，心智负担的总量就会大不一样**。

先从工具维度来对比说明吧。conor 的 daily notes 流程让 roam research 的使用者在记录的时候可以完全只管记录，不用管整理的事，因为它把整理的困难完全抛给了未来、以达到当下的完全无压，所以它的心智负担大约是：

反观传统文件夹式笔记，一般都需要在记录的同时考虑怎么整理，而且不成熟的整理方式在未来依然需要二次调整，所以它的心智负担大约是：

为什么第一列是「过去」呢？假如有人试图像制作二代卡片盒的卢曼一样，提前建立起一套特别完善且成熟的笔记体系，那么他的心智负担就主要存在于过去。

通过上面两个表格的对比，可以很直观地看到，roam research 中的 daily notes 流程**把整理的负担推给了未来、所以当下能做到零压力记录**。

这就是工具维度的对比，而时间维度就更妙了。

有些专题笔记虽然在 daily notes 里记了很多条，但是它平时不一定会被用到，有的笔记平时甚至连搜出来看一眼都没必要，只要真正需要认真整理某篇笔记的那天没有到来，用户就根本没必要为整理付出任何时间精力；如果那一天始终没有到来，那么未来整理这篇笔记的压力也变成了 0，也就是说，既然整理的命运只会降临在一部分专题笔记上，那么剩下的大部分专题笔记从头到尾的整理心智负担为：

![](https://pic4.zhimg.com/v2-be779bd53721a0e08fc8ac6fd655d047_b.jpg)
看到这三个零，很难不心动。

我们今天觉得有价值的内容在将来可能一次都用不到，我们今天觉得很难组织输出的内容对将来的自己可能是小菜一碟，我们今天自以为是的整理方式在将来的自己眼里可能就是一坨 shit，那我们凭什么要去跟未来能力更强视野更广的自己抢着干一些根本不紧急而且可做可不做的事情呢？**当下的我们需要做的，是那些只有现在能做，而且有意义的事情，「每天坚持积累」正是这样的事，而「整理得井井有条」并不是，这才是快速无压记录的真正核心**。双链笔记上的 daily notes 流程就是要强制性地让我们明白并实践这个重要道理（类似的还有纸质笔记本上的 bujo），它已经不仅仅是笔记方法了。

可是总有一些专题笔记必定会迎来需要整理的那一天，在前期记录时如何彻彻底底消除对那一天的顾虑？接下来我们就要进入深水区了。

#### 为心智负担兜底的一切

完全消除对后期整理的顾虑的方法其实不复杂。简单来说，像 roam research 这样的软件具备很多方便后期整理的功能、以及帮用户减轻压力的舆论氛围，**用户其实什么都不用做，只需要意识到这些东西的存在，前期就能彻底放心**。

大家应该也意识到了这样一件事，想做到真正的无压记录，笔记软件光有个双链或者一些前期的优化是远远不够的，还需要很多后期功能来为「今天思想尚不成熟且容易犯错的用户」擦屁股。**通过提高后期容错率让用户不怕试错大胆记录，这就是所谓的「兜底」，前期的无压记录一定要靠后期的整理能力来兜底**，我认为它在本质上是一种对普通人的人文关怀。

![](https://pic3.zhimg.com/v2-62cd362a519cd65d2806d41fec05f006_r.jpg)
接下来我们就具体来看看，这些兜底性质的功能都有哪些。

##### 为整理压力兜底的「编辑器」

daily notes 流程是把跟某个主题相关的内容都传递到某个 page 的反链里，然后在将来某一天把反链里的传递型双链都拖进正文慢慢整理，形成一篇成熟完善的内容。

既然要整理拖进正文的内容，编辑器的整理能力就不能太差，大纲编辑器刚好很合适，现在从 workflowy 到大纲双链笔记基本都具备把大纲内容转换成看板形式的功能，平铺查看并整理就更加方便了。文档型编辑器也可以做得方便整理，关键是愿不愿意去做，大纲型编辑器一样有做得很差的。不过目前除了思源笔记之外，所有的文档型双链笔记不仅反链展示形式做得不够好，而且根本没有打通反链和正文，在这里讨论它们各自的整理能力没什么意义。

现在有一个小问题，这里讨论的是「后期整理能力为前期记录的心智负担兜底」，要达成这种兜底效果，就需要用户非常相信编辑器有足够的整理能力，可是这种信任不是凭空产生的。我写一些测评的时候经常把话说得很绝对，比如「某某功能只有某某软件才有」「某某软件在这方面做得比任何同类产品都好」，那是因为我无遗漏地把所有同类产品都用过了，所以我敢下各种结论，包括每个软件的整理能力怎么样我心里也很清楚。可是，一个十年 evernote 老用户刚转到一个大纲双链笔记的时候，他对这个新工具并不了解，当然也就谈不上信任，前期快速记录的时候还是会有顾虑。如果靠他自己去深度使用，可能没等摸索明白他就先弃用了。

解决这个问题最有效的方式还是靠口碑和风评，比如 roam research 现在风头正劲，真正深度体验过的人全都交口称赞，所以新手哪怕压根没用明白也会比较相信它。可其它双链笔记没有 roam research 这么火爆，各种资料和教程也很少，所以我就厚着脸皮来个批量背书吧：

> 到目前为止，在所有能完整实践 daily notes 流程的双链笔记里，roam research、roam edit、葫芦笔记、logseq、思源笔记、remnote 的编辑器都有合格的后期整理能力，所以大家在 daily notes 中做前期记录的时候可以完全放心，后期一定可以顺利整理。

当然，上一段话只说了编辑器的整理能力，稳定性什么的我就没法保证了。接下来，让我们看看编辑器之外的整理功能。

##### 为命名压力兜底的「合并」

在前面也提到了一个场景，在 daily notes 中记录的时候，某个主题锚文本可能是 `- [[插件]]`，但是如果很久都没记录过关于这个主题的内容，在某一天突然要再次记录的时候可能想不起自己过去记录时使用的锚文本究竟是 `- [[插件]]` 还是 `- [[plugins]]`，并且在这个例子里，「插件」和「plugins」并没有任何一个共同字符，所以搜索过滤也是行不通的。

如果 roam research 做得不够到位，用户在这个时候可能就需要先试着搜索一下，看看自己以前用的究竟是哪个关键词，虽然搜一下也不是很麻烦，但实际上这是根本不需要的。为了达到真正的无压记录，用户只需要直接写下 `- [[plugins]]` ，不需要顾虑太多，就算以前记录时使用的锚文本是 `- [[插件]]` 也不用担心，因为 roam research 可以在将来直接通过重命名合并这两个页面。这个合并功能很重要，只要用户意识到了这个功能的存在，在记录的时候就少了很多顾虑，想到什么直接写就行了。

所以我觉得**「快速无压记录」这个问题不光要从前往后定义，还要从后往前定义**，想要真正忘记考虑「往哪放、加什么标签」之类的问题，就需要工具本身提供后期的调整能力。**只有使用者知道「反正这个软件可以让我在后期很方便地调整」，前期记录的时候才会真正无压力**。「合并」这种后期调整功能就是在为前期记录时的错误兜底，有了这种兜底，使用者才能放开手脚无压记录。

上面提到的页面合并功能并没有在用户在 daily notes 中进行记录的时候立即生效，甚至将来也有可能一次都不会真正使用，但只要它存在、只要使用者知道它存在、只要使用者相信它能发挥作用，记录的时候就可以随心所欲。这种功能的重要性类似于核弹，国家拥有核弹并不一定要真的发射到别的国家去，但只要持有核弹，就能在很大程度上保证国家安全。

![](https://pic2.zhimg.com/v2-56243a951daf66ea858af7c13d260aa9_r.jpg)
「合并」这个设计不仅体现在 roam research 的页面合并上，writeathon、flomo 和滴答清单也都具备合并标签的功能，这样用户在一开始记录某条内容的时候就可以随便写一个标签名，无需考虑已有的标签树长什么样。

##### 为检索压力兜底的「随机」

roam research 里是没有明面上的文档树的，这让很多人无法接受甚至直接弃用。如果所有文档不能以树状形式组织起来，就会让很多人担心某条笔记在将来找不到，当然这里的「找不到」不是指主动的全局搜索功能不好用，而是指忘记了自己曾经写过这么个文档。

虽然我很想说能忘记的东西多半不重要忘了就忘了呗，但我也清楚，这种观念不可能这么轻易放下，这个心态不解决就不能做到无压使用。

所以还是来说一点更现实的东西吧。很多小型的专题 wiki 站点都会做一个侧栏文档树或者一个 moc 页面，让访客直接鸟瞰**本站有哪些条目**，这也是在个人笔记系统上维护文档树的主要预期。但是只要文档数量很多，全局文档树和全局 moc 很难维护下去，如果文档数量多到了 Wikipedia（维基百科）那种程度，想在左边做个涵盖整站文档的全局文档树是不可能完成的任务，所以 Wikipedia 也只能在不同粒度上做一些包括 [Contents](https://link.zhihu.com/?target=https%3A//en.wikipedia.org/wiki/Wikipedia%3AContents) 在内的局部 moc；可是同样是做局部 moc，用 roam research 比 MediaWiki 方便了无数倍，而且 roam research 和 logseq 还有嵌套链接或 namespace page 来表示局部的树状关系，能直接选择 parent rem 的 remnote 就更不用说了，**它们并不是没有分类或不能分类，只是没有摆在明面上**，导致很多轻度用户没有探索到。如果一个人连那些老式 wiki 都能接受，完全没有理由不接受 roam research 的设计。

而且从我的长期经验来说，只要文档数量多到一定程度，维护分类的人真的会麻木，不维护分类的访客也懒得一层层点开目录树、都是直接全局搜索，最后全局分类体系形同虚设，就连局部 moc 也一定会疏于维护。所以我对文档树的理解就是一个树状的「收藏面板」，把常用的东西拿出来摆放一下就够了，没必要花精力维护全局的分类，logseq 的右侧栏和 remnote 的左侧栏也是这么做的。

但这还是没有解决那个问题：就算我说一大堆理由，但如果一个人**无论如何就是想看看**这里都有哪些东西，而且这个人也忘了用来全局搜索的关键词，该怎么办？特别是在个人笔记上，为了做到无压使用，这个问题一定不能逃避。

对于这个问题，Wikipedia 和 Reddit 给出了完美的答案：随机。

Wikipedia 左侧栏的[随机按钮](https://link.zhihu.com/?target=https%3A//en.wikipedia.org/wiki/Special%3ARandom)可以随机打开一个页面，Reddit 也可以进入[随机版块](https://link.zhihu.com/?target=https%3A//www.reddit.com/r/SonyHeadphones)，想看看这里有哪些东西吗，点随机按钮吧。

![](https://pic1.zhimg.com/v2-9feea1a017aee5f8600abce55f6bd9b0_r.jpg)
我一直觉得 Wikipedia 的这个随机按钮是一个特别妙的设计，它相当于是把一棵巨大的左侧栏文档树浓缩成了一个按钮。反正文档树在内容很多的时候维护不动，反正有了文档树也是瞎点瞎逛，还不如直接做成一个更高效的随机按钮。

最早把这个随机按钮加到笔记软件上的双链笔记是 obsidian，但实际使用之后发现体验没有想象中好，因为个人笔记里有一些东西就算随机出来了也没什么意思；每个双链笔记的查询语句可以做一些规则限定，但这个规则其实也很难设计。更重要的是，如果「随机打开一条笔记」这一行为重复了 10 次，只遇到 1 条是有随机价值的，这种体验是比较糟糕的，因为人在这个过程中会感受 9 次负反馈，这会让人没有动力再用这个功能。

最佳的随机方案是一次性随机出多条内容，然后把它们一览无余地呈现出来。这样一来，就算随机出的 10 条内容只有 1 条是有价值的，本次查询带来的依然是正反馈。这样就极大提高了容错率和效率。

做得比较好的双链笔记都可以用自己的查询语句或者插件来做出上述效果，有了这样的随机功能，就可以让所有沉寂在海底的笔记全部翻涌起来、从而缓解「害怕某条内容找不到」的心理，让使用者敢于在前期随便记录。

##### 为粒度压力兜底的「统一」

之前在跟人讨论的时候，别人提出了这样一个问题：

> 在有一条新的想法要快速记录的时候，究竟是为它单独新建一个文档，还是把它追加到某个已有文档的末尾呢？

daily notes 流程告诉你，不用想这些，把内容搭配上主题锚文本写在 daily notes 页面就好了。

那假如将来有一天，我使用上一节提到的随机技巧，随机出了多个页面，然后我发现这些页面里的 B 从内容上说是 A 的子话题，有没有办法做调整呢？即使是传统笔记软件，也不是不能靠剪贴板把整个 B 页面的内容转移到 A 页面，但是双链笔记上不能直接这么做，因为 B 的反链会因此消失。

roam research 可以直接把 `B` 改成 `[[[[A]]: B]]`、logseq 可以把 `B` 改成 `A/B` 来让 B 的链接出现在 A 页面的底部，这样就可以体现这两个主题的父子关系，可是如果我是想对外输出或是想大规模重写内容，当然更想把 B 的正文直接合并到 A 中，同时还要保证 B 的反链不消失，这个场景它俩就应付不了了，因为原来对 B 的引用是页面引用，而合并之后需要的是对名叫 B 的节点的块引用。并且，一旦 B 带上所有子节点成为了 A 中的一个块，daily notes 中也无法再靠 `[[` 搜到它，得用 `((` 才行，而使用者可能根本想不起 B 已经降级这回事。

同样的需求，在 remnote 里可以很轻松地做到，直接把 A 设为 B 的父节点就好了，而且 A 和 B 都可以标记为文档放在左侧文档树里，在 remnote 中，「文档」只是节点的一个可有可无的状态，没必要单独做出一个名叫「文档」的对象。在 remnote 中，所有节点都处于同一个空间里，它们可以自由地进行组合，发明一个叫「页面」的东西会让这个空间出现两种不同的粒度，名叫 A 的节点不能变成名叫 A 的页面，名叫 B 的页面不能变成名叫 B 的节点，这是对自由度的破坏。所以我一直都说「页面」这个概念在大纲型笔记上没必要存在，roam research 的页面重命名合并功能也不是非得要有一个页面命名空间，remnote 会主动探测重名节点、同样能解决这个问题。

文档型双链笔记没法做到像 remnote 这样完全统一，但它们依然有办法来弥补这个问题，比如思源笔记可以让正文里的某个 H 标题及其下属内容转换成文档树上的一个文档、也可以进行逆操作让一个文档变成正文里的一个 H 标题和下属正文，无序列表也即将支持这种双向转换；在这个打破粒度限制的转换过程中，所有链接都不会失效，并且思源笔记把文档块也当成一种块，`[[` 和 `((` 也是完全统一的，转换之后依然可以在 daily notes 中搜到。思源笔记在这方面跟 remnote 的差距主要就是文件名只能有纯文字、不支持各种行级元素，但是这种调整上的自由度在所有文档型笔记里已经一骑绝尘了，大纲型双链笔记里除了完全统一的 remnote，也只有 roam edit 做了单向转换。

**总之，统一的粒度能带来最大的整理自由，只要认识到这一点，前期记录就能更加无压、更加肆无忌惮。**

PS：还有一些软件看起来做了跟上面几个软件有点类似的功能，实际上背后的思想和达成的效果差异很大。比如 notion 可以把一个文档锚文本在原地展开变成一篇正文，hypernotes 和 obsidian 的 note refactor 可以用正文内容生成新文件，但它们都无法保持住被操作对象原本的反链，这个问题在双链时代之前也许无所谓，但放到现在就很成问题了，更别说有的转换操作还不是双向对称的。究其原因，它们做这些功能的思考出发点可能就是一种很传统的思考，notion 现在连双链都只做了一点皮毛，距离意识到粒度统一的问题起码还隔着两步。

##### 为责任压力兜底的「背书」

上古时期，我刚开始用大象的时候，经常学习别人分享的使用经验。在这个过程中，我发现一个现象，不管是以笔记本分类为主、还是重度依赖标签，有相当多的人都有过把自己原本的笔记系统推倒重建的经历，因为觉得原先的不合适。

我自己折腾得比较多，当然也有过这种经历，而且这种事情只要做过一次，之后再用上一个新软件都会比较小心，想在一开始就把使用方式想好，避免将来再重建。

这不也是一种很大的心智负担吗？而且能靠自己把各种东西都琢磨明白的人也很少，其它人难道就只能一直承担这份压力吗？

这个问题其实有个很简单的解法，那就是把构建笔记系统的责任从用户身上转移到另一个人身上，这个背锅的人可以是笔记软件的开发者，也可以是 KOL 或某个社群。

就拿 roam research 来说，软件本身带有引导性，conor 自己也会发一些视频演示来教大家使用，只要用户愿意相信 conor，直接跟着做就行了。以前如果笔记系统设计得很失败，那就全是使用者自己的责任，现在全都是 conor 的责任。万一将来觉得这套东西不行，就算免不了要重建，但用户至少可以直接去骂 conor 嘛（划掉），自己是没有任何过错的。

除了开发者自己，某个发教程的人或是整个舆论环境也可以起到背书作用，“既然某某大佬都这么说 / 既然大家全都这么说，那江来出了什么问题也不是我的泽任。”背书这件事情吧，除了对背书的那个人不好，对其它人都好。

所以我想，如果开发者预设一套笔记软件的使用方法并为之背书，那不是在束缚用户，而是主动把构建笔记系统的责任从用户那里揽到了自己头上，这样一来卸下责任的用户就会觉得很轻松，可以专注于内容。有一句话我百提不厌，大多数人需要的是明确且适当的束缚，超出能力范畴的自由只会让人无所适从。

双链笔记基本都能互相沾光、共享一点背书效果，这也是对用户轻松无压使用的一层保障。

#### 总结：支撑 daily notes 流程需要的功能

用表格来总结一下支撑 daily notes 流程需要的功能，顺便列出各个软件是否具备这些功能。除了 roam research 之外，这里只列出几个在双链上学得比较到位的双链笔记。

| 名称 | roam research | remnote | logseq | 葫芦笔记 | roam edit | 思源笔记 |
| --- | --- | --- | --- | --- | --- | --- |
| 统一粒度 | 否 | 完全统一！ | 否 | 否 | 统一了，但没完全统一，顶级主题没有降级选项 | 统一了，但是文档名不支持行级元素，所以比 remnote 略差 |

上面这些功能中，别的功能如果实在没有也不是不能忍，唯独反向链接绝对不能掉链子。

为了对 daily notes 中生成的主题进行后期整理，反链最最起码也要像现在的思源笔记一样，跟正文单向打通（正文流通到反链其实也可以，要靠浮窗），让反链的内容能流通到正文里；其次，反链展示要合适，反链的核心必须是上下文，而很多文档型笔记在反链里只展示一个文件名，不仅没用还占地方，说它是弊大于利都不为过。

另外，大纲编辑器（或无序列表）的反链层级展示规则虽然不怎么受重视，但实际上非常重要，下一节会详细讲它。

#### 暗坑：反链的详细对比

对大多数人来说，直接用双链笔记上的 daily notes 流程几乎就是最优解。当然它并不是一丁点缺点都没有，我觉得这套 daily notes 流程最主要的缺点在于数据可迁移性，当然这并不是什么特别大的问题，但我们还是非常有必要把它分析清楚。

因为绝大多数存在于 daily notes 中的笔记永远不会迎来被真正整理的那一天，所以已有的数据最好是存在于一个具备完善双向链接功能的软件上。也就是说，最好别把数据迁移到那些根本不具备双向链接功能的笔记软件上，有些软件虽然来蹭了双向链接的热度，但是它们的反链做得很差，根本无法实践高效无压的 daily notes 流程，同样不能为你的数据接盘。

而且，就算同样是具备比较完善双向链接功能的软件，直接迁移也不一定合适，接下来我们就来详细看看，各个主流双链笔记软件在反链上的差异。

#### 大纲型双链笔记的反链

##### roam research 的反链

这一部分当然是要从 roam research 说起，然后我们再来对比一下做得比较好的几个大纲型模仿品。

现在，我们先按照比较常规的用法，在 daily notes 中记录下这样的内容：

```
- [[反链层级]]
    - 一级子节点
        - 二级子节点：“是我不配吗”
- 对于大纲双链笔记来说，[[反链层级]] 的展示规则直接决定了双链使用体验
    - 一级子节点：“这次我没必要出境了吧”
        - 二级子节点：卑微.jpg
```

然后我们打开 `[[反链层级]]` 这个页面，看看 roam research 是如何展示反链的：

![](https://pic4.zhimg.com/v2-a82c58fa1180834159f797db6dc9e6df_r.jpg)
第一个锚文本独占了一个节点，也就是说**这个节点本身没有包含任何额外的信息**，在展示它的反链时，当然需要展示下级节点；如果这个锚文本的下级节点比较多，roam research 只会展示一级子节点，从二级开始的子节点则会折叠起来，因为一级子节点往往已经提供了一定的信息量。而且在 daily notes 中，独占一个节点的锚文本基本上都是用来把内容传递给某个主题页面的「传递型双链」，把所有下级节点全部展开会让这条反链占用过多的视觉空间，也不太利于后期快速回顾和整理。

第二个锚文本没有独占一个节点，而是自然地出现在了一个句子的中间，它就是前面提到的「关联型双链」，而**这个句子本身已经包含了很多的额外信息**，所以 roam research 在反链里直接把它的下级节点全部折叠了起来。当然凡事都有例外，要是在锚文本旁边加一个 emoji，其实谈不上有足够的额外信息量，但 roam research 依然会把所有子节点都折叠起来：

![](https://pic1.zhimg.com/v2-737ab34b49ac3849686a67c1acc4b9b4_r.jpg)
另外补充几点：

1. 「锚文本独占一个节点」准确地说是一个节点里只有锚文本和空格，所以写上多个锚文本也是一样的效果
2. 上述反链规则对井号标签也是一模一样的
3. 「提及」的展示规则就比较简单粗暴了，无论关键词是否独占一个节点，下级节点都不展开，它的预期目的可能是发掘出现在整句里的关联型双链

总的来说，roam research 对反链层级的分别处理规则是大纲双链笔记里最科学的，它没有一刀切地把子节点全部展开或是全部折叠，能比较灵活地应对各种情形。

至于少数特例，就需要用户在了解反链规则之后自行规避了，从这里也能看出，用户的用法也需要去适应软件的设计，假如有一个人就是喜欢把锚文本下方的所有子级在反链里全部展开，那 roam research 就不适合了。

##### remnote、roam edit 和葫芦笔记的反链

roam research 对于反链层级的展示是分了两种情况来处理，而这三个软件都没有做细分处理，所以 roam research 的一些使用方式就不适合照搬过来。

无论锚文本有没有独占一个节点，remnote 和 roam edit 都只会展示锚文本所在的那一个节点，下级节点全部折叠起来：

```
- [[反链层级]]
    - 一级子节点
        - 二级子节点：“是我不配吗”
- 对于大纲双链笔记来说，[[反链层级]] 的展示规则直接决定了双链使用体验
    - 一级子节点：“这次我没必要出境了吧”
        - 二级子节点：卑微.jpg
```

![](https://pic2.zhimg.com/v2-24c324b616a81f68d710099820151e01_r.jpg)
![](https://pic4.zhimg.com/v2-3960d34803177b492cd6d222866c0eff_r.jpg)
我觉得这种一刀切的方式不如 roam research 科学，锚文本独占一个节点时，这个节点完全没有额外信息量，可这两个软件的反链并没有把下级节点直接展示出来。所以在这两个软件里，假如想在 daily notes 中记录一张图片，还得用上软换行：

![](https://pic4.zhimg.com/v2-311e83cfdb864168ae5d9413304d6773_r.jpg)
![](https://pic1.zhimg.com/v2-a2c2b1ea877a14175739d0867df2be40_r.jpg)
图片是一个简单而特殊的例子，靠软换行就能完美解决，要是有一堆节点呢？在 roam research 中用独占锚文本 + 下级节点的方式来做传递型双链起码有三个好处，一是排版错落有致，二是在反链中辨认传递型双链很方便，三是仅用一个锚文本就能传递一整个子列表，而第三个场景在 remnote 和 roam edit 这种反链形式下是比较难受的，用软换行就会丧失列表结构，不用软换行就没法在反链里一览无余地看到。

葫芦笔记的反链层级又是另一种景象，无论锚文本有没有独占一个节点，反链里都会把所有子级节点全部展示出来：

```
- [[反链层级]]
    - 一级子节点
        - 二级子节点：“是我不配吗”
- 对于大纲双链笔记来说，[[反链层级]] 的展示规则直接决定了双链使用体验
    - 一级子节点：“这次我没必要出境了吧”
        - 二级子节点：卑微.jpg
```

![](https://pic1.zhimg.com/v2-3807bbf1df50a3bf52c8aedc0e63e140_r.jpg)
比起前两个软件的全部折叠，或许全部展开还更好一点，起码全部展开的反链必定能提供足够的信息量。不过全部展开的缺点就是有时候看起来太多太乱，而 roam research 的规则就刚好合适。

##### logseq 的反链

logseq 的反链展示规则是大纲双链模仿品里跟 roam research 最像的，虽然它也没有分情况处理，但它做到了折中。logseq 的反链规则是这样，无论锚文本有没有独占一个节点，反链里都只展示第一层子级，更深的子级全部折叠：

```
- [[反链层级]]
    - 一级子节点
        - 二级子节点：“是我不配吗”
- 对于大纲双链笔记来说，[[反链层级]] 的展示规则直接决定了双链使用体验
    - 一级子节点：“这次我没必要出境了吧”
        - 二级子节点：卑微.jpg
```

![](https://pic3.zhimg.com/v2-a49190a64338ce15295a9d84527e9cb2_r.jpg)
比起全部折叠和全部展开，我觉得这种规则更合理一些，在锚文本独占一个节点的时候能展示一层下级来提供足够的信息量，在锚文本没有独占节点的时候也不会展开过多的层级，而且包含锚文本的长段落下方很多时候本来也没有下级。

其实反链展示规则一路对比下来已经没有什么好多说的了，我觉得 roam research 的分情况处理最科学、也最能适应大多数人各自不同的使用习惯。**但是如果要使用其它大纲双链笔记，就要先把它们的反链形式搞清楚，因为 daily notes 的书写方式要跟反链形式相适应，不然后期无论是查看还是整理都很难受**。

顺便一提，如果按照我在 roam research 中的写法，锚文本下面的子列表中如果再次出现第二个同样的锚文本，这第二个锚文本依然会出现在对应页面的反链中（有上级面包屑），这根本就没有任何意义。所以不要滥用双链，为了链接而链接有害无益，纯粹是给自己添堵。

#### 文档型双链笔记的反链

虽然上面这个标题说是文档型，但实际上现在文档型的双链笔记里也只有思源笔记的反链足以支撑使用者实践完整的 daily notes 流程，所以这一节主要是拿它来跟大纲型的软件做一些比较，顺便对上一节的内容做一点补充。各个文档型双链笔记的反链上存在两个主要问题，一是不愿意优化列表的反链展示，二是不愿意打通反链和正文，至于为什么不愿意、该不该愿意、是不愿意还是没意识到，那都不重要，反正这两个主要问题如果不解决，就无法实践 roam research 上那种高效的 daily notes 流程。我知道你们要说什么，先别急，往后看。

思源笔记早期版本的反链面板哪怕放在文档型双链笔记里也算比较差的，但是后面先是照着 workflowy 优化了编辑器里的列表操作，最近又单向打通了反链和正文，特别是在列表层级规则上 100% 复刻了 roam research，所以这一节内容直接放到了 roam research 下面。

把上一节的测试内容放到思源笔记里，去 `反链层级` 这个页面看看反链的展示：

```
- [[反链层级]]
    - 一级子节点
        - 二级子节点：“是我不配吗”
- 对于大纲双链笔记来说，[[反链层级]] 的展示规则直接决定了双链使用体验
    - 一级子节点：“这次我没必要出境了吧”
        - 二级子节点：卑微.jpg
```

![](https://pic3.zhimg.com/v2-3d0768e086f528412ee19af8eabe6d06_r.jpg)
在反链层级选取上也没有什么好多说的，就是跟 roam research 一模一样，前面已经分析过了 roam research 的反链规则好在哪。另外，作为文档型软件，思源笔记在列表块之外还有标题块和普通段落块，它们的反链规则基本也是保持跟上面一样的思路，这就属于更灵活的进阶用法了，在这一篇里先不多提。除了合适的层级规则，现在思源笔记反链面板中的内容也可以直接拖动到正文中了。

现在的思源笔记证明了文档型双链笔记不是解决不了这两个问题，主要还是取决于意愿。v1.3.6 反链更新之后，虽然单说双链体验比起 roam research 还差得不少，但实践完整的 daily notes 流程问题不大，论坛上也有用户发了[使用感受](https://link.zhihu.com/?target=https%3A//ld246.com/article/1633719399031/comment/1633788290213%23%25E4%25B8%2580%25E4%25BA%259B%25E6%2580%259D%25E8%2580%2583)，反响还是很不错的。

主要矛盾已经解决了，至于次要矛盾，至少从开发计划上看，短期内应该不会解决。具体来说，roam research 等大纲双链笔记的反链都具备完整的编辑能力，而思源的反链面板只展示纯文本，还是稍微差点意思。

举两个小例子。

在上面的图中，一级子节点的下方究竟是不是还有二级子节点，从反链面板上是无法直接看到的，需要把鼠标放上去在浮窗里看。

![](https://pic2.zhimg.com/v2-df396aaa46967f213e0c44a4f83ee999_r.jpg)
这个操作虽然看起来相当简单，只需要把鼠标放上去就可以了，但是在做这个动作之前我并不知道该节点下方是不是还有子级内容，悬浮预览的结果可能是「有」也可能是「没有」，而每一次「没有」都是一次负反馈，多来几次负反馈我基本上就没有动力再把鼠标移上去看了。也许有人能在这件小事上战胜人性，反正我不能，宝贵的意志力不应该消耗在这种地方，而且设计者本来就应该在心里把全体用户都假设成极品懒狗。

所以浮窗这种形式有时候是有局限性的，**任何操作上只要存在不可预知的负反馈，它就很有可能直接沦为摆设**，正反馈奖励特别强的除外。（题外话：用这套逻辑来分析，绝大多数双链笔记里查看 PDF 某条批注被引用情况的小功能也都是鸡肋）

第二个小例子是这样的，我有时候看到有意思的图片会顺手收集一下，在 daily notes 中我是这样记录的（在 roam research 和思源里，这种记录完全没必要用软换行，我是为了数据通用性才会用这种写法）：

![](https://pic2.zhimg.com/v2-cb944456da0283177ad95c444ef7899d_r.jpg)
但是在传递型双链对应的那个页面中，反链只会显示纯文本，对于图片块只能看到一个 `image.png`：

![](https://pic2.zhimg.com/v2-f06fc1ce0ed85bda7eccb7c7d754ac25_r.jpg)
虽然把鼠标放上去就能直接通过浮窗查看图片，而且这里也不存在不可预知的负反馈，但是在人家大纲双链笔记里直接看反链就能看到图片。并且这个笔记又没多大价值，我也懒得把反链拖到正文里查看。好在思源里有一个技巧可以应对这种只需要汇总查看的场景，那就是在笔记里插入一个 SQL 查询块来模仿反链：

![](https://pic1.zhimg.com/v2-78c0da846ad14d9c786952a7ceb3eeb4_r.jpg)
这个 SQL 查询块的作用是查询所有链接到当前文档块的块，并做了一些展示上的小优化，用来汇总查看这些图片太合适了。不会写查询也可以直接从自带集市里下载这个模板（meteor/link），如果稍微懂一点 SQL 的话，可以像我一样把这个汇总 SQL 的排序方式改成随机排序，这样一来就能让不同时间点的记录全部翻涌起来，比按日期排序更舒服。SQL 块的可定制性让它在「仅回顾查看」这个场景下比真正的反链还好用。

思源的反链面板只显示纯文本造成了一些双链使用上的局限，但是漏网之鱼也有。双链有一个前面没提过的经典用法：把一个带复选框的任务项传递到指定日期的 daily notes 页面，等那一天到来的时候，daily notes 的反链里可以看到这个任务并直接打勾：

![](https://pic4.zhimg.com/v2-ff4a081d385d1c19c3ff41242c356b23_r.jpg)
![](https://pic1.zhimg.com/v2-733370e95a390cf18639d2453bc037b4_r.jpg)
在思源笔记的纯文本反链面板上虽然不能直接看出这是一个任务项，但按照多数人的通常用法，会链接到未来某一天的基本也只有任务项了，所以在这里还算能用。不过任务项有时候也会传递给 daily notes 以外一般的页面，conor 自己的演示当中就有这样的用法，所以反链面板做简单了还是有局限。

双向链接还有一些实用技巧是思源目前这种反链面板应付不了的，有的用法思源即使加上 SQL 块也应付不了，还有一种特殊的双链用法即使在大纲型双链笔记里也只有 roam research 能做到，而且那个用法既简单又实用、根本不是什么小众花活，但是跟这段关系不大我就不浪费字数了。双链的可能性很多，「互相链接」「网状」之类的理解根本连门都没有入（没错，我说的就是过去的自己），所以 roam research 之外的双链笔记想一次性就把双向链接抄到位肯定是不可能的，真正重要的是开发者有没有一种开放的心态来不断改进。

大纲型双链笔记直接照着 roam research 抄就很安全，但是我觉得它们还是太急于做出差异性了，各个大纲型模仿品都有 2 个以上的双链关键设计没学到位；而作为文档型笔记，思源笔记能跌跌撞撞做到现在这样已经很值得表扬了，毕竟文档型的前辈软件当中也没有一个真正懂双链的，我之前发过一个[关于双向链接的问卷调查](https://link.zhihu.com/?target=https%3A//www.yuque.com/deerain/gannbs/zwukhg)，虽然当时问卷设计得不是很好，但是从大家对某些关键问题的填写结果上也能看出有约 80% 的人对双链还没有完全入门，而把文档型笔记作为主力工具（包括当初还没优化反链的思源笔记）的人几乎全部落入了这 80% 中。这些文档型笔记也许在其它方面做得特别好，但 roam research 的双向链接它们是真的没学明白，光一个最基本的 daily notes 都应付不好（对这句有疑问的请看下一节），更别说其它双链用法了。

总结一下这一节，在文档型双链笔记里，只有思源笔记的反链算是摸到了及格线，在反链层级规则上它跟 roam research 保持了一致，虽然反链面板还比较简陋导致在双链这部分跟 roam research 有不小差距，但是完整实践基本的 daily notes 流程没问题，我自己也把思源笔记当做主力工具之一在用。

其它文档型双链笔记的反链现在还处于混沌状态，已公开的开发计划也不涉及对这方面的改进，这本来没什么，毕竟任何项目都是一步步慢慢改进的，但是社区的舆论氛围让我感觉这个问题基本没有改善的希望。

#### 暗坑：双链标签与传统标签

#### 双链标签

先来看一个包含了 3 种锚文本用法的 daily notes 小例子（roam research）：

![](https://pic3.zhimg.com/v2-68453b4dbfd7a03646077f3592895ee6_r.jpg)
在上图中，`[[github]]` 就是前面提到的「传递型双链」，因为我在 daily notes 中写下它的时候，内心真正期望的是把下面两个节点的笔记传递给 `[[github]]` 这个页面。正如前面反复提到的，这样做的真正目的是为了快速无压记录，而不是为了把 `[[2018-06-04]]` 跟 `[[github]]` 关联起来。

而 `[[微软]]` 就是前面提到的「关联型双链」，这种链接一般出现在某个句子当中，它在这里起到的作用就是强调关联性。

最后的 `#商业新闻` 是一个标签，有些词语没法自然地出现在句子里，但是又想用它来对当前节点做点标注，此时就可以把这个词当标签来用。大纲双链笔记里被称作标签的东西其实只是锚文本的一个语法糖，它的功能完全覆盖了传统标签，把它称作标签就像是在说「汽车是一种吃油的马车，这是对汽车最准确的定义」一样，有一种沉湎回忆走不出来的感觉，我宁愿把它称作「井号双链」。

大家也知道，在 roam research 等大纲双链笔记中，`[[主题]]` 和 `#主题` 在创建之后是等价的，在反链呈现上也一样，如果在上面的例子里想把传递型双链 `[[github]]` 写成 `#github` ，当然也是可以的，关键是自己要统一用法。

做得合格的大纲双链笔记就是这么简单，而文档型笔记就多了一点暗坑。

#### 传统标签

既然在 roam research 中可以用标签语法糖代替双方括号的 wikilink，那么在思源笔记这种文档型软件里直接用井号标签代替传递型双链来实践 daily notes 流程行不行？

大家听我一句劝，真的不行。早就有人在这个坑里踩得遍体鳞伤了。

不能这么做的原因很简单，就是没法整理。比如在 zettlr、obsidian 和思源里，文本标签只是正文里的字符串，点击某个文本标签只是对正文里的特殊字符串做了一次搜索，汇总结果是在搜索界面展示：

![](https://pic4.zhimg.com/v2-f71ef5c6d6214418d761ba3e706db77b_r.jpg)
而这样一个搜索界面的可整理性基本为零，使用者最多只能依次点开每一条搜索结果去修改，但是根本没法把它们合并起来，真要做这件事只能靠剪贴板，除了汇总还有排序的问题，相当麻烦。

同样是面向剪贴板做整理，这些文档型笔记**甚至还比不上前期拿 flomo 记录然后靠剪贴板移到另一个比较结构化的软件里组织**，最起码人家 flomo 对筛选结果能一览无余地展示，跟自带卡片整理能力的 writeathon 就更没法比。

思源里的 SQL 块很适合汇总展示跟某个标签相关的内容，但是真的到了要整理的时候也不方便。**而思源笔记里改版之后的反链面板只需要直接把传递型双链往正文里合适的位置一拖就行了，汇总加排序一气呵成**。

所以如果在 daily notes 里使用文本标签，后期整理绝对是一场灾难，以前一个被我安利过 obsidian 的朋友就是这么做的，后来有一天她比较急迫地需要整理输出的时候直接被面向剪贴板的繁琐整理步骤逼疯了，最后只能老老实实回去用 remnote 这个本来有点讨厌的大纲型软件。思源 1.3.6 反链改版之后我又推荐了一下，但人家已经对这些使用传统标签的软件 PTSD 了。

方便的后期整理能力是前期无压记录最重要的一层保障，如果使用文本标签而非传递型双链，这层保障就没了，而整理困难的事情只要发生过一次，对流程和软件的信任就会崩塌，前期记录时就不可能再做到无忧无虑。文本标签不适合作为 daily notes 的主力军绝不是因为它听起来没有双向链接酷，而是因为文本标签记录的东西在后期远不如反链那么好整理，虽然快速无压记录的核心是把整理的压力推给未来，但今天的自己还是需要给未来的自己做一点简单而靠谱的铺垫，不能推个烂摊子给人家。「标签」从来都不是多么强大的东西，更不是什么必须得用的东西，需要使用文本标签的场合也有，但它肯定不能直接在双链笔记的 daily notes 里代替传递型双链，在不适合文本标签出场的时候，就让它老老实实在旁边当配角吧。

比起大纲双链笔记，思源笔记是带有文档树的，所以在 daily notes 里 `[[` 产生新文档的路径需要单独设置一下，直接放在一个待整理的特定目录里就好。如果能够放下执念，那就把整理这个目录的任务直接推给未来，将来某一天你多半会庆幸自己当初没花时间吭哧吭哧整理文档树；如果不能放下执念，现在文档树的移动功能也比较方便了，以后还会继续优化文档树的批量移动功能，定期整理一下还是不费事的。

#### 例外

但是，在 writeathon 和 workflowy 中用文本标签来做快速记录突然又合适了，这又是为什么呢？其实逻辑和上面一样，因为 writeathon 和 workflowy 对速记内容的后期整理能力太强了。拿 writeathon 来说，它的反链做得一般，如果想要后期能够方便地整理，前期速记的时候与其用双链还不如用文本标签。所以快速无压记录这件事一定要从后往前定义，不能只考虑记录时那几分钟的事情。

#### 拓展：有没有别的方案？

跟绝大多数人现在使用的方法比起来，双链笔记上的 daily notes 流程是帕累托改进，基本上可以说是有利无弊，康奈尔笔记法之类的方法跟它一比更是处处溃败。但是如果现在正在使用的主力笔记工具的功能撑不起这套方法，而且也不想更换，读到这里之后可能是这种反应：

我十分理解大家的心情，数据迁移的痛苦我最清楚了，可是双链笔记的这套用法真的有点厉害，大多数人拿不出比它更好的方案。很多人特别特别喜欢在知识管理或者说做笔记这件事上反思，觉得我们不能「折腾工具和方法」，要「专注内容」「回归初心」，**可问题是，最能让人免去无数折腾、最能让人丢掉心智负担专注于内容、最接近初心的方法，恰恰就是 daily notes + 双链**，这就很尴尬了。可惜这套方法要有合适的功能支撑才能实践，这直接把很多人挡住了。

没关系，我还有别的弥补方案。

如果一个软件上无法实践基于双链的 daily notes 流程，同时又拿不出一套效果接近的方案，搭配一个速记软件是很自然的选择。

不管是什么文档型笔记软件，只要支持导入 markdown，都可以搭配一个 writeathon 来使用。

![](https://pic2.zhimg.com/v2-8c4279f82f06050b6df8ba967421aea1_b.jpg)
writeathon 有一个卡片盒功能，非常适合用来进行临时笔记的记录。表面上看，writeathon 的卡片跟 flomo 里的一条 memo 形式差不多，都是写点正文然后加上多级标签，但是，writeathon 其实是一个为了输出而存在的**写作软件**，所以它对于卡片盒里的零碎记录有非常强的管理、关联、组合能力，这是那些定位成「速记类 app 」的软件不可能具备的。至于 writeathon 在这方面具体有哪些功能，因为太费篇幅我就不多写了，大家可以自己去试，在这里只提一点，在所有的写作软件中（不是笔记软件），writeathon 是唯一支持双向链接的，虽然以双链笔记的标准来看它的反链做得并不合格，但至少可以拿 `[[wikilink]]` 来记录一点关联性，或者做点简单的 moc。

前面也反复提到，前期的无压记录需要后期的整理能力来兜底，flomo 这样的速记软件当然不可能具备这样的功能，所以官方推出了一系列弱弱联合的文章，大致思路就是把 flomo 中的内容复制到另一个有组织功能的宿主软件中进行整合，比如 notion 或者幕布。先不说面向剪贴板转移数据麻不麻烦的问题，notion 和幕布这些软件的整理能力虽然在笔记类软件中已经算是比较不错的了，但还是比不上 writeathon 这种真正的写作类软件，所以我一直觉得速记软件直接拿 writeathon 是最好的。

如果对 writeathon 这种没名气的产品不放心，用 flomo 来搭配一个文档型笔记软件来使用也是可以的，毕竟 flomo 也有一些其它方面的优点，哪怕 flomo 和宿主软件都没有什么整理能力，但哪怕仅仅是把零碎记录和成型文档隔离开，对后期整理都是有帮助的。但如果选择 flomo，就需要平时多看看官方号发布的文章。如果说双链笔记是靠软件本身的功能来保证用户能放心无压地实践 daily notes 流程，那么 flomo 就是靠文章来保证这一点，只要用户读完之后足够上头，前期记录的时候就能做到无压。官方号发的文章我自己也经常看，有一些写得的挺不错的。

速记类 app 因为要顾及数据兼容性，所以自身的编辑器富文本特性肯定不可能做得很强，flomo 更是连图文混排都不支持（这可能会是一个切入点，官方可以试着出篇新文章把它说成优点）。如果想要富文本特性很强的速记工具，基本也就只有语雀小记了，虽然语雀小记别的方面全都不如 writeathon 卡片盒，但语雀的富文本编辑器还是很给力的，而且小记跟语雀文档也单向打通了，算是有一点整理能力，有了手机 app 之后的使用效果应该还不错。

选一个速记软件来搭配使用只是表象，其核心依然是把心智负担推给未来，同时靠后期整理能力来为前期记录压力兜底。有一些待探究问题我直接当成任务记录在滴答清单里，也能达到把心智负担推给未来的效果，但是任务管理是一个非常危险的领域，最好不要轻易触碰。

#### 提前回复

> 需要给 daily notes 设置模板吗？

一般来说，大纲双链笔记里的 daily notes 模板都是配合查询语句和插件使用的，如果光是做个内容模板那就没什么必要，特别是那种用一大堆标题来分隔内容的模板，因为将来你**一定**会想要换模板，那一天会因为老数据和新数据形式不统一感到难受的，反正我是受不了，而且内容模板会限制我的灵活发挥。

> daily notes 中记录的是“卡片” ？

如果把一个东西称作卡片仅仅是为了把它称作卡片，做这种事又有什么意思呢？对于为什么要使用卡片这个问题，梅棹忠夫在《智识的生产技术》里有简单明了的回答：以前做田野调查的时候，传统笔记本记录的内容不方便后期整理，**活页笔记本的手感又不太喜欢**，所以就开始制作纸质卡片了。

> daily notes 流程适合所有人吗？

如果光看方法本身，我觉得它适合所有人，但不是所有人都能敞开心扉接受它。不接受的原因可以有很多种，有的是不想换工具所以主动抗拒，有的是不想换立场所以主动抗拒，不过最令人感慨的一种原因还是「不曾为自己当初的不成熟感到悔恨」。有些道理就算提前知道也没用，必须要碰过壁才能真正领悟，我想这就是青春吧。

> 我不喜欢碎片化的记录

真正需要反对的是「过度碎片化」而不是碎片化，我们的很多灵感本来就只是只言片语，而且当时可能也没有太多的时间去详细展开记录，如果反碎片化反得魔怔了，就会错过很多有价值的灵感，真正重要的事情是在将来把这些碎片化的记录整合成完整系统的内容，**更别说 daily notes 只是换个记录入口、并没有鼓励碎片化**。

> 这篇文章涵盖所有的双向链接使用技巧了吗？

肯定是没有的，本文只提了一小部分双向链接用法，不过对 daily notes 来说大体够用了。

还有一点也要强调一下，本文用来示范 daily notes 形式也只是适合新人上手的最基本形式，可以说只写了非常小的一部分，因为我觉得没必要在这篇文章里靠堆砌形式和技巧来凑篇幅，最重要的是把基本原则写清楚，这样才能让新人敢于迈出实践的第一步。

至于剩下的技巧，有空再另外单独写，这篇文章虽然有两万字的篇幅，但它其实只是个楔子。

> 既然还有很多使用技巧没有写到，是不是说明双向链接很复杂？我又双叒叕要开始反思了，我不想过度折腾工具，我要专注于内容本身。

今天我可以非常肯定地讲，至少本文里已经提到的用法都是相当简单的，如果只往能维护自己现在立场的方向去反思，死活不肯接纳双链，**日常的笔记流程必定比用双链的人更费时更折腾**，无论这个流程是什么样。我曾经对双链有误解，现在已经纠正过来了，而且我还主动把当初的黑历史拿出来晒，希望大家也能更勇敢地面对自己。

另外，其实大家也看得出来，daily notes 流程中的传递型双链锚文本在使用的时候跟标签有点类似，而 logseq 的 namespace page 则跟多级标签有点类似。**如果比双链弱上几十倍的传统标签都有用，完全覆盖了传统标签功能的双链又怎么可能没用？**光是在快速无压记录场景下，双链就已经体现出巨大的威力了。也不要再说双链笔记上手难了，明明就简单至极。
