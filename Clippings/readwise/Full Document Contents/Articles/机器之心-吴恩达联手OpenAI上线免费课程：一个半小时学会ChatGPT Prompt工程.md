---
人员: 
  - "[[机器之心]]"
tags:
  - articles
日期: 2023-04-28
时间: None
链接: https://www.jiqizhixin.com/articles/2023-04-28-8
附件: https://image.jiqizhixin.com/uploads/article/cover_image/abcec02b-46d6-4255-a590-debf2173df06/1.jpg)
---
## Document Note

## Summary

大模型时代，也得看吴恩达的教程。

## Full Document
ChatGPT 来了，一切变化都快了起来，一些科技公司开始招募「prompt 工程师」。与写代码的传统计算机工程师不同，Prompt 工程师通过向 AI 询问一系列逻辑缜密的问题来测试系统是否存在不合理行为或问题。但具体应该怎么做，一直没有统一的规范，或是成体系的经验。

刚刚，吴恩达官宣了一个好消息：他和 OpenAI 一起制作了一节关于 ChatGPT Prompt Engineering 的新课，而且课程是免费的。

![](https://image.jiqizhixin.com/uploads/editor/893524b1-e416-4a5f-b55a-61264dda3fe1/image__3_.png)
课程链接：https://www.deeplearning.ai/short-courses/chatgpt-prompt-engineering-for-developers/

吴恩达说，现在市面上有太多关于如何写 prompt 的材料。但对于开发者来说，如何调用大模型 API 来构建应用软件才是更重要的，但这方面材料却很少。这就是他们这门课的价值所在。

该课程长度总共一个半小时左右，旨在帮助开发者们：

* 学习应用开发所需的 prompt engineering 最佳实践；
* 发现使用 LLM 的新方法，包括如何构建自己的自定义聊天机器人；
* 获得使用 OpenAI API 编写和迭代 prompt 的实践经验。

从实践层面来看，该课程将展示 LLM API 如何用于各种任务的应用，包括：

* 摘要（例如简化用户评论的摘要）
* 推理（例如情感分类、主题提取）
* 转换文本（例如翻译、拼写和语法纠正）
* 扩展（例如自动撰写电子邮件）

所有的概念都有众多的例子加以说明，开发者们可以在 Jupyter notebook 环境中直接进行互动实验，以获得 prompt engineering 的实际经验。

![](https://image.jiqizhixin.com/uploads/editor/ba6e9291-79d6-4361-b575-87f4520764bf/1.jpg)
这门课程对初学者是非常友好的，只需要对 Python 有基本的了解。同时，对于希望接触 Prompt Engineering 和使用 LLM 的高级机器学习工程师来说，该课程也非常适合。

吴恩达说，到目前为止，对于希望使用 API 访问 LLM 来构建应用程序的开发者来说，关于最佳实践的材料相对较少（相对于如何 prompt Web UI）。他们很高兴能做出些改变。

**主讲人介绍**

![](https://image.jiqizhixin.com/uploads/editor/cb9f4e4c-ba65-444c-9045-907bb3205b01/1682645596658.png)
**Isa Fulford**

Isa Fulford 是 OpenAI 的技术工程师。她在斯坦福大学拿到了学士和硕士学位，曾先后在亚马逊等机构工作，2022 年 11 月加入 OpenAI。

![](https://image.jiqizhixin.com/uploads/editor/7d49930e-db60-49a7-a97e-857d08be6f96/1682647066168.png)
*Isa Fulford 的教育经历。*

![](https://image.jiqizhixin.com/uploads/editor/aa61650c-e0b5-4905-b968-991a49c77374/1682647288867.png)
*Isa Fulford 的工作经历。*

在进入 OpenAI 之前，她在一家名为 Mem.ai 的初创公司工作，该公司致力于将会议记录转化为有用且可操作的信息，其中便涉及对 OpenAI API 的调用。在 OpenAI 列出的 GPT-4 贡献者名单中，Isa Fulford 出现在了「Launch partners & product operations」一栏。

![](https://image.jiqizhixin.com/uploads/editor/446bf4a2-54b9-4e1e-a2e4-4c3ab13593dd/image__4_.png)
值得注意的是，Isa Fulford 是 ChatGPT Retrieval Plugin 的主要贡献者。该插件可以让用户以问答的方式轻松搜索和查找个人或工作文档，在 GitHub 上 star 量已经过万。此外，Isa Fulford 在教授如何将 LLM 用于产品方面也做了很多工作，是 OpenAI Cookbook（OpenAI API 的使用示例和指南）的主要贡献者之一。

![](https://image.jiqizhixin.com/uploads/editor/aecbfb88-9a23-431e-8dd1-97918dc91231/image__5_.png)
项目链接：https://github.com/openai/chatgpt-retrieval-plugin

![](https://image.jiqizhixin.com/uploads/editor/d0106c4d-9b75-46cb-8b5f-cf26d2d944ff/1682650828182.png)
项目链接：https://github.com/openai/openai-cookbook

**吴恩达**

吴恩达是斯坦福大学计算机科学系和电气工程系的客座教授，曾任斯坦福人工智能实验室主任。此外，他还是 DeepLearning.AI 的创始人、Landing AI 的创始人兼首席执行官、AI Fund 的普通合伙人、Coursera 的主席和联合创始人。

![](https://image.jiqizhixin.com/uploads/editor/52374626-2d09-41ab-a848-f58686bc703d/AndrewNg_2022-stageshot.jpg)
作为机器学习和在线教育的先驱，吴恩达博士通过他在 AI 领域的工作改变了无数人的生活，并撰写或合著了 200 多篇机器学习、机器人和相关领域的研究论文。2013 年，他被列入《时代》杂志评选的全球最具影响力人物 100 人名单。
