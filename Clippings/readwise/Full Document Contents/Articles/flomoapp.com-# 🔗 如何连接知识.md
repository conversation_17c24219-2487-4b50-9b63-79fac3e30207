---
人员: 
  - "[[flomoapp.com]]"
tags:
  - articles
日期: None
时间: None
链接: https://help.flomoapp.com/thinking/link.html
附件:
---
## Document Note

## Summary

## Full Document
![](https://resource.flomoapp.com/101/images-231.png!webp)
如果你玩过模拟城市就知道，城市的发展很大程度上取决于连接的方式 ——

* 如果没有高速公路，那么大家就会因为通勤问题导致不去上班
* 而如果到处都是大道而没有小路，居民又会抱怨没有休息交流的空间
* 缺乏连接会导致整个城市无法发展。

当你着急抵达某个地方的时候，你会考虑开车上高速，或者直接地铁直达。但当你想要放松或者随意漫步的时候，高架下面绝对不是一个好选择。

一座城市的连接方式（交通），某种意义上塑造了这座城市，而不是高楼大厦塑造了城市。

#### [#](https://help.flomoapp.com/thinking/link.html#我们大脑中的连接) 我们大脑中的连接

想象一下我们大脑中的知识，从来都不是一个分门别类整齐划一的图书馆。而更像是一个不断在扩展的城市，有高楼大厦，也有公园和贫民窟，被各种道路相连。

搜索就像是高铁，精准快速，但是无法到达铁路以外的地方，也不能中途下车；而标签像是大道，兼具了效率和精确度，但是很难通过大道直接抵达两个指定地点；而家门口的小路，则承担了散步，发呆，购买东西，社交等等功能。

在一篇介绍卢曼的 Zettelkasten 方法中[1]，也提到过类似的观点。搜索是到达指定卡片的确定性手段 —— 它们每次都产生相同的结果。

手动添加的链接是精选的参考资料 —— 创建一个链接的想法是独一无二的，它更加个性化，因此更适合帮助你思考。

![](https://resource.flomoapp.com/101/images-232.png!webp)
手动链接到另一个卡片，可以长时间巩固这种自发的联系。你可以给链接添加注释，以提醒你未来的自己注意上下文。所有这些可能对你有意义，而且只对你有意义。毕竟，这是你的第二个大脑，也是其存在的意义。

**flomo 之前只有搜索和标签，而我们最近将批注功能升级后，你就可以在 MEMO 之间，建立林荫小路了。**

例如，在 flomo 中记录了一些关于知识管理的内容，被放在 `#知识管理` 这个标签下。

其中关于知识的特征让我想起了另外一个之前记录的卡片 —— **以投资的视角看待知识管理**。这个 MEMO 被放在 `#投资` `#技术` 标签下。

在重温了**以投资的视角看待知识管理**这张卡片后，我又想起来之前和孟岩聊天时候关于复利的知识点，其中有很多关于复利的谬论，比如我们说的每天进步 1% ，其实很难有人真正做到如此高频的迭代。当然这张卡片的标签是`#朋友/孟岩` `#投资/复利`

基于以上的连接，我在直播中就讲述了关于复利的一些误区，避免大家在积累知识的时候产生许多妄念。许多同学听了之后亦反馈很有趣，**因为从未用这种投资的视角来看待知识的积累。**

![](https://resource.flomoapp.com/101/images-278.png!webp)
如上文所说，这三张 MEMO 被放在完全不同的标签下，如果通过搜索或者标签检索，他们很难同时出现在一起。而如果为他们新建标签，那么最终会造成标签的泛滥，依旧查找困难。

所以最简单的方法，是让这三张卡片相互引用，建立一条属于他们自己的小径。这样你在翻看任意其中一个 MEMO 的时候，就能联想起来其他的内容。**在科学史上，许多新的知识，也都是在这种交汇的地方产生。**

而随着这种小径建立的越来越多，许多新的知识就会被创造出来。你就能感受到「知识的复利」带来的巨大价值。

![](https://resource.flomoapp.com/101/images-279.png!webp)
#### [#](https://help.flomoapp.com/thinking/link.html#如何使用) **如何使用？**

**目前网页版已经全面部署，App 需要到7月下旬一起更新。目前创建连接的方法有两种：**

![](https://resource.flomoapp.com/101/images-235.png!webp)
当你完成链接后，被关联的那条 MEMO 亦会自动添加上是谁链接至此这样一次添加，两条 MEMO 都能方便的找到彼此了 —— 这便是现在比较流行的一个词「双链」，但我们仅认为这是一个非常朴素的小功能，最终能否产生巨大的价值，还是依赖于你自己思维上的变化。

另外后续会有更简单的关联方式，请继续往下看 😝

如若希望**新建一条 MEMO** 和过往的 MEMO 关联。可以在过往的 MEMO 菜单中选择「批注」，然后能看到在输入框内自动添加了一段链接。这时候你只需要继续输入你的看法，然后选择发送，即可创建成功。

![](https://resource.flomoapp.com/101/images-236.png!webp)
`[1]` 介绍卢曼的 Zettelkasten 方法中: *https://zettelkasten.de/posts/search-alone-is-not-enough/*
