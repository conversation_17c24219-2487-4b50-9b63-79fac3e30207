---
人员: 
  - "[[少数派编辑部]]"
tags:
  - articles
日期: 2025-01-17
时间: None
链接: https://mp.weixin.qq.com/s/C2TKbvMDpPBvVQGfv_ZHVQ
附件: https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwgOEGciaqZGxASf0J48Kk3Olw7EC9JgiagQPauJEdIdPiavlf8Qib5ZSIlg/0?wx_fmt=jpeg)
---
## Document Note

## Summary

J1 Assistant：以 AI 之名回归的锤科智能助手、Placify：地点打卡、旅游规划

## Full Document
欢迎收看本期《派评》。如果发现了其它感兴趣的 App 或者关注的话题，也欢迎在评论区和我们讨论。

**本期目录**

##### 🤖 J1 Assistant：以 AI 之名回归的锤科智能助手

📱 Placify：地点打卡、旅游规划

💻 ima.copliot：腾讯出品的 AI 知识库应用

💻 Notion Faces：捏一个 Notion 风格的个人头像

🤖 懒相机：易用又有趣的复古相机应用

🌐 Trakt 2.3：自动追踪流媒体观影记录

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/oylw20gGnRhswnRv0tJeklfoMbU4MPYibHibkwyEfFMkicLlbF5ECDlFDF2jlNp5ORtcvic5zGuv6iaEARIsibaeeLZA/640?wx_fmt=png&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
#### **📱**

#### **值得关注的新 App**

虽然少数派一直在为大家发现和介绍各平台上的优质 App，但仍有不少设计、功能、交互、体验都非常优秀的 App，还没有被我们发掘和介绍。它们可能是一款老 App，也可能是近期上架的新 App，我们会在这里介绍给你。

**▍****J1 Assistant：以 AI 之名回归的锤科智能助手**

* 平台：Android
* 关键词：AI、智能助手

@Noah\_Choi：是的，「那个男人」又再一次回到了手机圈。只不过这一次不是做手机或者其他硬件，而是迎着 AI 热潮，发布了一个名为 J1 Assistant（以下简称 J1）的手机智能助手。如果锤科手机还活着，它有可能作为类似 Siri 或者 Google Assistant 的产品内置于锤子和坚果手机当中，但是现在它只能作为一个独立的 App 并集成一些能够与 AI 交互的配套功能。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwh0HpZH09p2TWSqFGVAjeMxDVCgGg8qwUpKsvIlE1Eia03Bnv1fd5XQw/640?wx_fmt=webp&from=appmsg)
不过与其说是新开发的一个 AI 产品，不如说是借助 AI 能力调用「Smartisan OS 浓缩精华版」，因为从注册界面、功能选择再到操作方法，整个产品就散发着浓浓的锤子味道。比如在 J1 中，使用智能助手的方法之一是按住麦克风按钮说话，而在这里进行的语音转录几乎是 1:1 复刻「闪念胶囊」，更别提 J1 还提供了多种借助手机额外按钮激活的设置引导了。

J1 核心的操作方式是拖动按钮到对应的功能位置上，这种操作方式和 UI 设计也非常类似 Smartisan OS 锁屏界面的扇形菜单；至于笔记、对话等也几乎是完美复刻了 Smartisan OS。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDweaMYEiap3EzmfaG2bSNrROnbDz9yLJXO6HyibmdgbZAia6B9Z2WusmUpw/640?wx_fmt=webp&from=appmsg)
在功能方面，J1 也有着较为综合的表现。整个助手的核心是「Action」，也就是刚才提到的通过对话按钮告诉 J1 你想干什么，然后直接拖动到对应功能上即可。如果是比较单一的指令，比如发消息、搜索、与 ChatGPT 对话，则直接拖动到对应按钮上就能传递指令；但如果你要执行一系列任务比如「帮我找一个菜谱并记录到笔记本中然后明天提醒我打卡签到再给 Clyde 发消息说麦门永存」，那只需要把这一些列指令扔个 J1，它将会自动解析出来所有操作然后一步一步完成。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwPqvNwWDw58c34EZbZbkWuPyxc6GaGQic5Zn6uBic9L35NF6e0rhsXn4Q/640?wx_fmt=webp&from=appmsg)
除了这种指令对话，J1 的待办事项、笔记本、消息功能、聚合搜索功能都可以单独使用。其中笔记本里内置了 AI 扩写、翻译等功能，消息功能似乎是还魂了曾经的子弹短信，联系人必须都注册了 J1 才可以使用（是的，必须是 J1 中的好友才能使用消息功能），聚合搜索就非常简单粗暴了，输入搜索关键字然后点击对应的搜索分类，J1 将会同时打开好几个网页窗口给出搜索预览。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDw6LKDRpgiaL5crq9iacrdVgVm1NHvwkl3ox7upic0P7ibZlrhmCyLAE6J5w/640?wx_fmt=webp&from=appmsg)
一番体验下来，我最大的感触是 J1 确实是在用「助手」的理念去打造产品，虽然它的交互独树一帜，也带资进组配套了可以打通使用的代办和笔记本等，但与各手机厂商宣传「已经升级大模型」并且具有原生优势的语音助手相比，缺少了系统平台支撑的 J1 恐怕也难以让用户将它作为生产力工具使用。不过我认为 J1 原本就志不在此，也许这只是开发团队向外界传达的一种态度和概念。

另外需要注意的是，J1 支持中文语音识别，但限制了用户 IP 和注册手机号，官网表示目前支持的手机机型有限（实测似乎没有限制，建议自己测试一下），用户尝鲜有一定门槛，并且后续可能存在付费购买指令点数的可能。如果你都具备了这些条件，可以在其官方网站 matter.ai 下载体验。

**▍****Placify：地点打卡、旅游规划**

* 平台：iOS
* 关键词：行程记录、地图打卡

@ElijahLee：全新推出的 Placify 是专属地图和地点记录工具，与记录足迹不同的是，Placify 不通过 GPS 自动记录到访或计划到访的地点，而是通过图片导入、链接导入等多种方式来添加规划与记录地点。应用还提供了地点回忆、视图等功能。

首先是整理到访过的地点，Placify 支持在应用内的地图手动添加、搜索添加、照片导入、链接导入、共享菜单导入等多种方式。通过地图和搜索导入非常简单，手动点击地点即可，在出现的 + 号上点击即可标记为到访，并且可以上传照片作为附件记录。

值得一提的是，应用会自动按距离筛选出该地点附近拍摄的照片，以供迅速添加，我非常喜欢这个功能，可以让我免去在几千张照片中挑选的繁琐。对于各种类型的地点，Placify 还支持添加地点分类，并且提供了丰富的 emoji 符号以示区分。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwWywQ5odKVShyNFeRdBWIO7OJTBbsibVuasNH3tHYIgTOJbXtE7Kpwhg/640?wx_fmt=webp&from=appmsg)
从照片导入功能听起来非常便捷，只要照片带有 GPS 信息，Placify 会自动匹配与该 GPS 数据对应的地点，并自动标记。不过在实际使用上，该功能在中国大陆并不好用，一方面是因为火星地图的原因，照片的经纬度数据并不准确，另外对于商场等地点密集场所，GPS 对应的地点可能有很多个，往往会出现匹配错误的情况，而 Placify 目前还不支持地点修改。

从链接导入、共享菜单导入等方式，可以将社交媒体、地图应用中的地点导入至 Placify，目前支持 Google Map、Apple Map、Tiktok、Instagram、小红书等多种应用导入。既可以使用链接，也可以通过共享菜单分享导入。

Placify 的视图功能是针对旅行、分享等需求创新设计的功能。我们可以根据自己的需要创建一个地点合集，进行地点管理、旅行规划或者是回忆分享。应用提供了 3 个默认的视图，分别是全部地点、当日地点和 2024 年。

自定义创建视图也很简单，Placify 设计了智能视图功能，通过丰富的条件筛选并生成智能视图。这些条件包括国家、城市、时间、分类、是否带有照片等，并且这些条件支持「或」与「和」逻辑。对于特定的视图，点击分享按钮即可生成图片、地点文本、Instagram 快拍等多种格式的分享，并且可以选择是否带有 Emoji 符号、附带照片。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwj5ocUSXpZ1Dsn0icCqh0Ixib2I6lS74xHWLHny4s8d2xohWdmzUkEjmg/640?wx_fmt=webp&from=appmsg)
Placify 未来还会开发增加自定义路线、旅行模式、多人协作等功能，以增强旅行规划、行程记录等功能。应用可以在 App Store 免费下载，免费版只能标记 30 个地点、创建 1 个视图；内购订阅可以解锁 Pro 功能，无限制标记地点和无限制视图数量，费用为 12 元/月、98 元/年、128 元买断。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwgn97XykQePWL4nH4Kx7nYqrYbdecT6LqaFnlO0XZ8Elyjq7WAVkLqg/640?wx_fmt=png&from=appmsg)
**▍****ima.copliot：腾讯出品的 AI 知识库应用**

* 平台：macOS / Windows / 小程序
* 关键词：AI、知识库

@Vanilla：在 AI 如火如荼的当下，众多互联网大厂都端出了自己的 C 端产品，比如说字节的豆包、阿里巴巴的通义千问、百度的文心一言等，倒是腾讯显得有点沉寂。虽然说腾讯也早早地发力做了混元大模型，不过一直没有推出一款面向 C 端的基于大语言模型的应用。由腾讯混元大模型提供技术支持的 ima.copilot 的出现，打破了这一窘境，同时腾讯也另辟蹊径，选择了「知识库」作为切入口，既体现出了差异化，又直观地展示了产品的应用场景。

首先，ima.copliot 可以像其它的大模型应用一样进行连续上下文的问答。在回答问题的时候，除了答案正文，它还会显示信息来源、延伸阅读以及发散问题，这个模式和 Perplexity 很像，是我个人比较喜欢的一种展现方案。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwjjU4icQYSflMXwcLG74JlAwZDo9icrKvjibwXoGIhyxu15NByE3PuQ92g/640?wx_fmt=webp&from=appmsg)
![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwaptCX5ic73B9YzQgrjZvicsSGuJuDeK3qoiaEyYmeNwMp0HHxtJw5tGtQ/640?wx_fmt=webp&from=appmsg)
其次，ima.copliot 的核心功能 —— 知识库无处不在。我们可以把一次问答完整的上下文都添加到个人知识库，也可以把答案中的任意一条信息来源也添加到知识库。与此同时，当我们之后进行提问或者智能写作的时候，都可以直接调取自己知识库中的内容创作或者输出，形成了一个闭环。值得一提的是，ima.copliot 中的知识库还可以分享和收藏，也就是说一个好的知识库成为了一件流通的物品，可以为更多人所用。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDw1Cev26uDbT2zJW5GU5p82Uzb0rNaK5iarqdU6iaFduPTb9QbaTj2v3jw/640?wx_fmt=webp&from=appmsg)
![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwWGHH539uVIrFIw8MO3unWia7eKKSxv6GDfkJF364e1a7CWHjGDql3ng/640?wx_fmt=webp&from=appmsg)
最后，ima.copliot 还内置了一个笔记功能，我们可以直接将问答中的答案或者脑图一键添加到笔记中，方便我们整理或者二次创作。另外，不管是知识库中的内容还是笔记库中的内容，都可以通过「问问 ima」这个功能进行新一轮的提问，再次让内容进入循环。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDw6JXbDSia4fDOmiaXibfzIu5bZxHL00KKS0XNBnsicATHnUw98OEiaYSYgEg/640?wx_fmt=webp&from=appmsg)
![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDw5DmiaictujGzKWJoIgnAC4AM7CvaVeeiaRiaEH0LQnxJdfq5yoHFdue9iag/640?wx_fmt=webp&from=appmsg)
从产品逻辑上来说，我觉得 ima.copilot 已经相当完善，通过 AI 大模型技术打通了知识库工具中的各个环节，形成了知识获取、输出的闭环，如果再继续打磨一些细节，比如说移动端、浏览器插件、更多快捷键支持、第三方插件等，有很大的潜力成为一款成功的知识库应用。当然，这里有一个大前提，那就是 ima.copliot 目前仅在中文环境下表现良好，一旦我用英文来提问，虽然 ima.copliot 依旧可以给出回答，但是明显文不对题，而且也直接拒绝给出信息来源。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwDpr3iaQXic4ygUFFoicfAsbnjerNtSONHbSbcgGOtExW8Ad6ic7xo1lkvw/640?wx_fmt=webp&from=appmsg)
目前，ima.copliot 提供了 macOS、Windows 和微信小程序三个客户端，可以免费下载使用。

🔗 https://ima.qq.com/

**▍****Notion Faces：捏一个 Notion 风格的个人头像**

* 平台：Web
* 关键词：Notion、头像

@化学心情下2：提到 Notion 想必站内的各位派友早已经不再陌生，从最初的笔记类工具一路走来，Notion 已经逐步扩展成效率类工具平台，涵盖了笔记、建站、知识库、任务管理、日历等多种工具。其独特的设计美学也成为近些年互联网产品中最为另类的一个：简约的线条、单一的配色，如同素描般的设计界面却又透露出精致，让用者爱不释手。

只不过一直以来 Notion 的设计规范主要是应用在自家的产品中，当然也有喜欢其独特风格的开发者开发了第三方工具，可以让我们基于 Notion 的风格来制作头像，只不过这些工具都并非「原版」。好在 Notion 或许也看到了用户的呼声，终于上线了自家「捏脸」工具——Notion Faces。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDw5T39l708l8C3Le0CqK1IM7SuxfCVzXzLiaBVA5okppt0qZlmiaH5XJEg/640?wx_fmt=webp&from=appmsg)
比起那些基于 AI 生成的头像工具，Notion 主打的还是极简黑白的 icon 设计。打开设计工具后，Notion Faces 会先随机生成一个头像，你可以基于这个头像调整肤色、眼睛、眉毛、眼镜、鼻、嘴、头发、配饰等，当然如果你想要从头开始「捏脸」，也可以点击「新建脸谱」来从头创造。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwW2kicdp9nFlzxE2z5FWCRia0pwaBS69GMJ4XZLYDTkqjevzsVmUGGgCA/640?wx_fmt=webp&from=appmsg)
如果你觉得设计的不是那么的好，也可以点击「返回上一个图层」或者「清除图层」重新设计。点击保存后你可以在自定义你的脸谱中设置「标题」「说明文字」以及「背景」，然后就可以下载或者分享你设计的头像了。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwSGpFosjPia2gFnOvSzBEdspwokKkDLDuM2pGLEu2ET9FCAclAnzEuHA/640?wx_fmt=webp&from=appmsg)
在「下载并分享」页面中，我们可以下载前面设置的带有说明文字的头像，也可以仅仅只下载面部。我个人觉得很贴心的是 Notion 提供了包含背景和无背景的下载项，可以在不同的场景下使用。此外在该页面也可以看到其它 Notion 用户的脸谱设计以及将脸谱添加到你自己 Notion 的个人账户中。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwDJRKiaaPQIwae4UriamYSx3le5kofXB651SohEw2EN586moicnIERSBCQ/640?wx_fmt=webp&from=appmsg)
如果你和我一样非常喜欢 Notion 的设计风格并希望以此设计自己的个人头像，免费的 Notion Faces 不妨试试，你可以访问其官方网站免费使用。

🔗 https://faces.notion.com/

**▍****懒相机：易用又有趣的复古相机应用**

* 平台：Android
* 关键词：复古相机

@Noah\_Choi：印象里，自从智能手机开始注重相机性能开始，各类拍照滤镜类 App 便层出不穷，尤其是复古相机类型的产品，极大满足了人们的「文艺感」需求。不过随着各厂商把相机硬件卷到极致、把计算摄影调教到比我脑袋还聪明之后，这些 App 似乎不再那样热门了。这些 App 是真的消失了吗？

其实并没有，虽然它们不再像以前那样热门，但仍有开发者试图在数字时代还原胶片相机的美感。比如这款在 Android 平台上架的「懒相机」。App 的整体使用思路和以往的同类产品比较相似，大家上手会比较容易。而且它也吧主界面复刻成了胶片相机，通过虚拟旋钮、拨杆来实现各项功能。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwoHJSJ7U9vUFlrZ8zRpgTcBDxXx9l8VcrYzkV4ygMSf8dSvicibVMibsog/640?wx_fmt=webp&from=appmsg)
比如右上角的拨杆可以控制对应旋钮的功能是缩放还是调整曝光、旁边那个好似胶卷扳手的地方则是控制闪光灯开关和倒计时，我们在使用这些按钮时还会伴随着震动，确实有了一种回到 90 年代的感觉。

懒相机也是以「胶卷」的形式给相片套用各色滤镜。目前 App 内已经预置了 12 款滤镜，而且每一款滤镜都可以在管理界面单独调整滤镜对应的亮度、高光、阴影等参数，可以让用户调整出属于自己的一套滤镜风格。除此之外，上下滑动右下角的方形多功能按钮可以切换不同设置项，能够调整相机外观、音效、相册管理等功能。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwNpdMtNmwJOwGNlA6sQ1agiaf0U1Qg29XHxDecEgPM6psuJC6NJZlhbA/640?wx_fmt=webp&from=appmsg)
你可以在酷安下载懒相机。应用主要功能免费使用，喜欢的用户也可以订阅高级权益解锁 WebDAV 同步、无限制预设导入以及后续可能的升级功能。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwQs8BKlZBTtpUicf8OvS6O0dCKjHbArpeSpCHgzZeibicctNwQBpDMYhUg/640?wx_fmt=png&from=appmsg)
#### **🆕**

#### **不容错过的 App 更新**

除了「新鲜」App，App Store 中的许多老面孔也在不断迭代、更新，增加更多有趣和实用的功能。少数派希望帮你筛选 App Store 中值得关注的 App 动态，让你快速了解 App 和开发者们的最新动态。

**▍****Trakt 2.3：自动追踪流媒体观影记录**

* 平台：iOS / iPadOS / Android / Web
* 关键词：追剧、观影记录

@Snow：Trakt 对主流媒体库如 Emby、Plex 或者 Jellyfin 的观影记录同步可谓是手到擒来，你可以借助插件支持，也可以依赖 Infuse 这类媒体库管理应用实现，只是到了流媒体平台这边 Trakt 就瞬间「摸瞎」，基本只能依靠手动标注。上个月，2.3 版的更新让 Trakt 可以理直气壮地跟用户宣布「大人，时代变了」，新增的 Streaming Scrobbler 让 Trakt 自动同步流媒体的观影记录和评分成为可能。

打开 iOS 或 Android 平台最新版 Trakt，你可以在「个人资料-设置」中找到 Streaming Scrobbler，应用依靠 Younify SDK，支持自动同步 Netflix、Hulu、Prime Video、HBO Max 以及 Apple TV+ 这五个流媒体平台。点击 Sign In 登录对应平台账号，确认授权后即可实现观影记录和评分的数据同步。对于 Netflix、Prime Video 这类支持多用户的账号，Trakt 则可以挑选单一用户同步，不会让其它用户的行为污染你的数据。此外，Trakt 会在同步时跳过原本已标记的项目，避免因同步功能而导致数据混乱。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwlkPIYuYHXxqZHV6467gCT17R94mLiaU7keWcmCn7RFTaTCAYcwa92tQ/640?wx_fmt=webp&from=appmsg)
Streaming Scrobbler 目前并不支持实时同步，在首次获取授权同步初始数据后，后续数据将每 24 小时同步一次。所有同步数据都会列在 Scrobbler Settings 网页上，你可以在这里手动撤销任一数据，或者快速解绑同步服务。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwibBublyrfnTVQLLYfgb5KL5yar2U0Vm3wtMLxGNGam0uf1wPlobXDgg/640?wx_fmt=webp&from=appmsg)
Streaming Scrobbler 需 VIP 服务支持，你可以按 38 元/月 或 398 元/年订阅使用。应用提供了 1 个月的免费试用期，考虑到 Streaming Scrobbler 功能目前还处于测试期，我就遇到了无法获取 Apple TV+ 数据的问题，建议你可以在试用后再行决定是否付费。

你可以在 App Store 免费下载 Trakt。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwibhd4tBeMW5uHiaIcwichYActibRCYT6WQJKCcRia0ZsNcmNiazSPJYDiaN5Q/640?wx_fmt=png&from=appmsg)
#### **📣**

#### **App 速报**

* Mozilla Firefox（Windows | Linux | macOS）：更新至 v134，新版针对 Linux 平台加入了触摸板 Hold 手势，针对 Windows 平台新增 HEVC 硬件解码，提升特定视频格式的播放效率，降低 CPU 占用等。

原文链接：

https://sspai.com/post/95588?utm\_source=wechat&utm\_medium=social

责编：waychane

****本文由多位作者联合撰写****

**主作者**

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRjP13BqRImicDe2d9iaz7Vk2ln0Vib7arS8PZpNm6ojmlLgoZuribiaWN1GXW291cdNSSwNxvfno0DWaaQ/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
少数派编辑部

**联合作者**

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/oylw20gGnRjP13BqRImicDe2d9iaz7Vk2l8RHScsmXTDKkOC2Ls5f6PoluvnTleLEPJoQe5ialfyhRicibTibwt8jPwA/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
Snow

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/oylw20gGnRjP13BqRImicDe2d9iaz7Vk2lSic6Hia3kVQdIbpzibI3NFme24krJ9YbeLSen2lwuJZDXp8PNDLh6rzOA/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
Vanilla

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRjP13BqRImicDe2d9iaz7Vk2lfhIWO5fcbGPGw5LicQULjYzZsHUEVnp852jt3wajInna7mfYEOJMA5w/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
Noah\_Choi

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRjP13BqRImicDe2d9iaz7Vk2lpflmffVPnTSISXhXoKxvmKL1heEqM9ZmibQYtFmbL2CJXI2icHeoOONA/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
化学心情下2

![Image](https://mmbiz.qpic.cn/sz_mmbiz_jpg/oylw20gGnRjVv0KG46UI6otHqDZblewOggZK3p04ZjZcgfhlWD7gVM3s7ZP2BicZ9PSrEhQFoubKFJeUVLkzvXA/640?wx_fmt=jpeg&from=appmsg)
ElijahLee

******/** **更多热门文章** **/******

[![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDw6IL0YNP2s3san9eAxMiaeJMjsOzTRW0qo8fGByL73QlgofOx1UUXHFQ/640?wx_fmt=png&from=appmsg)](https://mp.weixin.qq.com/s?__biz=MzU4Mjg3MDAyMQ==&mid=2247587071&idx=1&sn=2859c6dbe4b0f76827153135d355dd57&scene=21#wechat_redirect)

[![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/oylw20gGnRiaiaPJMU9b5icnNL9ztIQ6icDwbSsohmnoQMnr1hjgHibt6eL9yQ4G21dJbp7WCQmfouC7CSunQrmZ0LQ/640?wx_fmt=png&from=appmsg)](https://mp.weixin.qq.com/s?__biz=MzU4Mjg3MDAyMQ==&mid=2247586310&idx=1&sn=2135a4a8816250412019011ba55cb46d&scene=21#wechat_redirect)

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/oylw20gGnRia0gia2XRjDMyUibKnCy2K0x54Bib33FrlL2EuuBN3j1JU6EwcpdUY81WF9oVNhLDM0H8vFOjQ2Qfn7w/640?wx_fmt=png)
![Image](https://mmbiz.qpic.cn/sz_mmbiz_gif/oylw20gGnRia5eQ4a4yV4Qpwj5xOiaaKKiclUP2GvCckKna5gukbFmkUQpoVjdekXiawhY0jiaZALA2nYCQwzP50kfg/640?wx_fmt=gif)
