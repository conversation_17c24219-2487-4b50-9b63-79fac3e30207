---
人员: 
  - "[[刘飞Lufy]]"
tags:
  - articles
日期: 2024-06-05
时间: 2024-06-07 03:38:01.135441+00:00
链接: https://mp.weixin.qq.com/s/2ctGJEtnwj83iUo0XfNoFg
附件: https://mmbiz.qpic.cn/mmbiz_jpg/bucsruJVwLjRNOPe3E5fMEn8EV4xiamrFVYrEic7oDamVqSgrn357AnTkD0ybXsvV9dd5AZcibL1j0c6gMic6SN4bQ/0?wx_fmt=jpeg)
---
## Document Note

## Summary

The author argues that Alibaba lacks true product-focused employees like product managers who prioritize user needs. The text emphasizes the importance of focusing on user needs and product development over sales strategies for long-term success. It suggests that a company's success hinges on prioritizing user experience and product innovation rather than solely focusing on sales.

## Full Document
![[Attachments/e521ad842ffd0fb97875b8df34e5156d_MD5.webp]]
阿里没有产品，这个观点是我私下会跟一些朋友交流的。后来观点上做了一些打磨，今天算是做一次完整的表述，望有启发。

**1 什么是产品**

我接下来提到的产品，是指产品经理。而且我的理解里，不存在什么数据产品经理，中台产品经理，商业化产品经理。产品经理就是做产品的，解决用户问题的。不是这样的人，也许是分析师，也许是调研员，总之不会是产品经理。

阿里是没有真正的产品经理的。

当然，我们从实际的表现看，阿里有很多 APP。淘宝 APP 是产品，也有界面设计，有账户体系，有复杂的交互，也有推荐策略。单就这些产品功能而言，做出它们，必然是有人参与的，这些人在 title 上也标的都是产品经理。似乎与微信、抖音的产品团队别无二致。

实际上很不一样。

产品经理是应该面向用户做事情的，理解用户的画像，思考用户的场景，解决用户的需求。这一点亘古不变，不是随着时代发展或者某些人的意志转移而变化的。

1889 年，法国商人爱德华决定改造轮胎，道理很简单，作为乘客，驾驶马车实在难受。那时候的轮胎都是实心的，颠簸到崩溃，也没有什么耐用性可言，走一段时间轮胎就烂掉了。爱德华迭代并推广了充气轮胎，成为世界轮胎巨鳄米其林的创始人。这家 130 年的公司迄今屹立不倒。

1989 年，加拿大登山爱好者戴夫·莱恩，认为他能买到的装备都不够好用，决定自己做，公司名字叫磐石（Rock Solid）代表像石头一样坚硬耐用。它们的产品解决了户外爱好者的刚需，衣服透气防水也保温，还非常耐用，背带等装备也更坚韧、耐用。几年后，公司改名始祖鸟，成为户外运动的先驱。

回顾过去 20 年互联网的飞速发展，并不单纯是技术带来的革新，而是技术之上的产品带来的革新。智能手机的信息通讯功能，以及移动支付，让生活更加便利。让今天这些巨头公司生存的，不是他们的代码写得多漂亮，不是他们的销售多出色，根本还是解决了用户的问题，让生活更舒适了。

区块链技术可能是过去二十年最伟大的技术之一，但单纯的技术并不能解决用户的日常问题。它不能因为自己的伟大，就成为好的产品。反而在很多人手里，成了割韭菜的工具。

过去互联网公司发展太快了，迭代太猛了，烧钱也太狠了，对用户需求的关注，被掩盖到大众话题背后了。很多人在问的是一个大厂的职级能赚多少钱，在问的是中概股的股票能怎么涨，在问的是高管们的权力斗争进度几何。讨论产品和需求甚至像是过时的事情了。

但是在浪潮退去的时候，产品和需求反而又能显现出其亘古未变的威力。

为什么不会刷淘宝首页了？因为小红书更好逛。就这么简单。

为什么不在淘宝上买东西了？因为拼多多更便宜。就这么简单。

为什么不愿意参加各种购物节了？因为太麻烦。就这么简单。

这些简单的东西，但凡是产品经理，不需要有什么天赋，只要他对用户需求有感知，很容易得到结论。可惜的就是，阿里没有产品。充其量算是有功能设计师和交互设计师。他们是为了销售目标服务的，不是为了用户服务的。

在一个不会关心用户，不曾思考产品的大组织里，讨论什么是「更好逛」「更便宜」和「太麻烦」，是很困难的。就像勾股定理，是数学领域的课题，你却想通过牛顿和爱因斯坦解决这个课题，哪怕成了量子力学的大师，也是无能为力。而研究数学的话，很轻松就能证明。

**2 什么是销售**

我的第二个暴论就是，阿里全部都是销售。

一个卖咸菜的小店，老板很懂得腌咸菜，也很懂得用户需求。当地人喜欢辣，但不喜欢重辣，他总能调试到很稳定的辣度。当地人喜欢的香料风格，他也熟悉。他会关注用户口味的变化，比如大家开始追求健康了，他就减少了添加剂，主打有机绿色。这个小店，就能这么持续经营下去。

这是产品逻辑。

相对的，这个卖咸菜的小店老板，有个去商学院毕业回来的大侄子，说我来给你改造一下。你需要做很多工作，先去地方电视台和自媒体做投放，吸引更多客流，再让这些人都转化过来，买你的试吃装。买完后，给发优惠券，还要短信轰炸、召回提醒。当地有什么市集，有什么商场活动，全部参加。然后开分店，做分销，铺量，产量加满，最后去郊区买个工厂，流水线生产。

这就是销售逻辑。

我家楼下一个熟悉顾客的调酒师、水果店老板和健身房教练，都比在大厂园区里摆按钮、改文案的产品经理，更像真正的产品经理。

我没有半点说销售逻辑不好的意思，这些做法本身都没有问题。全球最顶级的商业品牌，销售也都是主力。销售是公司经营的正常组成部分，酒香也怕巷子深，没错的。

不过，一旦一个公司的掌舵人，想的也全都是销售，那整个公司就会完全以销售逻辑作为第一性原理，那产品就变成不重要的事情了。这个小店没有人思考咸菜该怎么做了。

这是件很吊诡的事情，却是在真实发生的：如果你约见阿里电商的任何一个人，不限于产品经理，包括程序员、设计师、运营、商务、行政等等，聊起电商，聊的将全部围绕这些关键词：增长、转化、优惠券、购物节、投流、供应链、库存、出海、ROI、GMV 等等。他会跟你聊今天跟拼多多和京东比的竞争优势和策略打法，会聊黄峥的精准判断和拼多多对商家的爱恨情仇，会聊双十一的落寞与逍遥子的遗憾，会聊直播行业带来的影响和货架电商的关系。

唯独不会聊的，是用户。

诚然，在聊天过程中，多多少少会出现这样一些关键词：用户在线时长，用户转化率、用户生命周期价值（LTV）、用户增长、用户浏览量......

这些句句写着用户，但句句不是用户，句句写的都是把用户卖钱。就像句句仁义道德，但句句其实是那啥。

考虑用户，和考虑把用户卖给客户，是完全不同的两件事。虚心询问用户需要什么，满足他们，跟在高档餐厅和客户觥筹交错，聊哪些用户身上好赚钱，是完全不同的两件事。

不是翻开商业宝典和增长大全，研究怎么「运营用户」「转化用户」「教育用户」。是做个简单的判断：现在的用户，到底喜欢甜，还是喜欢咸？

**3 什么是全员销售体制**

从商业史来看，一个企业的发展都是螺旋上升的，在每个技术带来的变化阶段，都有一次起死回生的机会。能存活下来的企业，都不是完全以销售为导向的，而是有一个独裁的、强权的、有控制力的老板或者领导层，决心赌一把，赌一个有不确定性的方向，赌一个用户会有需求的方向。

IBM 做打孔机几十年，决定转型电子计算机。大型计算机已处于垄断地位时，决心投入做个人电脑。再之后，做云计算和 IT 服务。每一步都差点被彻底杀死，还是或主动或被动在产品上做了自己的路径选择，在当年都是充满风险的。

米其林在 1930 年代大规模裁员，公司从上千人一路裁到没几个人。后来米其林的家族继承人坦诚地说，当年米其林就是在产品上没有任何创新迭代了，不再被用户信任了。此后，米其林遇到过波折，但从来都是产品引领者。自行车时代，米其林是马车的推动者；马车时代，米其林是汽车的推动者；汽车时代，米其林又布局了土木工程车和航空轮胎。

如果按照销售的思路，既然多花点精力在发券上能赚更多钱，为什么要把精力放在产品研发呢？

这里要注意，对产品研发有投入，跟是否围绕产品做出决策，完全是两码事。有投入可能只是行业惯例，真的尊重这些投入、运用这些投入，才更关键。事关用户体验的功能，十几年都不变，举办多少技术大会、投放多少产品软文，都没有意义。

要评判一家公司是面向用户的产品逻辑，还是面向赚钱的销售逻辑，有个简便的方法，就是看一把手在做什么。他一天的时间里，多少在思考下一代的产品，多少在跟客户开会？又有多少在跟用户聊天，有多少在给下属做教育，或者享受下属的糖衣汇报？

一把手都不关心的事，高管们为什么关心？高管们不关心的事，下属们为什么关心？

有一种理论是，术业有专攻，老板只需要关心管理，剩下的各干各的。这属于扯淡了。真正在大组织待过的就知道，领导层的意志，就是每个人手头的工作。没有意志，就没有工作。

管理上，一把手不关心的部分，还有种美化方法，就是 KPI 拆分。电商是最容易以数字衡量产出的，交易额和利润率清晰可见，组织看似就比别的公司好管理了。但这也是最危险的地方。

假产品经理们，不会想用户怎么想，而是只会在页面里塞牛皮癣，让更多人下单，因为这样能带来销售。他们是销售设计师，不是用户设计师。程序员们，会看产品下菜碟，有销售转化的项目，才会参与。设计师、运营无不如此。HR 也会考虑，这个人来了公司，能不能让 GMV 再涨一涨，部门奖金包多了，我这个政委也与有荣焉。

这种贯彻到全员的思考模式，就完全复现了前文说的大侄子卖咸菜的情景。每个人看起来都在忙活自己的专业领域：产品、技术、运营、市场、人力、行政......实则每个人都在忙活同一件事，就是：多赚钱。

**4 为什么左右为难**

这样以来，很多表面现象就很好解释了。比如阿里面对的两个对手：拼多多和小红书。

阿里电商是有主观意志，故意舍弃了中小商家吗？这是一种战略失误吗？我理解并不是。这是一种自然流动的结果，水渠在这，水只能这么流。

服务一个大的 KA 商家，在销售效果上，可能等同于服务 100 个甚至 1000 个小商家。那何必吃这个苦呢？在组织内，愿意坚持服务小商家的人，将失去话语权。KA 商家的产品和运营（实际上都是销售）将升职加薪，掌握资源。

我过去常吐槽的一些小细节，比如难用又垃圾的地址管理 20 年都没改过了。真的没有（假）产品经理关心这些用户体验吗？必然也是有的。跟水平没有任何关系，跟产品经理的方法论也没有任何关系。只是没有技术愿意配合，这件事情没有收益，为什么要配合？不为了自己涨工资，难道为了对用户纯粹的爱吗？

拼多多坚持低价，在全员销售的体制来说，是一件非常非常难做的决策。第一，它在当时无法证实或者证伪，正如黄峥说的，五环外的人群没人关心，但究竟这些人能否上网购物，要打一个大问号。既然现在的咸菜好卖，为啥要换品种。

第二，低价策略会大大得罪商家，尤其是大商家和中商家。既然现在的合作伙伴都挺开心的，每年躺着赚钱，为啥要做低价。

拼多多在低价方面的策略是复杂的，不仅涉及平台内的管理，还有对品类详细的价格监控逻辑，能全网找出某个精准品类下的低价区间。这种能力也是需要成本的，没有公司战略层面的资源支持，难以维系。

拼多多的单兵作战能力很强，不代表阿里的单兵就不强。更多是拼多多有一致的方向，做低价就是做低价，做仅退款就是做仅退款，指哪打哪的前提，是一把手做了决策，他认为有的用户就是需要低价。而阿里的表面一把手，还要跟真正的合伙人们汇报业绩，汇报数字，谁都不敢得罪，谁都不能得罪。他是个大销售，他并不是真正的一把手。

另外一个为难的地方，就是小红书。淘系内部很早就把小红书和抖音列为竞争对手了。抖音是视频，淘宝也抄不动，且不多提。小红书是图文，种草功能显著，跟淘宝首页的推荐信息流构成直接竞争。阿里为什么也很难呢？

没有耐心养创作者、没有长期战略做内容，也许都是对的解读。不过背后也有同样的逻辑：全员销售的状态下，无法孵化出小红书。

在销售的逻辑里，并不是理解内容的人和理解需求的人做决策，做内容产品，就会变成拆解 KPI。拿引入内容创作者举例，只会有两种情况：成本开销低的团队脱颖而出，在同样的内容数据上，花费更少，性价比很高，实则引入的都是劣质内容，把良币都驱逐了；对于名声在外的好创作者，花重金纳入，然后杀鸡取卵，迅速榨干。

越是需要跟另外的销售团队竞争，就越是容易杀鸡取卵，杀取的动作越来越早，杀得越来越勤。内容还没起量，广告已经贴满了。

后来招了很多腾讯的产品经理、小红书的产品经理、B 站的产品经理，都完全无法解决这些问题。因为这些产品经理，来到体制内，留不下来的，就匆匆离开，留下来的，一致成为销售。

无法验证的需求，在内部无法得到重视；无法证明有 KPI 贡献的项目，在内部无法获取资源。无法实验，就更没有证据。没有证据，就更无法实验。这就是销售体制的悖论。

**5 写在最后**

我特别钦佩的，在米其林掌舵了 44 年的弗朗索瓦·米其林说过这样一句话：

![[Attachments/2b5d4c0f6c888f159685a33e784565a4_MD5.png]]
真正关心用户体验的行为，并不是在公开信里说一说，并不是在价值观里写一写，而是让关心用户的产品经理，有话语权，让公司的每个人，都思考用户需要什么，让关心用户落在实际的 KPI 和绩效奖金里。

我不是单纯的阿里黑，我是一个挺盼着阿里好的阿里黑。毕竟阿里好，杭州的房价就好。

阿里在很多层面，的确是很了不起的公司，能把线上交易做得如此高效，能够长期维系了商家的关系，能改变我们的消费习惯，有超强的执行力和销售水准。这些都不否认。

只是，我认为阿里最大的问题就是，这依然是一家全员销售的公司，对产品并不在意，也对用户并不尊重。它有传统浙江商人出色的一面，口吐莲花，脚踏实地，尊重客户，算钱精细。也有很多浙江商人的另一面，不琢磨产品，不尊重用户，容易骄傲，强烈自信。

有人可能会想象一个场景：阿里坐在战情室里，面对拼多多的阻击，带队熬夜做了分析，最后得出了错误的结论，导致错失一座城池。

真实场景肯定不是这样。更可能的是：阿里躺在摇椅上闭目养神，旁边印钞机哗哗出钱，完全两耳不闻窗外事。被人掐了一下，才醒过来。先看了眼印钞机，还在，那就放心了。

但愿阿里有一天能不再躺在摇椅上，只关心印钞机，而是站起身来，出去看看，用户到底喜欢甜的，还是喜欢咸的。

题图由 Midjourney 绘制。Prompt: an image of metal building in the fog, in the style of the stars art group (xing xing), new sculpture, shwedoff, iconic album covers, zhichao cai, minimalist stage designs --ar 16:9 --v 6.0
