---
人员: 
  - "[[知乎专栏]]"
tags:
  - articles
日期: 2021-05-22
时间: None
链接: https://zhuanlan.zhihu.com/p/374481787
附件: https://static.zhihu.com/heifetz/assets/apple-touch-icon-152.a53ae37b.png)
---
## Document Note

## Summary

原文链接PKM闭环中有一个很重要的环节就是信息输入，包括各种信息来源，例如微信公众号、博客、知乎、RSS等等，因此也就诞生了一大堆稍后读软件，如何真正有效的获取输入而不是做一只仓鼠是需要思考的。最近看了《…

## Full Document
PKM闭环中有一个很重要的环节就是信息输入，包括各种信息来源，例如微信公众号、博客、知乎、RSS等等，因此也就诞生了一大堆稍后读软件，如何真正有效的获取输入而不是做一只仓鼠是需要思考的。最近看了《小强升职记》很有收获，里面提到最好在每个方面都有一个自己的小系统，“**做事要靠系统而不是靠感觉**”，因此这里简单记录下自己的思考和目前构建起来的信息收集整理系统。

#### **需求场景**

手机在浏览阅读一些比较有价值的文章的时候，无法立马吸收，例如读到一些专业的技术博客、长文等等，此时，就需要将这些内容收藏起来有时间的时候继续深入阅读。

##### **为什么手机不适合进行深度阅读？**

1. 干扰太多。比如阅读时候弹出微信消息、短信等等，此时跳到微信再回来已经过去了半个小时，更别提现在微信公众号已经占据我信息来源的50%以上，这种打断就显得尤为浪费时间。
2. 没有大块时间。一般在排队、等车等情况下进行阅读，可能只能判断出这篇文章是否需要进一步学习。
3. 不适合标注笔记。这个也很好理解，手机毕竟屏幕太小，但很多专业性很强的文章如果不配合笔记甚至实操，是根本无法吸收内化的。

手机的使用场景决定了它本身只能进行一些文章价值的快速筛选，辨别出这篇文章是否需要进一步深入学习，仅此而已。

所以我给自己定的一个小目标就是每天的手机使用时间不要超过3小时，微信、知乎中发现的有价值的内容都第一时间收藏到稍后读中，电脑上统一进行整理，手机只作为一个收集器和过滤器，用于快速识别出哪些文章需要进一步研究。

##### **关于碎片化时间的思考**

关于这点，收到了简悦作者的部分观点启发。

个人观点：不要有太多碎片化时间，利用碎片化阅读本身就是个伪命题，原因上面也有讲到。

很多人恨不得自己24小时都在进行阅读，可能是为了造成一种自己很努力错觉，也有可能是因为现在这个时代知识焦虑实在被贩卖的太严重，如果不让自己时刻在学习在阅读就会被淘汰？实际上这样做并不会对个人提升有太大帮助。很多人(包括我)都会利用走路、吃饭时间进行手机阅读，这种只能用来看看新闻，我理解是无法进行自我提升的。

那有些人说就是会有很多碎片化时间应该如何处理呢？比如通勤时间较长，或者一些工作性质导致会有很多碎片化时间。

我最近的感受下来，这种小块时间宁可放空，进行思考和回顾。拿我自己来说，之前每次上下班开车的时候总是喜欢听一点书、或者听一些专业相关的教程，例如喜马拉雅、极客时间、得到等，实际上听完一遍，根本没有吸收，最多只能做到，知道有这么本书，大概讲的是个什么东西，但具体到如何讲的一概不知。最近这段时间见尝试放空自己，开车就好好开车，什么也不听，发现经常会有各种灵感从脑袋里面冒出来，比如对工作的回顾、对自己日常的思考等等（同时也会让我更加专注开车，更加安全）。大家也可以尝试一下，定期放空回顾自己说不定会让你走的更快更远，PS：这里也可以学习一下如何进行冥想，我自己也在尝试。

#### **工具挑选**

##### **工具选择的原则**

知道了需求背景，我们就开始真正开始进行软件挑选了，经过我这么多年的软件使用实践，我发现大多数时候其实我们并不知道自己需要什么软件，外加现在这个时代，开发一个软件成本实在有点低，一旦有个热点，同类型产品几乎是雨后春笋般冒出来（参考最近大火的Roam类双向链接笔记）。如果不建立一个自己的筛选甄别系统，很容易就被各种工具迷花了眼。

这里我简单列举一下我自己这些年总结的一些选择软件的原则，其中很多在我的[旧文](https://link.zhihu.com/?target=http%3A//blog.samwei12.cn/2020/05/02/Thinking/%25E5%25A6%2582%25E4%25BD%2595%25E6%259E%2584%25E5%25BB%25BA%25E4%25B8%2580%25E4%25B8%25AAPKM%25E7%25B3%25BB%25E7%25BB%259F/)中都有提到，仅供大家参考：

1. 尽可能不要全部依赖免费工具。
2. 格式越简单越好。深入表述一点就是软件的 Lock-in 成本越低越好，目前这个时代，软件更迭速度实在快的惊人，大家可以仔细想一下有哪些软件使用时间超过了5年？或者说3年？所以构建自己的个人系统时，尽可能不要强依赖软件，做到快速迁入迁出最好了。这里不得不提到国产很多软件，真的是没有什么节操，导入功能支持的完美的一笔，导出功能就是个渣渣，就差明说我要抢你们的用户，但你们谁也抢不走我的用户。开发者真的是多没自信才能干出这种事情？
3. 除非是网络实在不可用，否则尽可能选用国外产品。 这一点原文已经提到了，不过说实话，这两年国内产品质量越来越高了，很多软件在使用体验上已经超越了国外软件。如果遇到比较良心比较有节操的开发者，这条原则可以作废了。
4. 你一定要明确你的需求。这点上面有提到过，如果你不明白你真的需要什么，那也一定要清楚自己不需要什么。只有这样，你才能够从不停地切换软件中挣脱出来，避免成功“工具人”（字面意思，被工具奴役）。

##### **收集类软件需求分析**

对于我来说稍后读软件方面个人的诉求点包括：

1. 手机端随时能够快速保存。尤其是微信公众号文章，很多稍后读软件在微信公众号文章上面体验都很差
2. 能够跨平台使用。多平台的支持很重要，我平时使用网页版很少，喜欢使用客户端，一般是iOS跟Mac使用
3. 能够持久化保存，网站无法访问也能正常使用。这点相比大家也都明白，网站不能打开的因素实在是太多了
4. 能够进行标注，并且能导出标注并进行检索关联。阅读过程中对于精华部分进行标注是一个很必要的功能，且需要能够对其进行分类打标，使其成为素材笔记(卡片盒笔记中的术语，字面意思)，并且能够跟现有的知识进行结合。你学到的每个知识点，只有跟现有的知识点进行关联，才有可能真的内化吸收变为你自己的知识，一个个的孤岛知识点是没有任何意义的。

##### **我用过的那些收集类产品测评**

搞明白为什么需要一个信息收集系统跟它需要具备哪些能力之后，我们就可以开始进行一些工具的筛选和鉴别了。目前笔记软件和稍后读软件实在是太多了，很容易就挑花了眼，我这里只简单介绍一下我自己深度使用过的一些软件和工具，当然我自己在使用过程中也并不是一下子就想清楚需要哪些能力再开始进行挑选的，也是逐步使用过程中发现哪些适合自己哪些不适合自己，因此现在把使用过程中的一些经验简单说一下，希望能减少大家的时间成本，同时由于每个人情况不同，需求也不一样，这方面主观因素比较强，最好是梳理出你自己的需求点，建立最适合你的小系统。

这里我把目前用过的工具简单分为两类，一类是笔记类，一类是收藏类，他们的定位有所区别。笔记类，偏向剪藏、管理、检索、写作；收集类，偏向快速收集、标注、阅读。

##### **笔记类**

##### **印象笔记**

老牌仓鼠专用笔记，只收藏不老牌仓鼠专用笔记，只收藏不处理。我从11年左右就开始在用印象笔记，当时还没有国内版，同步速度比较一般，不过真的是稳，最强大的就是剪藏功能，能够完整保存网页内容，选区可以调整，不过一般来说自动识别完全够用；其次是搜索功能，甚至能利用OCR，搜索图片内容。

优点：

* 剪藏功能简直逆天，仓鼠党必备
* 搜索功能很强大
* 稳定性很强

缺点：

* 大公司通病，迭代速度过于缓慢，一个Markdown支持好几年都没上，很长一段时间只能用 马克飞象 等替代；最近一两年有所好转，从推出超级笔记开始，功能迭代速度明显变快，前几天甚至出了双向链接，不过有点为时已晚的感觉，毕竟现在笔记市场已经不是几年前了
* 在现在双链笔记横行的时代，印象笔记之前的笔记模式已经过于落伍，缺失了收藏到输出中间最重要的一个组织整理功能，也就无法使知识内化，逐步沦为一个收藏仓库，永远也不会打开。当然不排除有些人用印象笔记也用的很棒，但那需要个人比较强的组织管理知识的能力，一般人达不到
* 剪藏过慢，这可能跟实现方式有关，印象笔记需要等完整网页内容转换保存下来之后再提示成功，一般的微信文章大约需要3-5秒，有点接受不了。完全可以改为后台通过链接下载创建笔记，用户只需要粘贴链接即可离开，不需要在界面停留等待。
* **微信公众号支持不佳**，现在很多软件都是通过提供服务号来实现微信文章收藏，我只想问问他们实际使用过这个功能吗？槽点实在太多，实际收藏场景通常在用户浏览公众号文章列表时候发现这篇文章不错，想要收藏一下，此时需要：右上角拷贝链接，退出公众号，打开服务号，粘贴链接，返回公众号继续浏览下一篇文章。不得不说这实在是个迷幻操作，这中间的打断次数多的我都不想继续使用了。当然有一些办法可以简单规避这个问题，例如使用iOS捷径，但实际体验过之后槽点也真的够多。首先，使用捷径创建笔记时候需要进行授权，这个没啥可说的，但骚就骚在这里有个严重bug，即使授权完成，在桌面的快捷方式中点击收藏，一样会频繁提示授权失败，最终解决方案，**重启手机**；其次，官方那个捷径有点蛋疼，每次收藏完笔记，会自动打开该链接，也就是跳到印象笔记手机端该笔记中，这是为了手机端打开次数和时长KPI？解决方案，删除捷径中这些多余步骤；最后就是收藏速度了，经常有的文章我收藏一下，要等待大概10-15秒，不确定是不是文章里面有大量图片之类的，可以参考上一点，这里还有个恶心的点就是捷径本身都是同步的，也就是说这15秒时间内，你只能在桌面傻等着，一旦有了其他点击事件，就会取消掉本次操作，每天浪费大量时间。
* 公司网络屏蔽，无法使用，这个是我个人原因

##### **为知笔记**

大概在15、16年左右，由于实在受不了印象笔记对于Markdown的支持力度，同时不愿意进行太多付费，转而开始使用为知笔记，但不得不说，使用体验比起印象笔记简直是天上地下，服务响应不及时、开发进度缓慢、客户端经常卡死等问题一点点再消耗我的耐心，终于还是舍弃了它。

编写本文的时候我还专门去下载了为知笔记，发现网上能够搜索到的资料基本都是16、17年的了，为知笔记的官网中，Linux跟Mac客户端在20年之后更是完全没有更新过，不得不说还是挺可惜的， 其实它本来很有机会超越印象笔记的。

##### **Onenote**

微软出品，必属精品。大部分情况下这句话没啥毛病，很多人对Onenote十分狂热，我简单试用过一段时间，实在有点不太习惯，放弃了。

优点：

* 继承了微软产品的一贯作风，操作上手门槛极低，任何灵感都可以随时记录

缺点：

* 同步极不稳定，懂得都懂。貌似现在好一些了，近期没有使用过。
* 排版实在有点迷，习惯了 Markdown 的简洁，我有点受不了，更不提对于程序员来说，没有代码高亮支持极其难受

##### **有道云笔记**

待补充，这个软件已经好几年没用过了，最新情况已经不太清楚

##### **Bear**

在iOS跟Mac上发家的一款新兴笔记软件，最近也开启了双链功能支持，我简单使用过。现在已经改名叫做熊掌记了，国内化做的不错。

优点：

* 界面简洁易用
* 支持文档互链
* 对Markdown支持的较好，可以当做一个轻度任务管理软件使用，配合子弹笔记可以玩出花

缺点：

* 只支持苹果系
* 不支持买断，订阅制，这点其实不完全算是缺点，个人不是很喜欢吧
* 记录个人灵感还是蛮舒服的，但是无法当做文章收集软件来使用

##### **VNote**

不想补充了

##### **收集类**

对于大多数场景，这类软件才是真正能够用于收藏的，笔记类软件能够当收藏使用说实话是被印象笔记一手带起来的风气。

##### **Instapaper**

国外老牌稍后读软件，与Pocket齐名，以排版优雅、界面简洁著称

优点：

* 阅读格式精心调校过，比较适合长时间阅读，但说实话手机上我不太想深度阅读
* 阅读界面清爽，没有广告
* 免费版就可以永久保存

缺点：

* 不支持标签
* 微信公众号的文章支持不友好，无法直接微信内收藏， 可以通过iOS捷径等手段使用，不过体感比印象笔记还是好多了。不过有个很要命的问题，微信公众号的文章，解析时候经常会提示未授权，PC 端无法查看图片，只能点击打开原文

##### **Pocket**

同上，国外老牌稍后读软件，感觉相比Instapaper对国内的优化更好一点，但社交元素有点多，我只想安安静静的阅读个文章，不想有这么多干扰

优点：

* 国内优化相对好一点
* 支持微信文章保存

缺点：

* 免费版不支持永久保存
* 干扰元素太多，很多用户比较排斥

##### **Pinboard**

国外的一款比较有名的网页收集类软件，收费理念比较特殊，随着使用人数增加，费用逐步提高，我知道的比较早，但购买的比较晚，当时购买的时候已经到了一年40刀，说实话我觉得有点太贵了。好在支持退款，用了几天之后毫不犹豫的退款了，不是很适合我。

优点

* 可以持久化保存网页快照

缺点

* 界面比较丑，阅读体验不是很好
* 老外的软件，更新迭代很慢，英文不好可能有点吃力
* 稍微有点贵， 40 刀一年

##### **Pinbox**

国产类似 Pinboard 的软件，做的很蛮不错，且针对国人习惯加了很多新功能，例如支持直接保存文字、图片等。价格也比较公道，但移动端体验实在有点糟糕。产品定位是一个纯网页保存类软件，基本类似于浏览器的收藏夹， 不过支持标签、检索等，对稍后读的支持力度其实一般，或者说没有，不支持阅读模式、标注等。

优点：

* 界面好看
* 同步速度很快，比较稳定
* 价格实惠，会员一年 69￥，免费版支持300个书签
* 支持图片和文字收藏
* 支持导出html，还算方便

缺点：

* 移动端版本可用性太差
* 排序功能较差
* 不支持网页快照
* 无法跟微信公众号打通，必须要复制链接，然后切换到 Pinbox 中新增
* 不支持阅读模式跟标注，基本上无法作为深入阅读软件使用

国外一款书签收集软件，之前有段时间蛮喜欢的，界面比较清爽，后来因为网络因素改为使用收趣云了。

优点

* 免费版可以无限制创建文件夹和收藏

缺点

* 国内同步速度太慢，偶尔网络会抽风，经常想收藏一下文章，5秒钟过去了，还在转圈

##### **Cubox**

国产软件，基于 Linnk 重构升级而来，可以说是目前用下来最符合快速收藏文章诉求的软件了。跟作者有过简短的几次邮件对话，交流下来比较省心，回复很快，也很能接受用户意见，促使我毫不犹豫的购买会员。 会员价格一年78￥，可以说很良心了。

优点：

* 免费版即可文章模式支持永久保存，支持200个收藏。
* 网络同步速度贼快，国产软件必备技能
* 支持的导入方式很多，国产软件必备技能
* 导出很方便，对标Instapaper、Pocket，说明产品设计的比较合理，且有一定信心，比那些生怕用户跑了的产品好多了(对，说的就是你，收趣)。毕竟留住用户靠的不是想办法把转出门槛提高，而是产品质量
* 可以直接在微信中分享到指定账号进行收藏，不需要切换软件或者打断公众号浏览

缺点：

* 标注功能还比较弱，免费版支持3个标注，但导出很差
* 不支持快速收集想法，只支持网页收藏

##### **滴答清单**

国产软件里面很优秀的GTD工具，甚至个人觉得没有之一。用来收集灵感十分方便，配合桌面小插件，基本可以做到手机上3s内记录想法。同时各平台支持都很到位， 价格定位也很良心，平时轻度使用完全可以不购买会员。

以下是会员专有功能

* 支持摘要汇总，用来查看本周完成了哪些事项，写周报时候很方便
* 支持日历视图，可以在日历中安排每天的工作，查看本周哪些天会比较忙等
* 支持智能清单，需要会员，可以自定义一些例如重要且紧急、重要不紧急等清单，定期处理

优点：

* 支持自然语言识别，可以快速输入例如明天上午8点做某事的事项
* 列表和事项没有限制，免费版完全足够日常使用
* 内置习惯养成，虽然功能比较弱，聊胜于无，适合轻度用户
* 支持番茄钟，配合番茄工作法还是蛮有用的

缺点：

* 如果用来当做稍后读软件的链接收藏，稍微有点不太合适，链接不会去重，但这个说实话不是它的问题，毕竟软件定位也不是这个
* 苹果用户需要注意下，这里有个坑就是苹果本身交易抽成30%，也就是说你在Mac和iOS上面购买会比原价贵30%，官网直接购买会员只有 139￥可以说非常良心了；直接在iOS上面购买你会发现是 168￥，而且还是到期自动订阅！！！，我之前就没注意被坑了两年。
* 没了，在我心里已经是一款十分完美的GTD软件了。

##### **简悦**

起先是靠做网页上面的阅读模式起家，支持油猴插件，作者对于深度阅读有比较深入的思考，有过对话，对我挺有启发的。最近2.0版本之后，迭代速度飞速提高，更是紧跟潮流，在双链笔记结合上花了大量心思，包括各种标注导出、文章导出、稍后读上都较之前版本有很大提升，重点是，大版本买断只需要18￥，真的非常划算了，建议大家入手一个。

优点：

* 阅读模式很棒，支持超多网站，最重要的是可以自己进行圈选和隐藏
* 标注功能非常强大，可以说是我目前用过的最强大的了，没有之一，包括高亮、备注、打标签等，而且支持各种导出
* 用户隐私方面做得很好，通过各种同步盘本地文件等形式保存信息，简悦自身并不保存相关信息
* 平台支持和导出功能可以说是非常强了，支持超多网盘导出、超多格式导出(md、pdf、epub等等)
* 新版本的稍后读功能很强大，这跟作者的理念是分不开的，基本上不会变成一个仓库，而是各种鼓励你进行阅读。

缺点：

* 功能实在有点太庞杂臃肿，我身为一个程序员，而且是很喜欢研究各种软件的，说实话尝试新软件能力还算比较强了，但是实际使用下来，各种冗长的配置(真的不止几十项)，配合上部分含糊不清的文档，真的有点难以上手，我前后大概花了至少一个下午左右的时间，才算勉强会使用了，使用门槛可以说非常高了(基本的阅读模式不包含在内，上手还是比较方便的)，后面有机会单独写一篇我自己的简悦的使用心得。
* 对用户动手能力要求很高，里面会员支持的各种功能，基本上没有编程基础或者很强动手能力基本可以说是没啥卵用。比如说各种token申请、API使用、自定义模板等等。
* 移动端支持很差，iOS上很难用， 可能也跟iOS本身的封闭有关系，需要使用很多trick手段。跟作者沟通下来之后表示并不是想要做一个快速收藏然后当做仓库的软件，就是想要提高使用门槛，鼓励用户真正的自己进行深度阅读。这个我可以理解，但说实话，这样逼迫我不得不再配合一个其他收藏软件一起使用了，单独使用简悦，全平台打通能力真的不行。

##### **收趣云**

国产稍后读软件，大概16年左右还是火了一阵子的，毕竟国内软件大多针对国人使用习惯做过优化，再加上网络同步速度远快于国外软件。但缺点也很明显，一般Lock-in都比较高，想办法让你从其他各种软件导入进来，导入方式支持的很到位；但想要导出？没门，各种加门槛，要么要求付费，要么干脆不支持。后来由于找不到太好的赢利点，且会员收费策略做的不好，现在已经很少有人用了。

优点：

* 国内网站，速度较快
* 对国内微信公众号等支持较好 ，可以跟微信打通，直接在微信发送到指定账号即可收藏，很方便

缺点：

* 广告太多，还添加了大量社交元素
* 不支持自由导出，需要会员付费
* 已经很久没维护了

##### **Linnk**

已经升级成为Cubox，不再赘述。

#### **个人实践**

经过以上各个软件的简要分析，相信大家也看出来我的一些偏向了。目前个人的PKM流程大概如下，算是个人PKM2.0版本([PKM1.0版本](https://link.zhihu.com/?target=http%3A//blog.samwei12.cn/2020/05/02/Thinking/%25E5%25A6%2582%25E4%25BD%2595%25E6%259E%2584%25E5%25BB%25BA%25E4%25B8%2580%25E4%25B8%25AAPKM%25E7%25B3%25BB%25E7%25BB%259F/)，有关2.0版本输出部分后面有机会再介绍一下)：

##### **输入**

如果是外部输入，例如通过微信公众号、知乎等发现的一些有价值的文章，快速通过 Cubox 保存，不要在手机上花费太多时间。

如果是日常的一些灵感和任务相关，随手记在滴答清单的收集箱中。

##### **组织整理**

单纯的收集是没有意义的，不能满足于当一只仓鼠，在整个知识内化过程中，有一个很重要的步骤就是组织整理，这个跟GTD的理念也是相同的。我会设定一个闹钟，例如每天晚上或者每周末抽出一定的时间来清空收集箱。

对于Cubox中收藏的文章，花费1-2分钟快速明确是否跟当下的关注点和Areas重叠(这里涉及到PARA工作法，有机会再单独整理一篇），如果是需要近期关注的，那么进入简悦稍后读或者直接开始进行阅读，完成阅读后，把笔记备注和标注导出为素材笔记(卡片盒笔记中的概念，也可以理解为PARA工作法中的Resources)，存入Logseq中

对于滴答清单中的待办项，根据四象限法则进行排序整理，如果不是当前需要立马完成的事情，纳入到将来清单中， 定期回顾即可(这里可以参考小强升职记中的概念)。

在这个环节中，双向链接笔记是一个很重要的工具，我对它的定位是一个信息输入输出的枢纽。所有的灵感、外部输入都在简单筛选之后进入到素材笔记中进行打标签、分类、链接，同时需要输出某个特定主题的时候，可以通过双向链接快速查找到相关的笔记内容，整理归纳到博客或者其他外部输出中。

最近这一两年，双向链接笔记已经火的不行了，但目前真正国内稳定可用的软件屈指可数，目前我个人在使用的是Logseq这款，关于双向链接笔记的使用选择方面，完全可以单独再整理一篇了，先挖坑，后面一定补上 :)

##### **输出**

将信息快速内化为知识的一个有效手段就是传授给他人，这其中输出博客是一个很好的方案。现在搭建维护个人博客的成本极低，同时又有很多类似于飞书、语雀这样的平台用来进行输出，唯一限制你的可能就只有你的懒惰了(说的就是我自己)。

我目前采用的方案是Hexo，这方面可以参考 [如何使用 Hexo 搭建个人博客](https://link.zhihu.com/?target=http%3A//blog.samwei12.cn/2015/09/01/Utilities/Writing/%25E5%25A6%2582%25E4%25BD%2595%25E4%25BD%25BF%25E7%2594%25A8-Hexo-%25E6%2590%25AD%25E5%25BB%25BA%25E4%25B8%25AA%25E4%25BA%25BA%25E5%258D%259A%25E5%25AE%25A2/%3Fhighlight%3D%25E5%258D%259A%25E5%25AE%25A2) ，这里不再赘述，值得一提的一款软件是MWeb，这是一款博客写作发布软件，目前支持很多平台，包括语雀、少数派、Wordpress 等等，值得一提的是，它还支持使用 Metablog API，这样就可以同时在多个博客平台进行发布。

关于我个人的PKM实践，大体方案就介绍到这里，希望对大家有所帮助，关于PKM的更多信息可以参考我的[旧文](https://link.zhihu.com/?target=http%3A//blog.samwei12.cn/2020/05/02/Thinking/%25E5%25A6%2582%25E4%25BD%2595%25E6%259E%2584%25E5%25BB%25BA%25E4%25B8%2580%25E4%25B8%25AAPKM%25E7%25B3%25BB%25E7%25BB%259F/)，虽然很多工具软件已经过时或者需要更新了，但整体的思路是没有大变的。这里的一个很重要的原则就是不要太过热衷于折腾工具，都是小术而已，只有真正了解了道，才能做到各种工具都能随心所欲的驾驭，最终的目的是为了学习知识，而不是学习工具。

![使用手机知乎 APP，打开我的页面，点击左上角扫一扫，可以扫描本页的二维码](https://pic1.zhimg.com/80/v2-3ca9dfefdc7ab9034c123abd69a57ae0.png)
