---
人员: 
  - "[[向阳乔木]]"
tags:
  - articles
日期: 2025-04-26
时间: None
链接: https://mp.weixin.qq.com/s/BrPlkNt7RSVFXVlYGfs60w
附件: https://mmbiz.qpic.cn/mmbiz_jpg/jibL99tg2bCVDLqXOu2DCUEzibfb6N7uv8wQF2IJXIyNjndUJX4oGgKWNuVVy10nM5S7gft2l0xtRHRDFW9LwOCA/0?wx_fmt=jpeg)
---
## Document Note

## Summary

两张照片，20秒生成，刷爆全网的变装视频原来是这样做的。

## Full Document
**最近AI圈又炸锅了！**

国产AI视频公司生数科技，刚发布的 Vidu Q1，直接冲上全球权威 VBench 榜单榜首。

超过 Runway、Sora、LumaAI 等大牌，一举拿下文生视频榜单双料第一。

![Image](https://mmbiz.qpic.cn/mmbiz_png/jibL99tg2bCVDLqXOu2DCUEzibfb6N7uv8QUr8AybCz0JZDygF22ticaicMBW6uzBicZd48HKSvhL0rFAWHIFGZ8XnA/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)
在SuperCLUE的图生视频专项榜单上，Vidu Q1同样表现亮眼，无论是动漫风格还是写实风格，都稳居榜首。

官方报道：

> https://mp.weixin.qq.com/s/UHOYgeu5b6G6Cjagw3s5Cg
> 
> 

评测归评测，实际效果到底如何？

抽空体验了下，发现真的有点东西！

不废话，直接上Vidu Q1制作的视频，用剪映简单处理了下。

#### 两张图就能合成炫酷视频

最近经常在TikTok、抖音刷到大量奇特视频。

比如一个人和一只动物，人摸下动物，就变成动物头人身的视频。

动不动就几十到上百万播放。

![Image](https://mmbiz.qpic.cn/mmbiz_png/jibL99tg2bCVDLqXOu2DCUEzibfb6N7uv88WZKc6veYJibCOicSrqF4ibG98jFibWPKibft2Krtu7Yibb2VbxMV6uNcJNg/640?wx_fmt=png&from=appmsg)
![Image](https://mmbiz.qpic.cn/mmbiz_png/jibL99tg2bCVDLqXOu2DCUEzibfb6N7uv8ic88ZHw18lrOic9zC5YP1tM5oIMABTwVuZ2sXibVfIoqIqwqgABEicEVhQ/640?wx_fmt=png&from=appmsg)
朋友问我怎么做的，我也不懂。

直到用了Vidu才发现，原来生成这类视频如此简单。

仅需提供两张照片。

文字描述下图片变化过程，点下按钮就能生成。

![Image](https://mmbiz.qpic.cn/mmbiz_png/jibL99tg2bCVDLqXOu2DCUEzibfb6N7uv8gEt2ibRzkBpy7lgFK3PjAI0jCztCvjBsVByC8hUdyBBxibicM7qL2M6uQ/640?wx_fmt=png&from=appmsg)
我视频里的美女换装就这样做的。

传两张照片，一张是视频开头照片，一张是最后视频结束时照片。

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/jibL99tg2bCVDLqXOu2DCUEzibfb6N7uv89xnVrXBcZ3aOkcuBqRDDphia67fOxrKKx7cibBBiaiamTcofWYlpIyQpng/640?wx_fmt=jpeg&from=appmsg)
两张图片，中间如何过渡和变化，全靠提示词，如：

```
第一张图人物优雅转身面向镜头。  
  
头发随动作自然飘逸， 镜头平滑环绕人物一周，捕捉连贯动作过程。  
  
人物自然流畅地调整姿态，逐渐进入第二张图的舞蹈起始动作。  
  
手臂和身体线条保持连续性，最终稳定呈现第二张图中的性感舞蹈姿态。  
  
确保面部表情和身体比例一致。
```

如果不会写提示词，点App的发现Tab，有大量视频案例分享，能看到别人的提示词，找找灵感。

另外，如果点击“设置”按钮，还能选“运动幅度”、“生成视频数量”、“视频清晰度”等。

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/jibL99tg2bCVDLqXOu2DCUEzibfb6N7uv8ckWhLqsBjeRORdV7OypGFJTu9GQk5tbiagWcJq9GCgpVicCn6kDf7vYA/640?wx_fmt=jpeg&from=appmsg)
设置最底部，有个错峰生成选项，研究发现，简直就是“白嫖模式”，勾选后：

* • 非高峰时段，**0积分消耗，免费不限量生成视频**。
* • 高峰时段，自动排队，等服务器空闲时处理你的任务，也不扣积分。

利用产品机制设计，充分利用服务器，又能造福用户，妙啊！

#### 一键生成玩法

我理解，应该还是首尾帧生成的变种？

不过，官方直接做成了模版。

我们只需选模版，传一张图片，就能生成类似效果。

![Image](https://mmbiz.qpic.cn/mmbiz_png/jibL99tg2bCVDLqXOu2DCUEzibfb6N7uv8Yg5rNvcfHZbqagV45SWePqxdvUMRNgOpIySneu9TB19xIkquGB3yhw/640?wx_fmt=png&from=appmsg)
比如：

**一键吉卜力：** 把你的照片生成吉卜力风格动画。

**万物皆可变形金刚**： 把任意照片中的主体变身为变形金刚。

**一键互换性别**: 试试把自己自拍照传上去，看看变异性什么样子，哈哈！

#### 文本生成动漫测试

Vidu当然也支持文本生成视频。

尤其动漫视频生成，效果很赞。

自己做的一个动漫视频Demo

**提示词如下：**

```
日式动漫风格的机器狗，流线型金属设计，反光装甲板， 发光蓝色能量线条。  
  
警觉姿态，穿行于被毁的东京城市景观，倒塌的摩天大楼，杂草丛生。  
  
戏剧性的末日氛围，背景烟雾升腾，破碎的科技设备散落四周，温暖的日落光线投下长长阴影。  
  
电影级镜头跟随机械狗移动，机械部件运动的精细动画。  
  
高质量渲染，电影颗粒效果，16：9宽高比，24帧每秒，流畅运动
```

不断调整提示词，生成不同画面，剪辑拼接成有情节的动漫视频。

想象空间也很大。

#### 如何下载体验Vidu？

iOS、安卓、电脑端都可以体验。

![Image](https://mmbiz.qpic.cn/mmbiz_png/jibL99tg2bCVDLqXOu2DCUEzibfb6N7uv8P3TSBEKR1MiaUts1c7gnEq95FAzrDQAAicBLP2eWuro1L1GNso9ibRarw/640?wx_fmt=png&from=appmsg)
**网页版：**

> https://www.vidu.cn/
> 
> 

**iOS手机：**

> https://apps.apple.com/cn/app/vidu-ai/id6741831482
> 
> 

**安卓：**

> https://prod-ss-images.s3.cn-northwest-1.amazonaws.com.cn/apk/app\_prodChina\_release\_v2.0.1\_104092008\_20250409200833\_fae544a.apk
> 
> 

#### 后记

经测试，发现Vidu Q1的视频质量真不赖，且模型语义理解和遵循能力都很优秀。

更为重要的是，性价比超级高。

粗略估算对比：

|  |  |  |  |  |
| --- | --- | --- | --- | --- |
| 对比 | Vidu Q1 | 可灵 2.0 | Sora | 谷歌 Veo2 |
| 分辨率 | 1080p | 720p | 1080p | 1080p |
| 5秒成本 | 3元 | 10元 | 30元 | 20元 |

价格相当能打。

尤其还有贴心的“错峰模式”，完全可以不花钱生成视频。

简直AI视频生成圈的“赛博菩萨。”

发挥想象力和创造力，感觉人人能当导演的时代真的不远了。

#### 附录

##### VBench 榜单

![Image](https://mmbiz.qpic.cn/mmbiz_png/jibL99tg2bCVDLqXOu2DCUEzibfb6N7uv8HSiaSahSRspvtbzjYPz921ibicCuGAR9rvkRB4HAWMRp1iaHD7tgctCic6A/640?wx_fmt=png&from=appmsg)
https://huggingface.co/spaces/Vchitect/VBench\_Leaderboard

##### 更多首尾帧玩法

![Image](https://mmbiz.qpic.cn/mmbiz_png/jibL99tg2bCVDLqXOu2DCUEzibfb6N7uv89rNH71OsciaeG7X1XAtjmqjJwb8enpwicdzAqZ9PQVpqqTYkEqb2YLibA/640?wx_fmt=png&from=appmsg)
https://xiangyangqiaomu.feishu.cn/wiki/Vtedw9JhtiooW7k6Qy6cAN2anmf?fromScene=spaceOverview
