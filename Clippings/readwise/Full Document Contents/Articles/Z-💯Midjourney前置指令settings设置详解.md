---
人员: 
  - "[[Z]]"
tags:
  - articles
日期: 2024-09-22
时间: None
链接: https://cloud.tencent.com/developer/article/2459506
附件: https://cloudcache.tencentcs.com/open_proj/proj_qcloud_v2/gateway/shareicons/cloud.png)
---
## Document Note

## Summary

首先我们需要在服务器的聊天区输入/setting调出Midjourney的前置指令/settings设置

## Full Document
首先我们需要在[服务器](https://cloud.tencent.com/product/cvm/?from_column=20065&from=20065)的聊天区输入`/setting`调出Midjourney的前置指令`/settings设置`

代码语言：javascript

```
/settings
```

![[Attachments/01ac33bfefdc8d6216d1171017cece77_MD5.png]]
输入指令回车后Midjourney Bot会给我们回复一个`前置指令/settings的设置菜单`，在这个菜单中的内容就是本文所要详细介绍的部分。

![[Attachments/c013a94fb5b63fbbe405654abb52accf_MD5.png]]
##### 💯Use the default model（AI绘画所使用的大模型）

首先介绍的是第一个模块 Use the default model，这是一个下拉框，为我们提供了丰富的选择：我们可以从中挑选并使用Midjourney推出的各个AI绘画大模型。

![[Attachments/e2a279483e00db95626f7fac57b066b1_MD5.png]]
![[Attachments/f2bd955021640a8aca41645876bfe311_MD5.png]]
在`/settings`中的Use the default model设置里，每一个模型都可以被看作是一位AI画师,Midjourney已经推出了多个版本的大模型。通常，较早版本的大模型，它们的绘画能力相对先版本较弱，因为大模型的迭代总是朝着相对更好的方向发展。随着技术的不断发展，越往后发布的大模型，在绘画能力上往往有着更为出色的表现。尤其是Midjourney的最新版本v6.1，作为最新的大模型，它在绘画能力上无疑是最为强大的。

在Use the default model模块中，我们主要可以选择两种类型的大模型：Midjourney模型和Niji模型，接下来我将介绍一下这两种模型。

###### Midjourney Model（Midjourney 模型）

>  Midjourney模型是一种泛用风格的大模型，它涵盖了二次元和三次元的多种图像类型，无论是什么类型的图片，它都能有一定的处理能力。这使得Midjourney模型在绘画领域具有广泛的适用性。 
> 
> 

Use the default model (V6.1)表示在生成图像时，系统所自动采用默认的绘画模型版本，一般来说都是默认使用最高版本的Midjourney模型。

![[Attachments/2a0b03cd8241a5f1188040db6b90d988_MD5.png]]
接下来，为了更直观地感受不同版本模型之间的差异，我们将使用同一Midjourney提示词来引导不同的版本模型为我们输出图片。这一步骤将帮助我们深入了解每个模型独特的绘画风格和表现能力，从而更全面地了解它们各自的特点和优势。

提示词后的`--v+版本号`正是表示所使用的Midjourney模型版本

代码语言：javascript

```
1 cat sitting near the book,cute 
```

* Midjourney Model V6.1

![[Attachments/a46ddf4f4a434e28bab4433185f1bd3c_MD5.png]]
![[Attachments/d27472aac7df2d700bde8360a1371138_MD5.png]]
* Midjourney Model V6.0

![[Attachments/85b6c3af47a6794cb2236f89e38449b7_MD5.png]]
* Midjourney Model V5.2

![[Attachments/6fd896fb31bac2d7a85fe019f74a1b36_MD5.png]]
* Midjourney Model V5.1

![[Attachments/913ad64937ec0f340f12c6f300bc993e_MD5.png]]
* Midjourney Model V5.0

![[Attachments/a173f1cae4ef5108f5b00b9e22bdd941_MD5.png]]
* Midjourney Model V4

![[Attachments/612abb884033134a896401125f96ea2b_MD5.png]]
* Midjourney Model V3

![[Attachments/a65d9d7cfdb6c9b86e1b770da4bffd76_MD5.png]]
* Midjourney Model V2

![[Attachments/be493055467ba961478fbb976e831f3c_MD5.png]]
* Midjourney Model V1

![[Attachments/2c1488938e36285cf35bbda13d6c06e7_MD5.png]]
通过以上不同Midjourney模型生成图片对比，我们发现从V3版本开始出图的质量开始大幅度的下降，这个猫跟书的关系理解的不怎么好。V2版本、V1版本连这个猫的形态都不能正确的刻画。这一观察结果，实则验证了我们先前的一个观点一一较早推出的模型往往性能有限。原因在于，随着技术的不断进步，新发布的模型得益于更先进的训练技术和更高质量的图片数据集，因此能够生成更为出色的结果。

###### Niji Model（Niji模型）

Niji模型专注于动漫风格，它特别适合制作动画和插画风格的图像。如果你对动漫风格有特别的喜好或需求，那么Niji模型将是你的不二之选。

![[Attachments/645359e963136434413f1e1935658461_MD5.png]]
和上面一样，我们使用同一提示词来让不同版本的模型为我们输出图片，更直观地感受它们之间的区别。

代码语言：javascript

```
1 cat sitting near the book,cute 
```

* Niji Model V6 [ALPHA]

![[Attachments/f9cf7658192479161b1e5a272663bd66_MD5.png]]
* Niji Model V5

![[Attachments/d52dc953e190fb890da4d46847484508_MD5.png]]
* Niji Model V4

![[Attachments/d305575f666c67f7712853358ddc8e2a_MD5.png]]
从以上我们可以看出随着AI绘画模型版本的更新迭代，绘画模型高版本相对于低版本会有更多细节上的呈现和对提示词理解的优化。我们在一般情况下也都是使用最高版本的AI绘画模型，使用体验感会更好。

**接下来我将从模型类型角度分别介绍Midjourney模型和 Niji 模型的/settings设置。**

##### 💯Midjourney Model V6.1的/settings设置详解

在Midjourney Model 的6.1版本中，用户可以通过/settings选项自定义多个设置，以优化图像生成的效果和速度。这些设置不仅影响最终图像的视觉呈现，还能根据个人需求调整生成效率和风格表现。接下来我将详细介绍这些设置，包括RAW Mode、Stylize、Personalization、Public Mode、Remix Mode、Variation Mode、生成速度模式以及Reset Settings功能，帮助您更好地理解和应用这些工具，从而提升创作体验。

![[Attachments/03663a7560a353fd215bb2ad13f58bd1_MD5.png]]
###### RAW Mode（原始模式）

作用：RAW Mode在摄影领域里，代表着相机捕捉到的原始图像数据，它特别注重保留图像的真实性和自然性。当我们在Midjourney中启用`RAW Mode`时，它会让生成图片的过程减少不必要的联想和修饰，从而使得最终的图像效果更加贴近写实风格。如果你需要在你的创作中追求真实感的效果，那么RAW Mode无疑是一个值得尝试的选择。

![[Attachments/1fd4d486697d5279817f3ae5f57ddbc7_MD5.png]]
代码语言：javascript

```
1 cat sitting near the book,cute 
```

* 开启 RAW Mode ： 开启RAW Mode生成的图片就像是摄影机拍出来一样。就算我们在它的提示词没有进行一个仔细的摄影画面描述，但是他画出来的内容就已经很像是我们摄影机拍出来的效果了。

![[Attachments/3dbce095cbac3408bcb0aa4abca5d182_MD5.png]]
* 未开启RAW Mode 虽然没有开启RAW Mode生成的图片一样很好看，一样很写实，但你明显感觉到跟前面那个图片比起来，没有那种摄影的感觉了，像是某个电影的截图或者某个游戏的截图一样。

![[Attachments/40176af61059b28154ff13e49e971913_MD5.png]]
当然了，有的时候我们用的时候，你会感觉开不开RAW Mode，好像出的图都差不多，特别是当我们的提示词中有摄影相关类型的词的时候。

###### Stylize（风格化程度）

Stylize（风格化程度）这一参数控制着生成图片的风格化或艺术化程度。通过适当调整这一参数，用户可以生成在色彩、构图和形式上都更具艺术感的图像。具体来说，当风格化程度设置得较低时，生成的图像在艺术化处理上会相对较弱，但与此同时，图像与提示词之间的相关程度会更高，即图像内容更贴近用户的初始描述或意图。相反，如果风格化程度设置得较高，那么生成的图像在艺术化表现上会更加强烈，色彩、构图和形式都可能呈现出更为独特和创新的风格。然而，这也意味着图像与提示词之间的相关度可能会降低，因为艺术化的处理可能会使图像内容在一定程度上偏离用户的初始描述。

![[Attachments/d4e223c9f4ac6f6e8a389308f00c979d_MD5.png]]
可以这么理解：

>  风格化程度越低，艺术化越弱，与提示词越相关程度越高 风格化程度越高，艺术化越强，但与提示词相关度降低 
> 
> 

* Stylize强度

1. Stylize low：几乎没有风格化，画面比较干
2. Stylize med ： (推荐)略有风格化
3. Stylize high ：(推荐)风格化较为明显
4. Stylize very high：风格化非常明显

代码语言：javascript

```
1 cat sitting near the book,cute
```

我们在不同模式下使用相同的提示词让Midjourney为我们输出图片，我们可以看到：

* Stylize low 第一张就是stylize low的图片，可以看到基本上没有风格化，就是我们拍照那种感觉

![[Attachments/adbb829f2ce5a4994f3871854ec61110_MD5.png]]
* Stylize med 风格化中等的图片，可以看到猫咪多了一个眼镜，它有了一些自己的想法，对于猫看书他可能会发挥一些联想和想象。

![[Attachments/70338bf6ec27e8bafeb0c8bae9eed4ae_MD5.png]]
* Stylize high 然后再往下可以看到我们Mdijourney发挥的联想和想象更大。因为它是Stylize high，风格化非常高。

![[Attachments/6f3d66e176f79fe837687d50cfb051b4_MD5.png]]
* Stylize very high Stylize very high甚至让猫咪呈现出了人看书时的姿态。

![[Attachments/b7000954e44401ebb260fa2d82c671bb_MD5.png]]
我们可以看到生成的照片跟一开始照片有很多的区别，不管是光影、色彩，还是构图，还是我们这个画面中一些元素。

###### Personalization（个性化）

>  Personalization功能可以被视为一种LORA的实现，它通过收集用户的风格偏好信息，对Midjourney的底层模型进行微调，从而生成具有用户专属风格的图像。 
> 
> 

![[Attachments/99ac7eac86aa4741ba2b851993f56dff_MD5.png]]
**使用方法**

1. 图像评级：首先，用户需要在Midjourney的Alpha官网（或相关平台）的「Rank Images」版块内，完成至少200组图像评级。在这个过程中，用户通过选择自己更喜欢的图像，帮助系统记录下自己的风格偏好。这一步是调用Personalization功能的前提。
2. 开启个性化功能：完成图像评级后，用户可以在提示词后加上–p参数，并发送出去。此时，Midjourney将基于用户的风格偏好生成图像，生成的图像将显示出用户的专属风格，与默认模型会有明显不同。
3. 分享与调整：系统还会为每个用户返回一个对应的个性化代码（如–personalize jsg4hzo），该代码是独一无二的，并支持分享。使用他人的个性化代码，就相当于用他的专属风格来生成图像。此外，用户还可以通过–s参数（接受0到1000之间的值，默认值为100）来调节个性化风格的强度，数值越高风格越明显。

如果没有完成至少200组图像评级会出现以下提示信息

![[Attachments/91d4a8aeebf029040135bcf1508965db_MD5.png]]
代码语言：javascript

```
“评分不足
你还没有足够的评分来使用 --p 参数创建个性化风格！
请访问 [https://www.midjourney.com/rank](https://www.midjourney.com/rank) 来帮助改进Midjourney并打造你的个性化风格。
在对一些图像进行评分后，请稍后再试；我们建议你至少评分200张图像。
/imagine 1 cat sitting near the book,cute --p”

这里的 /imagine 1 cat sitting near the book,cute --p 是一个示例命令，用于生成一张图片，其中有一只猫坐在书旁边，风格被标记为“cute”，但由于你没有足够的评分来定义个性化风格（--p），这个命令可能不会按你期望的方式工作，或者可能根本不起作用。
```

>  这条信息提示你当前还没有足够的评分来创建一个个性化的绘画风格（通过 --p 参数使用）。Midjourney 的个性化风格功能依赖于用户对其生成的图像进行评分，这些评分帮助算法学习并理解你的审美偏好，从而能够生成更符合你个人风格的图像。 
> 
> 

因此想使用该功能需要先完成至少200组图像评级才能使用，后面详细功能就不深入了，这里就只是简单介绍一下。

###### Public mode （公开模式）

>  Midjourney的Public mode指的是公开模式。在这个模式下，用户生成的图像将不仅仅是自己可见，而是会出现在其他地方，也就是说，所有人都可以看到这些图像。这种设置通常适用于那些希望分享自己创作成果，或者想要将作品公之于众的用户。 Public mode是Midjourney提供的一种灵活性较高的功能，它允许用户根据自己的需求和偏好来选择是否公开自己的作品。对于希望在社交媒体、个人网站或其他平台上展示自己绘画才能的用户来说，Public mode无疑是一个非常方便的选择 
> 
> 

Public mod跟我们生成图片并没有关系，就是当你这个点开的时候，你的图片可以被其他人能看到。当你把这个关上的话，你这个图片就不会再被其他人看到，他只能被你自己所看到。但是这个是有一定限制的，需要Pro及以上的会员才能关闭。如果你希望你自己身上的图片完全保密，那你就花更多的钱然后把这个东西关掉就可以了，对于我们图片生成并不是很重要。

![[Attachments/95a886b5219103451cd94359191798bb_MD5.png]]
###### Remix mode（混合模式）

>  我们接着往下看看这个remix模式，也就是混合模式。主要是用于图片生成图片后下方的V指令搭配使用。 
> 
> 

![[Attachments/ffecd5e2a8f4616b096c898b40571b9a_MD5.png]]
（V1、V2、V3、V4）按钮表示默认将保持与原图相似的风格和构图，但在细节上会有所不同，从而为用户提供更多的选择和微调空间。

![[Attachments/d94f411ae3de4c0e38a4fd2999f7309d_MD5.png]]
代码语言：javascript

```
1 cat sitting near the book,cute 
```

* 开启Remix mode后 你会发现其实和平时输出没有什么区别

![[Attachments/01c3354e1bdcdbfc296028b5ac374861_MD5.png]]
* 此时我们按下V1，如果我们开启了Remix mode后，我们可以手动添加一些新的提示词。

![[Attachments/02dabc1778a825f792020d5097c410c3_MD5.png]]
* 然后我们在提交之后生成出来的这个图片，就是既有原来这个图片的内容，又混合着我新写提示词内容，它就可以实现一个图片的迭代。

![[Attachments/05deb07a86aca81ac6c82b4a21af9f50_MD5.png]]
* 注意：这个效果必须是在开启remix模式之后才能实现，如果没有开启remix模式，系统就会自动使用原先的提示词重新生成图片，就不能在原来提示词基础上新增新的提示词了。这就是混合模式的具体作用。

###### Variation Mode（变化模式）

>  Variation Mode指的是一种功能模式，允许用户在保留原始图像或素材的某些关键特征的基础上，通过调整参数、应用不同的风格或进行其他修改，来生成具有多样性的变体或变化版本。 
> 
> 

![[Attachments/32bed80e4056485599dce02880cf7b6e_MD5.png]]
Variation Mode有俩按钮，一个叫High Variation Mode高变化模式，一个叫Low Variation Mode低变化模式。这个高变化模式和低变化模式改变的是什么地方呢？改变的还是这个地方的V1、V2、V3、V4按钮。我在这个地方对画面进行变化，但是我怎么样控制它变化的一个程度呢？如果我们点击高变化模式，那么我们点击这里的变化的时候，它生成出来的四张图会在原来的基础上发生比较大的变化。相对来说比较如果我们这边选择低变化模式的时候，我们点击V1、V2、V3、V4的时候，会在原图的基础上产生一个较小的变化。来我们来看一下效果。

代码语言：javascript

```
1 cat sitting near the book,cute 
```

* 原图：

![[Attachments/a66cec34d663c8576612dd93bd12a8aa_MD5.png]]
* 开启High Variation Mode： 开启High Variation Mode后点击V按钮生成的图片变化的很大，可以看到和原图相比，虽然还有猫、还有植物、还有书架，但是猫的神态和这些元素所在的位置各有不同，跟原图的第一张图产生了非常大的变化。

![[Attachments/daaaa758fc8c45af7e723c1269aae7e1_MD5.png]]
* 开启 Low Variation Mode: “Subtle” 是一个形容词，其基本含义是“微妙的”、“精细的”、“不易察觉的。 我们可以开启 Low Variation Mode后点击V按钮生成的整个的构图画面几乎和原来一模一样，只是在一些细节方面有调整。比如说桌子的纹理，背后的这个书的颜色会有一点点变化。

![[Attachments/9d264f5c0a3fcd28ef08e9c9db8ef265_MD5.png]]
###### mode（生图速度模式）

>  在生成图片时，有三种不同的速度模式可以选择，分别是Turbo模式、Fast模式和Relax模式。这些模式的选择会直接影响图片生成的速度。 
> 
> 

![[Attachments/90850ede424846d7285d4d2f8743454e_MD5.png]]
进行一个直观的对比：这里fast发出指令比relaxed晚几秒钟，但是当fsat模式已经出图了，relaxed只生成到55%。

![[Attachments/445ac925c4053d70f855c677c4c55bd8_MD5.png]]
首先是Turbo模式，也就是涡轮模式，它是三种模式中速度最快的。使用这个模式时，大约只需15秒钟就能生成四张图片。Fast模式是快速模式，生成速度也很快，大约在一分钟左右。最后是Relax模式，也称为放松模式，它的生成速度最慢，可能需要1到10分钟才能生成一张图片。

| 模式 | 生成图片速度 | 消耗点数 |
| --- | --- | --- |
| Turbo mode | 最快速度生成图片，约15秒 | 消耗双倍的账号点数 |
| Fast mode | 快速生成图片，约1分钟 | 正常消耗账号点数 |
| Relax mode | 缓慢生成图片，约1-10分钟 | 收费账户不收点数 |

根据我的使用经验，Relax模式的生成速度有时与Fast模式相近，但有时确实会更慢。选择哪种模式主要取决于你的需求，尤其是在需要一次生成大量图片时，使用Relax模式是一个不错的选择，比如可以在晚上一次性向Midjourney输入好几个图片提示词，早上起来就可以收集生成好的图片。

需要注意的是，选择速度更快的模式是有代价的。Turbo模式会消耗双倍的账号点数，这意味着本来可以生成2000张图的点数，现在只能生成1000张。而Fast模式消耗的点数是正常的。相比之下，Relax模式不消耗收费账号的点数。因此，如果时间不是特别紧迫，通常选择Relax模式或Fast模式是比较明智的。

###### Reset Settings（重置设置）

>  Midjourney的"Reset Settings"功能用于恢复默认设置。使用这个功能，可以将所有自定义的设置和参数重置为Midjourney的原始状态。尤其是在对系统进行了大量调整后，想要回到默认的起始点时，可以有效帮助我们节省时间。 
> 
> 

![[Attachments/28019dcf523299bdbca9fe7ae65e1f6a_MD5.png]]
##### 💯Niji Model V5的/settings设置详解

>  Midjourney绘画模型除了Midjourney Model的这个大模型之外还有Niji Model，这个Niji模型在我们切换过之后，会发现里面的参数设置选项会和Midjourney Model的有点不一样。 
> 
> 

![[Attachments/06c5be33181f179e1015e260ec5f9232_MD5.png]]
我们可以看到它底下的大部分参数都是一样的,所以可以参照上面的Midjourney Model的/settings设置。我们发现Niji Model的参数设置选项和Midjourney Model有两点不一样，第一个就是没有那个RAW模式，这很正常。那个RAW模式是拍现实风的照片，三次元照片才有。Niji模型这个是二次元大模型，那肯定不存在相机模式。我们还能看到的它不同地方在于这几个按钮，Default Style(默认风格)、Expressive Style (表 现力强的风格)、Cute Style(可爱的风格)、Scenic Style(风景的风格)和Original Style(原始的风格)。这五个按钮是一起的，它们是一组设置的是同一个东西。接下来我们主要介绍这个设置。

![[Attachments/26a69a8f773b2585a9461df7e1f5129a_MD5.png]]
###### Style（Niji模型二次元生图风格）

| 类型 | 偏好 |
| --- | --- |
| Default Style （默认的风格） | 中规中矩的动画风格 |
| Expressive Style（表现力强的风格） | 偏向于扁平2D插画风格 |
| Cute Style（可爱的风格） | 更擅长生成可爱风格的东西 |
| Scenic Style（风景的风格） | 更擅长生成风景，让背景、光影更加漂亮梦幻 |
| Original Style（原始的风格） | Original Style 使用了最初的Niji Model Version5，这是2023年5月26日之前的默认版本。 |

我们分别使用相同提示词设置不同风格，看看Niji模型输出的图片有什么不同。

代码语言：javascript

```
1 cat sitting near the book,cute 
```

* Default Style 默认风格下我们生成出来这个图片就是我们中规中矩的Niji模型二次元的风格

![[Attachments/e45236607ebf6963ae543876cb1a60e2_MD5.png]]
* Expressive Style Expressive风格，这种模式下生成出来的图片，就算你不写提示词，它也更像那个2D平面插画会更扁平一些。

![[Attachments/7d789f90450674f4712df48e81704656_MD5.png]]
* Cute Style 这个Cute风格生成出来这个猫明显就感觉那种很可爱的感觉，软萌软萌的。

![[Attachments/adda983bc0a1c9dd95e6592d00764648_MD5.png]]
* Scenic Style 风景风格，可以看到很明显这个猫在画面中的占比变小了，但是周围环境在画面中的占比变大了。这个大模型它会更倾向于描绘出一个更加复杂、更加漂亮、更加光影色彩好看的一个背景。

![[Attachments/e572a8373b5c25718373d2b6ca7e0b3b_MD5.png]]
* Original Style 最后是一个原始风格，这个就是我们的Niji模型旧版本的一个表现。可以看到这个旧模型也有它的可取之处，大家可以根据自己的喜好进行选择使用。

![[Attachments/5267b9fee85bb02cf13b964cf370d4a1_MD5.png]]
##### 💯结语

![[Attachments/1e1756b87ea6734485128b7e408143d3_MD5.png]]
![[Attachments/08d78885ba975a3878a5380f54058a46_MD5.png]]
