---
人员: 
  - "[[歸藏的 AI 工具箱]]"
tags:
  - articles
日期: 2025-04-01
时间: None
相关:
  - "[[uv]]"
  - "[[exa]]"
  - "[[mcp]]"
  - "[[npx]]"
  - "[[uvx]]"
  - "[[json]]"
  - "[[arxiv]]"
  - "[[figma]]"
  - "[[flomo]]"
  - "[[claude]]"
  - "[[cursor]]"
  - "[[mcp.so]]"
  - "[[科技]]"
  - "[[高德]]"
  - "[[api key]]"
  - "[[node.js]]"
  - "[[v3 0324]]"
  - "[[chatwise]]"
  - "[[obsidian]]"
  - "[[windsurf]]"
  - "[[mcp协议]]"
  - "[[http协议]]"
  - "[[nodejs.org]]"
  - "[[powershell]]"
  - "[[exa_api_key]]"
  - "[[mcp servers]]"
  - "[[smithery.ai]]"
  - "[[ai编程 ide]]"
  - "[[cherry studio]]"
  - "[[flomo api url]]"
  - "[[ai工具生态]]"
  - "[[gemini 2.5 pro]]"
  - "[[loccal rest api]]"
  - "[[arxiv-mcp-server]]"
  - "[[obsidian_api_key]]"
  - "[[amap_maps_api_key]]"
  - "[[windsurf settings]]"
  - "[[@chatmcp/mcp-server-flomo]]"

链接: https://mp.weixin.qq.com/s/BjsoBsUxCzeqXZq46_nrog
附件: https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY67hX8KiaWdVyf3DHBOdibOXT2md0y1A7k0fVeGMxxaojwMcwFMPLeEkKw/0?wx_fmt=jpeg)
---
## Document Note

## Summary

本文详细介绍了 MCP（Model-Context Protocol）协议及其对普通用户使用 AI 工具的意义。MCP 统一了 AI 模型与各种应用之间的数据交互格式，降低了适配成本，使得无论何种模型，只要支持 MCP 协议，就能与支持 MCP 的工具兼容，极大提升了 AI 工具的开放性与灵活性。文章针对普通用户，重点介绍了如何在 ChatWise 和 Windsurf 两款客户端中配置 MCP 服务，详细讲解了安装依赖、获取 MCP 服务、配置步骤及注意事项。

作者还分享了多个实用案例，包括从 Figma 获取设计稿生成网页、手动搭建 AI 搜索工具（Exa & Time）、利用 MCP 搭建 Obsidian AI 知识库、用高德 MCP 检索附近咖啡馆、从 arxiv 检索并下载论文，以及通过 AI 快速创建 Flomo 笔记。这些案例展示了 MCP 在不同场景下的应用潜力，帮助普通用户从零开始实现复杂的 AI 工作流。

文章最后强调，MCP 协议推动 AI 工具生态从封闭向开放转变，使 AI 不再是被动服务，而成为主动助手。尽管当前配置门槛仍高，但随着技术成熟，未来用户只需简单操作即可使用 MCP。作者鼓励读者积极尝试，认为掌握这些技术将成为未来核心竞争力。

---

**问题 1：**  
什么是 MCP 协议，它解决了 AI 模型与应用之间的哪些问题？

答案：  
MCP 是统一 AI 模型与应用间数据格式和交互方式的协议，解决了不同工具 API 格式不统一导致的适配复杂和功能添加缓慢问题，使模型与多应用兼容变得简单。

**问题 2：**  
普通用户如何在 ChatWise 中快速配置和使用 MCP 服务？

答案：  
ChatWise 支持直接从剪切板导入 MCP 的 Json 配置，用户只需导入配置、填写必要的命令和环境变量，启用对应 MCP 服务即可通过自然语言调用 AI 工具，简化了配置流程。

**问题 3：**  
本文中提到的 MCP 应用案例有哪些，如何帮助用户提升 AI 使用体验？

答案：  
案例包括从 Figma 生成网页、搭建 AI 搜索、构建 Obsidian 知识库、查询附近咖啡馆、检索下载论文及快速创建 Flomo 笔记，展示了 MCP 在多场景集成 AI 功能，帮助用户高效实现定制化 AI 工作流。

## Full Document
最近 MCP 协议很火，自己也发掘了一些玩法，但是目前来看 MCP 的配置还是过于繁琐了，对普通人门槛有点高。

这几天终于摸索出来了一些方法让大家可以相对容易理解的方式配置 MCP 服务。

后面我也会直接给你几个常用的案例，教你从配置到使用的全过程，希望这个教程看完能让你顺滑的使用 MCP。

目前支持 MCP 服务的客户端主要是 Claude、Chatwise、Cherry Studio 这种聊天客户端或者是 Cursor、Windsurf 这种 AI 编程 IDE。

由于我们面向的主要还是普通人，所以演示部分我主要演示Chatwise的操作，配置部分我会讲 ChatWise 和 Windsurf 的操作。

🎁

如果你想要购买 ChatWise 的高级版本的话可以使用藏师傅的优惠码「guizang」可以打八折

收藏后面看也行，记得先给点个赞👍或者喜欢🩷，谢谢各位了。

目录：

* 简单介绍什么是 MCP，以及他们的作用
* 配置 MCP 前的准备工作，主要是安装依赖
* 从哪里获取 MCP 服务
* 如何在 Windsurf 上配置 MCP 服务
* 如何在 ChatWise 上配置 MCP 服务
* 我探索出的实用 MCP 实用方式和对应参数
+ 从 FIgma 获取设计稿信息生成高还原度网页
+ 自己手搓丐版 AI 搜索（Exa&Time）
+ 利用 MCP 搭建 Obsidian AI 知识库
+ 利用高德 MCP 检索附近咖啡馆并创建网页
+ 从 arxiv 检索和下载论文
+ 与 AI 对话快速创建 Flomo 笔记

#### 什么是 MCP

这里不过多解释，毕竟我们只是使用而不是实现。

简单来说 LLM使用不同工具时，以前需要同时修改模型和工具，因为各工具的API数据格式不统一，导致适配成本高、功能添加慢。

MCP协议统一了数据格式标准，规定了应用向LLM传输数据的方式。任何模型只要兼容MCP协议，就能与所有支持MCP的应用交互。

这将适配工作从双向简化为单向（仅应用端），且对于已有API的应用，第三方开发者也可基于其API进行MCP封装适配，无需官方支持。

可以看下面 Claude 画的这个图，虽然糙但是也可以理解了，哈哈。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6dc8kianFBDMNb4fmM35rh3aicLD6hUcsrKUmY03ic0FYe6MkKjNEhIhqA/640?wx_fmt=png&from=appmsg)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6xvYN0rLkiaoMjm0icPapXPy0XgtpTcUiae3BLNfIDicoF63GiaDcwSshJPw/640?wx_fmt=png&from=appmsg)
#### 配置准备工作

注意：Windows 在环境配置和网络上的问题比 Mac 多很多，所以如果你没有编程经验并且是 Windows 电脑，出错不是你的问题，实在无法修复的话就算了。

MCP 现在一共有两种模式：

* Stdio：主要用在本地服务上，操作你本地的软件或者本地的文件，比如 Blender 这种就只能用 Stdio 因为他没有在线服务
* SSE ：主要用在远程服务上，这个服务本身就有在线的 API，比如访问你的谷歌邮件，谷歌日历等。

SEE 的配置方式非常简单基本上就一个链接就行，这个不需要教，如果你找到的是 SEE 的直接复制他的链接填上就行，而且现在使用 SEE 配置的 MCP 非常少，基本上都是 Stdio 的方式。

Stdio 的配置比较复杂，我们需要做些准备工作，你需要提前安装需要的命令行工具。

Stdio 主要需要两个开始的命令一个是 uvx 一个是 npx。

其中 uvx 我们需要安装 uv：如果你是 Windows 的话可以按“Win”键，点击搜索后输入"PowerShell"，然后右键选择“以管理员身份运行”，粘贴下面的命令回车就行，运行完记得重启。

```
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
```

如果你是 Mac 的话只需要点击你的“启动台”搜索“终端”应用，然后输入下面的代码回车就行。

```
curl -LsSf https://astral.sh/uv/install.sh | sh
```

接下来是 upx，这个简单我们只需要安装 Node.js 就行，访问官网（https://nodejs.org/）点击下载正常安装就行。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6gFhffF5H95CrmAyHKSnqZvwKwcj5ks7Uh4dGLuDUlMQ8mC2ia5NuMFQ/640?wx_fmt=png&from=appmsg)
#### 获取MCP

随着 MCP 的爆火已经有了很多 MCP 聚合网站，比如逗哥的 MCP.so 和 https://smithery.ai/ 。

他们的使用也大同小异，一般在左侧输入这个 MCP 必要的 API 之后就可以帮你生成各种导入的 Json 命令了。

API 如何获取一般在左侧的介绍部分也有有写，我后面的案例部分也会帮你介绍我用的几个常见 MCP 的 API 获取地址。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6VVCQcHXpILajQOibM90y1DzBGFvzqo17vHhjkeWLFBdgwBfkRgl3yaw/640?wx_fmt=png&from=appmsg)
下面的配置部分分别是 Windsurf 和 chatwise 的可以根据你使用的客户端选择查看。

#### 配置 MCP-Windsurf

完之后我们就得配置了，如果你用的是 Windsurf 的话，可以在右上角头像部分打开“Windsurf Settings”在里面找到

MCP Servers 点击右边的“Add Server”按钮添加。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6ian7w9MHvYsu8ibicVFMvePdermIoC600M9BN8ua4jXU6hdicQKIcsJ6BQ/640?wx_fmt=png&from=appmsg)
Windsurf 好的是他内置了一些常用的的 MCP 服务你可以直接用，同时由于是常见的 IDE 所以很多 MCP 网站都有适配命令很好找，上面获取 MCP 的部分我们已经说过了。

如果默认配置的没有你想要的 MCP 可以点击「Add custom sever」

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6pftDk9ggyEEibZ6L7IXTeDctmz10pMPNwbPYwz6YmD4XDac6R73ggiag/640?wx_fmt=png&from=appmsg)
之后你会发现他打开了一个文件，你只需要将从网站复制的 Json 填写进去保存就行。

这里需要注意的是，当你需要配置第二个的时候，不要将网站给你的 Json 直接放在第一个下面，只需要将下面代码块的部分去掉，将剩下的部分放到原来剩下那部分结束就行。

如果你实在不会，你可以把你的整个配置文件复制给 AI 让 AI 帮你改，只需要说“这个 Json 写法有什么问题吗？如果有帮我修复”就行，然后将 AI 帮你修复的复制进去，记得保存修改。

```
{  
"mcpServers": {  
  
}  
}
```

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6FuobRw8FYCCkFuEqfUUiaXdnn6Tf3TF73UMicHf4VV3Mz5dmTLa9OHOQ/640?wx_fmt=png&from=appmsg)
如果你配置无误的话应该会看到右边图示位置出现了你配置的 MCP 服务的名字，同时前面有个绿点，如果配置有问题的话就是红色的点。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6tzfMK8Uibz3JIwEaKrq3Js1RibxibfZau3sqw89EBEzd5oQvLm0pYxbww/640?wx_fmt=png&from=appmsg)
一旦你添加成功使用就很方便了，直接在右侧跟 AI 对话提出要求就好了，比如我这里就让他直接往我的 Flomo 笔记里面增加了一条笔记。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6K1TEXz5Zrf9sw2A4Eic7aibwMGoDNlo8vZLlU882EY7S5I3BHicQ4j8lA/640?wx_fmt=png&from=appmsg)
#### 配置 MCP-ChatWise

麻了，我昨天吭哧瘪肚教大家吧 Json 转成正常命令，结果今天 Chatwise 支持了一个新功能，可以直接从 Json 新建 MCP 了。

在新建的时候左下角加号点击后选择「从剪切板导入 Json」就可以直接导入你从 Windsurf 或者 MCP 网站复制来的 Json 了，省事很多朋友们。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6e3Ey3BC7c3gCArh8jrcSC3JdFxhDcba7BmH6s7xE7hZKghUzhTdCww/640?wx_fmt=png&from=appmsg)
接下来就是使用了，ChatWise 可以在输入框点击锤子图标后开启来开启 MCP 服务的使用，需要注意的是，在开启开关后还需要单独点击需要启用的 MCP 服务，有了对号代表着启用了。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6LYvQIPD7BARcvVEia418z5BgiauyPrMgjQNRp2n2oJx0NxjZRDSuXwqw/640?wx_fmt=png&from=appmsg)
这个时候我们可以直接在聊天窗口用自然语言跟模型对话，需要的时候他自然就会启动对应的 MCP 获取信息。

比如我下面这个问题他先调用时间 MCP 获取到了当前时间，然后调用 Exa 的搜索 MCP 搜索到了 Open AI最近的新闻。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY65IMd0tWd3NmNIXuObzjXHdia3gwcJ7efQUb6vpCicKpEr8sXBMiciaxsLg/640?wx_fmt=png&from=appmsg)
当然你还是可以看下面正常的添加流程。

AI IDE 对于普通人来说还是有点复杂，我们更多了解的还是 Chatwise 这种跟 AI 对话的工具，ChatWise 的配置比 windsurf 好找，难的是命令行。

点击左下角头像，然后在设置里面找到工具，在点击左下角的加号就可以添加。

可以看到我们需要填写四个东西：类型、ID、命令、环境变量。

类型这里一般如果 MCP 网站给的是上面的 Json 格式而不是网址的话就选 Stdio 就行。

ID 这个你可以自己起名字，一般标识一下这个 MCP 的服务名字就行。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6xSbtgv4MfaE5RtVIPSHaGBdL8Rw4g8a9xM86MzicNe35TV1MlAQusJg/640?wx_fmt=png&from=appmsg)
接下来的命令和环境变量，这里我之前也发愁怎么办，但后来我想了一个好办法，我们不是可以从 MCP 网站可以获取到 Json 代码吗。

只需要将 json 代码发给 AI 模型（Claude 或者 Deepseek）然后让他将这个 Json 转换为正常的命令，同时将环境变量单列出来就行，比如下面这个高德 MCP 的 Json 就成功的分离了，我们将命令和环境变量分别填写就行。

然后我们可以点击下面的查看工具按钮，如果调试没问题，就会列出具体的工具，如果调试有问题就会列出返回的报错，这时候我们就可以将报错发给 AI 看应该如何修复，一般是命令缺少参数或者环境变量的 API Key 填错了。

这个时候我们的 MCP 就配置好了，只需要点击右上角的开关就能启用，如果这个 MCP没有敏感风险的话，可以勾选自动执行工具，这样效率高点。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6Rh72UhHrJuSppnP9gskahprTnkHX4vFaWMST0mFnqfY6qcNDx0r9Nw/640?wx_fmt=png&from=appmsg)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6blDKseORdYAKIVcK2WCNuHxaQmhUDUJhMmLPuHSvXib1dsHcYtSicicww/640?wx_fmt=png&from=appmsg)
#### 案例和参数

接下来就是案例的部分我在这里会介绍和演示一些我用着还行的 MCP 服务用法，另外也会写上他们的来源和 Chatwise 中对应的参数，你可以直接添加，这些都是我试过能跑的。

##### 从 Figma 获取设计稿生成网页

使用的 MCP 是 Framelink Figma MCP Server：https://mcp.so/server/Figma-Context-MCP/GLips

```
## ChatWise 参数  
  
命令：npx -y figma-developer-mcp --stdio --figma-api-key=粘贴获取到的Figma API Key替换
```

这里我们需要去Figma 软件里获取一下我们的API Token，具体的步骤是：

1. 1. 点击左上角的头像找到设置（Settings）
2. 2. 然后找到安全（Seccurity）选项，找到创建新的 Token（Generat new Token）
3. 3. 之后File content选择只读，Dev resources也选择只读就行

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6hNiaE5HjWTiaNZkG1Nn1u21dNzUroaeBV09rKmmxsJhj4GOLmXuOQ95Q/640?wx_fmt=png&from=appmsg)
![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY648SW67fJBpK9HN8PD6An0ibTXz3vOiaJULDnwI7ZIOMaUbCBLJDm6Libg/640?wx_fmt=png&from=appmsg)
然后我们就可以在自己的 figma 设计稿里面找到一个画板然后在右键的复制里找到「复制到所选项的链接」。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6ZrhT3iaE6UftHyiatahUTpvkK3nJVWpxorNEpDWKFubfmNW6RfDw8EoQ/640?wx_fmt=png&from=appmsg)
最后在下面输入框中启用 Figma MCP 后把你复制的链接扔给他让他生成网页就行，这里推荐用 Gemini 2.5 Pro 或者 V3 0324，都可以白嫖。

具体白嫖方法可以看我之前写的《[顶级白嫖指南，教你低成本用上最好的 AI 模型，再加加上 AI 搜索和推理](https://mp.weixin.qq.com/s?__biz=MzU0MDk3NTUxMA==&mid=2247487434&idx=1&sn=32bea665b4877f21280b6a30a9dc6153&scene=21#wechat_redirect)》

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6yt5gN2sqf1vI2d5pmIvW1LuJgYNnBQZjKyW0ugX9oYHyeN5qKz3OSA/640?wx_fmt=png&from=appmsg)
##### 自己手搓 AI 搜索（Exa&Time）

接下来教大家自己手搓一个 AI 搜索，需要用到两个插件首先是 Claude 的官方 MCP 时间获取服务，因为在不联网的情况下模型是不知道现在的时间的，在搜索类似这几天的新闻这种问题的时候他就会瞎选时间。

下面就是 Time 这个服务的参数，很简单就是拉取服务，然后设置一下你的时区就行，我这里设置的上海。

```
## ChatWise 参数  
  
命令：uvx mcp-server-time --local-timezone Asia/Shanghai
```

再然后就是 Exa 的 MCP 了，这个也需要一个 API，但还好 Exa 的 API 是免费的，你可以直接去这里点创建复制就行：https://dashboard.exa.ai/api-keys

```
## ChatWise 参数  
  
命令：npx -y exa-mcp-server  
环境变量：EXA_API_KEY=用你申请到的 API 替换这段文案
```

都配置好之后我们就可以在输入框的位置启动这两个 MCP，进行 AI 搜索了，我这里会让他先获取时间再获取搜索内容，但 Cluade 这种模型应该不需要提醒他获取时间，其他模型他自己不获取的话可以通过提示提醒他一下。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6VyxiatA8GWvwwYrylc6pPRnEtdbGsc3icPeARThFsZp2rMPSCQ2EqVjw/640?wx_fmt=png&from=appmsg)
##### 利用 MCP 搭建 Obsidian AI 知识库

这里我使用的是Obsidian Model Context Protocol（https://mcp.so/zh/server/mcp-obsidian/smithery-ai）这个服务，可以检索你的 Obsidian 知识库的笔记让 AI 分析。

比较坑的是他们这个文档写的真实坑啥也没写，我好不容易才找到怎么用。

这里我们需要做个准备工作，先去 Obsidian 的「第三方插件」-「社区插件市场」找到 Loccal Rest API 这个插件安装并且启用/

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6rooh8PIIWicooTqPkMhAibOEKnuWd6R3umxU3WGYu9fV8TuQT3BdFgsQ/640?wx_fmt=png&from=appmsg)
然后我们就可以在 Loccal Rest API 这个插件的设置里面看到我们的 API Key 了。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6cfHTcRLWvnTiafObESkJm3otoiaDOznIqrANyiaEllL2a7f0wf63eskkw/640?wx_fmt=png&from=appmsg)
然后访问这个链接：https://coddingtonbear.github.io/obsidian-local-rest-api/

点击右下角的「Authorize」输入刚才的 API Key 启用服务就可以了。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6NPyk53l3Zc899eeJv66SncK0dtSt9AbEia5r7BibdaqHgIakoic1fen4g/640?wx_fmt=png&from=appmsg)
还需要准备一个东西就是你的 Obsidian 仓库本地文件位置，我们需要点击 Obsidian 左下角你的仓库这里的管理仓库。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY679Baq3kvzGoDFEfTnA4lWL5YfibRBMBvuVs9PfV260HcgmC68FUeibyw/640?wx_fmt=png&from=appmsg)
然后选择在「访达中显示仓库文件夹」，找到文件夹之后按住「Option」键右键选择复制这个文件夹的路径，记下来备用。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6SGdH6lVuW1FBxcp5xgeMFZZ5IIpZO7Flh5EEA1O4cnRSYqqMicoGI3g/640?wx_fmt=png&from=appmsg)
这时候我们终于可以回到 Chatwise 中了，

```
## ChatWise 参数  
  
命令：uv tool run mcp-obsidian --vault-path 替换为你Obsidian仓库的文件路径  
  
环境变量：OBSIDIAN_API_KEY=用你Loccal Rest API插件中的Key替换这段文案
```

最后在 Chatwise 输入框启用 MCP 服务后就可以让任何 AI 模型检索你的内容了，比如我这里让他在指定文件夹查找关于 MCP 的文章，然后可以基于这些文章做 AI 知识库。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6TD3wia2I2bv1V5jKvfd6lB588pNAhMT1JjgKhwluo9mf28zRfDnCicdQ/640?wx_fmt=png&from=appmsg)
##### 利用高德 MCP 检索附近咖啡馆并创建网页

接下来我们用高德的 MCP 搜索一下家附近的咖啡馆，而且用我之前的网页生成提示词给搜索结果做的展示的网页，点子主要来自 @AI产品黄叔 。

首先我们需要去高德（https://console.amap.com/dev/key/app）申请一个个人的 API，需要按要求完成个人开发者的认证。

之后在控制台的「我的应用」这里创建应用就行，创建完成之后选择需要创建 Key 的应用，点击【添加 Key】，表单中的服务平台选择【Web 服务】，之后复制你的 Key 备用。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6IYNasJzRLHhvS44NnIx96V1wxmbYrueH6aiaLHZPicsL08hBwycOh74A/640?wx_fmt=png&from=appmsg)
接下来就是在 ChatWIse 填写下面的命令和环境变量，注意在环境变量这里填写你申请的 Key。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6hVLu7hwIHOV4ibMr7zaufNSzKBXOwvVutNbk7DjWQkSdnoZTzEqvs1w/640?wx_fmt=png&from=appmsg)

```
## ChatWise 参数  
  
命令：npx -y @amap/amap-maps-mcp-server  
  
环境变量：AMAP_MAPS_API_KEY="用你申请的高德API Key替换这段文字"
```

然后在聊天窗口开启高德的 MCP，先让他获取你家附近的经纬度，然后让他获取附近的咖啡馆的详细信息。

之后我们就可以用我在《[为了让大家一键生成更漂亮的可视化网页，我写了个工具！](https://mp.weixin.qq.com/s?__biz=MzU0MDk3NTUxMA==&mid=2247487756&idx=1&sn=7855c80ee6e4dae521081745de90e95d&scene=21#wechat_redirect)》写的提示词让他生成一个家附近的咖啡馆展示网页了。

##### 从 arxiv 检索和下载论文

AI 从业者应该都会经常看论文，那天发现arxiv都有 MCP 了，而且除了查询之外还可以自动下载论文的 PDF 版本。

这个直接使用命令就行，不需要环境变量，Windows 用户注意需要把 Path 的路径换成你想要保存论文的文件夹。

```
## ChatWise 参数  
  
命令：uv tool run arxiv-mcp-server --storage-path /path/to/paper/storage
```

然后我们就可以愉快的检索和下载论文了，比如这里我们就让 MCP 从 arxiv 获取最近的 10 篇 AI 相关论文和介绍。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6AjcOnkXhibAsM0CRQLNyia5DB5Fqic3SqLumGHkFwaIoup9c89jsXaRHg/640?wx_fmt=png&from=appmsg)
##### 与 AI 对话快速创建 Flomo 笔记

虽然有很多笔记软件一些短的笔记还是用 Flomo 用的顺手，那天看到逗哥写了一个 Flomo 的 MCP 服务。

这样跟 AI 对胡生成的很多不错的结果都可以直接保存了，而且可以结合其他 MCP 使用，比如将上面 Exa 的搜索结果直接保存到我们的 Flomo 账号。

我们需要提前从你的 Flomo 设置里复制你的 API 链接，准备配置的时候放到环境变量里面，直接在这个页面复制就行：https://v.flomoapp.com/mine?source=incoming\_webhook

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6lOXhzthz51Y6yl5Sc43McwSfF87jfM8icjSsxGXvvX89ClGPOu6A90Q/640?wx_fmt=png&from=appmsg)
然后就是在 Chatwise 填写对应的命令和环境变量。

```
## ChatWise 参数  
  
命令：npx -y @chatmcp/mcp-server-flomo  
  
环境变量：FLOMO_API_URL=更换为你从Flomo获取的API链接
```

你还可以要求 AI 在保存 Flomo 的时候带上你对应的标签方便管理，麻了我标签字打错了。

![Image](https://mmbiz.qpic.cn/mmbiz_png/fbRX0iaT8EgdOzqos18VaY5vmxzSWoUY6hFTVnygDn93KqvUtKDwgK3alic9rv1K39icF0XhGnpuETqIMsDcdLogw/640?wx_fmt=png&from=appmsg)
好了今天的教程就到这里了。

MCP协议的出现，标志着AI工具生态正从"封闭花园"走向"开放广场"。

如同互联网的HTTP协议统一了网页访问标准，MCP正在统一AI与工具的交互方式。这种统一不仅是技术层面的进步，更是AI使用范式的革命。

MCP的意义在于将AI从"被服务"的角色转变为"主动服务"的角色，让普通用户也能定制专属的AI工作流。

半年前，只有大公司才能打造的AI能力，现在正逐步开放给每一位有一点技术思维的普通人。

然而，MCP目前面临的困境也值得我们思考：技术的民主化与易用性之间总是存在矛盾。一方面我们希望技术足够开放和灵活，另一方面又希望它足够简单直观。

这种矛盾在每一项新兴技术发展初期都会出现，但随着生态的成熟，这种矛盾终将被解决。

希望今年能看到只需在AI助手中点击"安装工具"，能能使用的 MCP。

作为用户，不妨现在就开始尝试这些看似复杂的技术。即使过程有些曲折，这种尝试本身就是对未来的一种投资。

在AI时代，熟练使用工具将成为我们的核心竞争力，而了解底层原理将让你比他人更早一步掌握未来。
