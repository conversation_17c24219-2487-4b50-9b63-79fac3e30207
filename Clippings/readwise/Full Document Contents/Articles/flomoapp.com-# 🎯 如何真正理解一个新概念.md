---
人员: 
  - "[[flomoapp.com]]"
tags:
  - articles
日期: None
时间: None
链接: https://help.flomoapp.com/thinking/meaning.html
附件:
---
## Document Note

## Summary

## Full Document
真正的知识体系不在 flomo 中，也不在 Notion 中，不在任何一个笔记工具中，而是在**你的脑海中。**

个人知识网络的建立，往往是这么来的 ——

![](https://imgproxy.readwise.io/?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/meaning001.png%21webp&hash=0490973b19a39f63eecbfe6572f7cdd2)
起初，我们在生活中接触到许多新鲜的概念，比如知道了某个单词（如 知识管理）。

这时候应该把他记下来，然后和现有知识建立起一点点关联（这个是研究知识管理时候碰到的概念）。**如果没有迅速记下来，并且和已有知识建立起连接， 那么很快就会忘记。**

`第一次接触到「知识」的定义👇🏻`

![](https://imgproxy.readwise.io/?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/meaning002.png%21webp&hash=fc1ff764ff32fe59768cc0ae54aae9b4)
但仅仅是记下来还不够，我们还需要花时间去理解。 在过程中不断地去寻找这个单词背后的意义，和已有知识进行关联，才能让我们在接下来的时间去使用它。

对「知识」这个概念的不断学习👇🏻

![](https://imgproxy.readwise.io/?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/meaning003.png%21webp&hash=11d22f506925bcf4efe001719baf0e05)
在反复不断地**使用**过程中，我们会进一步将这些概念和已有的模块合并，进入到「模型」阶段。这个时候我们才能够轻松快速地使用它，甚至是自动地使用它。诸如围棋大师，寿司之神，便会有类似的操作。这时候才能说我们**精通**了。

不断积累之后产出的「知识管理」模型👇🏻

![](https://imgproxy.readwise.io/?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/meaning004.png%21webp&hash=633f505e07e70538cccac974a0ac5731)
所以，知识和理解是同一事物的两个部分 —— **知识是点，是结网的基础；理解是线，用线才能把点串起来。**

在某种意义上，任何知识都有某种程度的意义。**当我们真正依赖这个概念产生决策或行动时，我们才是真正的理解了。** 反之，我们的大脑只能在有限的时间内保存任意的信息（例如一个新词）。当它不再有用的时候，它会迅速消失，而当它有用的时候，它就会固定下来。

这个过程中，有一些其他要点需要注意 ——

#### [#](https://help.flomoapp.com/thinking/meaning.html#_1-先把基础概念记下来) 1. 先把基础概念记下来

我们通过不断学习新的概念（即知识点）来扩充我们的知识网络。

下图是一个典型的学习新概念的理解过程

* 吸收新概念（乘法）
* 将现有的知识与新概念联系起来（乘法和加法的关系等）
* 它们之间的联系有意义（在什么情况下该用乘法）

![](https://imgproxy.readwise.io/?url=http%3A//flomo-resource.oss-cn-shanghai.aliyuncs.com/101/meaning005.png%21webp&hash=bfaaa6a1a052be4c89b95f55742bee3f)
如左图，你不能教一个完全没有「数学」概念的小朋友学习「乘法」。至少他需要知道「数字」、「加法」、「数学的基本功」、「加法符号」等知识点，并且这些知识点在大脑中形成基础的网络，才能开始尝试学习「乘法」这个概念。

而当我们开始学习「乘法」的时候，它会和许多已有概念进行关联。但这时候连接还比较弱，我们需要不断地做习题，向老师提问，让这些知识点的连接更加牢靠。最终如右图一样，固化在你的知识网络中，这样你下次遇到同样的问题，就可以使用「乘法」这个概念，而不用仅仅只用「加法」来解决问题了。

**请注意！**

**我们大脑中的知识，**

**并不是以「书本」「文章」的形式来存储，**

**而是一个一个知识点。**

这也是为何 flomo 一直强调写卡片的原因 —— 在我们写出华丽的文章前，先把基本概念记下来，后续才能在脑海中形成网络。

#### [#](https://help.flomoapp.com/thinking/meaning.html#_2-不要着急-花一点时间理解新概念) 2.不要着急，花一点时间理解新概念

信息不像实物，并不像一瓶水或者一个包，买到了就可以立即消费。如果你没有花时间去理解，那么这些信息对你来说没有任何价值。

这也是为何 flomo 很反对收藏的原因 —— **因为我们没有花时间去理解这些信息**。

除了偷懒之外，**过于勤奋**也会带来一些副作用。

许多时候开始学习陌生的领域， 还没有摸清楚主要脉络的时候，一头扎进去（比如买很多书或者课程），会让自己有一种窒息感。各种陌生的概念涌来，每个字拆开都认识，合在一起并不知道他们在讲什么。

比如我刚进互联网医疗行业的时候，下面一句话足以让我吐血（我猜正在看这篇文章的你也会很懵）：

> 明天看下这个 4+7（的药）有没有原研，但也不能太多一品双规就可以，顺便问下国准为什么有重复。
> 
> 

先卖个关子，放在文末解释这句话是干啥的。

但这也是我们为何从来不希望你一把梭从其他地方导入数据的原因：**因为这会引发概念海啸，让你大脑过载。**

在构建自己知识体系的时候，不能着急，反而要慢下来着重**理解每个新概念。**

**因为你的思考速度是固定的，你不能强迫自己思考的更快或更慢。**

#### [#](https://help.flomoapp.com/thinking/meaning.html#_3-用你自己的话写-理解多少写多少) 3.用你自己的话写，理解多少写多少

真正的理解，表现为在现有背景知识的基础上，以及在明确学习和实践的有意义的联系上，那些能实现的行动。

比如当你真正理解「价值投资」这个概念时，不是在你放下书本时，也不是在开始买股票时，而是在经历了过山车一样的周期时的那种纠结最终熬过去时，才真正理解这个概念 —— 这也便是知行合一的意义。

这也是为何 flomo 总是鼓励你：**用你自己的话写，理解多少写多少。**

而这背后的基础是，**你要对你自己诚实。**

诚实就是承认自己的无知，然后把基础的事情搞搞清楚，然后再在上面建立新的知识结构。

我们想要抵达某个目标的时候，如上图效率是最高的，但是地基却是不稳的，和其他知识块建立的连接很少。这就像某些面霸候选人，只是把答案背了下来。把他放在具体的情境里面，就能看到大型翻车现场。因为他只看到了局部，根基不牢。

**诚实很重要。**

会背诵九九乘法表，不代表就懂得乘法。会念两句阿弥陀佛，不代表就懂佛经。这无关乎人品，而是在于我们学习的成果。

解释：

* 4+7 = 政府带量采购医保药品，会导致药品价格大幅下降
* 原研 = 原创性的新药，相对来说的是仿制药
* 一品双规 = 同一通用名称药品的品种不要超过两种
* 国准 = 国药准字，药品生产批准文号
