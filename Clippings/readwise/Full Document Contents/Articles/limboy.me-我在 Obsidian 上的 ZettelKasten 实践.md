---
人员: 
  - "[[limboy.me]]"
tags:
  - articles
日期: 2022-10-13
时间: None
链接: https://limboy.me/posts/obsidian-zettelkasten/
附件: https://fav.farm/👻)
---
## Document Note

## Summary

## Full Document
用过不少笔记 App，都各有所长，但却很少有长期使用的，这其中我觉得更多的是自己的问题，没有找到适合自己的笔记系统。终于在尝试又一款笔记 App 失败后，打算认真地研究下这个问题。

经过一番比较后，感觉 ZettelKasten（以下简称 ZK）的理念非常吸引我。

 关于 ZettelKasten 可以看下[这篇文章](https://zettelkasten.de/introduction/zh/)的介绍。简单来说有这么几个要点： * 笔记要原子化
* 用自己的话描述
* 笔记间建立连接

 原子化笔记，就是每条笔记就讲一个点，之后可以被不同的文章引用。就像一个氧原子，既可以跟氢原子结合生成水，也可以跟碳原子结合生成二氧化碳。

 然后这个笔记不能单纯地 Quote 书或文章里的内容，这样就不是你的笔记，印象也不会深刻，要用自己的话来描述，再附上出处（比如书名）。

 笔记间要有连接，且这个连接要自然，要有上下文（方便反向链接查看）。如果无法自然地链接，说明笔记之间的连接不紧密的。 

然后就看哪个笔记 App 对它的支持最好，我对移动端以及同步没有需求，但要 Local First，并且数据不能是私有格式，方便在 App 之间迁移，这样一来可选的就不多了，主要是 Obsidian 和 Logseq，虽然之前 Logseq 用得比较多，但感觉还是缺少打磨，这次正好来尝试下 Obsidian。

经过几天使用后，目前感觉还不错，无论是 UI 还是功能，昨天还发布了 [1.0 版本](https://obsidian.md/1.0)，增加了对 Tab 的支持。下面就来说说我的使用姿势。

经过 Logseq 的洗礼后，已经离不开 Daily Journal 了，所以这个选项一定要开启。接下来的问题就是如何将 Daily Journal 与 原子化笔记 相结合。一开始想的是，在 Journal 里可以建多个 Block，Block 之间可以相互链接，还可以给 Block 打标签。结果是我想多了，Obsidian 只支持 Page 之间的链接。

一番琢磨后，发现可以这样：一个 Page 是一个原子化笔记，然后在 Daily Journal 里去引用这些笔记。除了这些引用外，Daily Journal 里还可以随意地记录一些 Ideas 或 Thoughts：

```
// 随意记录一些今日的所感所想，方便将来回顾
Obsdian 换了个 theme 后，用下来感觉还不错，想要的功能基本都有了，实践一下新的笔记系统。

// 链接到今天创建的笔记
- [[ZettelKasten 卡片盒笔记法]]
- [[我的笔记系统]]
```

按照 ZK 的理念，每条笔记会被拆分为几个部分：

![](https://limboy.me/posts/obsidian-zettelkasten/complete-zettel.png)
在 Obsidian 里，就是这样的：

![](https://limboy.me/posts/obsidian-zettelkasten/1.webp)
1. 每个 Page 都有 Tag。这个 Tag 看着简单，但其实还挺花心思的，因为我还让这些 Tag 实现了 Category 的功能，而构建 Category 是很费功夫的（想象下图书馆的图书分类）。Obsidian 支持 Nested Tags，这个对于 Category 很有帮助，比如 `#programming/language/javascript`，通过 `#programming` 或 `#programming/language` 都可以被关联到。
2. 这个就是用自己的话描述某个 Topic。在描述过程中会再去回顾相关的内容，加深印象。内容尽量与其他 Page 有关联。
3. 描述这些内容参考了哪些文章或书。
4. 反向链接。可以看到哪些 Page 链接到了本页，还能看到一些 Context。

这里很重要的是「用自己的话描述」和「建立连接」，前者迫使你去回顾知识，看是否真的理解了，后者需要进行些发散，一个知识点被多次连接后，印象就会深刻。这些连接，结合 Obsidian 的反向链接能力，可以让相关内容被更好地聚合。

我还有个习惯，会将书、文章甚至视频也放进来（统一放到 Assets 目录下），然后在 Page 的末尾附上内部链接（如 `[[Assets/how-to-read-a-book.pdf]]`），这样就不用再到别处去找参考内容了。

 对于文章，我会用 [Clear Reader](https://chrome.google.com/webstore/detail/clearly-reader-your-missi/odfonlkabodgbolnmmkdijkaeggofoop) 这个 chrome 插件来提取文章内容，并下载 PDF。如果是 youtube 视频，会用 [yt-dlp](https://github.com/yt-dlp/yt-dlp) 来下载。 
以上就是我在 Obsidian 上的 ZK 实践，目前感觉还不错，如果你也有这方面的心得，欢迎交流。

![](https://gravatar.loli.net/avatar/eec8d130a4cf616d85ca6ec71c968880?d=monsterid)
![](https://gravatar.loli.net/avatar/4bac306658fbb7b7d05ef309443d95b7?d=monsterid)
![](https://gravatar.loli.net/avatar/0ac816bef45371e1f05f3c6fa651d018?d=monsterid)
![](https://gravatar.loli.net/avatar/b475a93576d80d0a8adb46b794a950b9?d=monsterid)
