---
人员: 
  - "[[V2EX]]"
tags:
  - articles
日期: 2022-10-17
时间: None
链接: https://www.v2ex.com/t/887582
附件: https://cdn.v2ex.com/avatar/c5a2/8d86/583635_xlarge.png?m=1687739725)
---
## Document Note

## Summary

程序员 - @xiaodongxier - 经常收到朋友发的拼多多助力，无意间发现一个问题，就是拼多多是怎么做到分享的助力链接域名不是自己的呢？1. https://surl.amap.com/mxNiRlg1d3z2. https

## Full Document
|  |  |  |
| --- | --- | --- |
|  |  |  4 **[Exdui](https://www.v2ex.com/member/Exdui)** 323 天前 25 .只需要上传一张图片，引导用户复制链接、打开拼多多.任何可以上传图片的图床，都可以被他拿去当成口令（链接=口令）.App 取用户的复制链接，根据链接找找是不是口令，是的话就打开对应页面微信封的话，是封这些图床地址 /服务，跟拼多多彻底无关联。  |

|  |  |  |
| --- | --- | --- |
|  |  |  22 **[Seulgi](https://www.v2ex.com/member/Seulgi)** 322 天前 1 先生成引导引导用户复制链接的图片传到各云厂商的 oss, 链接上关联上一个分享 token. 再把这个 oss 链接给到各短链服务商生成短链, 给到用户分享. 那这样看, pdd 有一个 oss 聚合平台, 有一个短链聚合平台, 自己不做短链, 直接聚合世面的短链就行了?  |

|  |  |  |
| --- | --- | --- |
|  |  |  49 **[jim9606](https://www.v2ex.com/member/jim9606)** 322 天前 看链接的内容没看出直接关联。我想到的方案是利用 URL 的最后一段，用特殊方法编码跟踪 ID ，客户端通过剪贴板得到 URL 后尝试解码出跟踪 ID 。这样即使整个 URL 是无效的也不影响分享，如果无法控制白名单地址显示的内容就最好是构造成非法地址避免意外跳转。不过感觉这个方案似乎没有比淘口令那种好多少。  |

|  |  |  |
| --- | --- | --- |
|  |  |  70 **[xiaodongxier](https://www.v2ex.com/member/xiaodongxier)** 322 天前 @[yuzo555](https://www.v2ex.com/member/yuzo555) 普通非技术的朋友发的，并且是非技术的朋友本人助力的，之前也有这种情况当时以为是拼多多第三方合作实现的？可是现在想想微信封禁那么严格难道第三方就不怕被封？所以很好奇如果是第三方合作，第三方是怎么想的？如果不是合作，那么拼多多是怎么做到的？难道第三方不知道？  |
