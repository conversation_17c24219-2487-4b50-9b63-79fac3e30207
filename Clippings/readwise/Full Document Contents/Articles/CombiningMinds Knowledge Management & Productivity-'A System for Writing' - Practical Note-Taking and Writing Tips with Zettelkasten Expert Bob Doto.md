---
人员: 
  - "[[CombiningMinds Knowledge Management & Productivity]]"
tags:
  - articles
日期: 2024-09-10
时间: None
相关:
  - "[[ideas]]"
  - "[[essays]]"
  - "[[writer]]"
  - "[[article]]"
  - "[[journey]]"
  - "[[thinker]]"
  - "[[writing]]"
  - "[[bob doto]]"
  - "[[feedback]]"
  - "[[sentence]]"
  - "[[outlining]]"
  - "[[paragraph]]"
  - "[[discipline]]"
  - "[[narratives]]"
  - "[[note-taking]]"
  - "[[smart notes]]"
  - "[[conversation]]"
  - "[[productivity]]"
  - "[[zettelkasten]]"
  - "[[知识管理]]"
  - "[[collaboration]]"
  - "[[combiningminds]]"
  - "[[literature note]]"
  - "[[individual notes]]"
  - "[[writing processes]]"
  - "[[structured writing]]"
  - "[[knowledge management]]"
  - "[[spiritual background]]"

链接: https://www.youtube.com/watch?v=8Pfya5NI1tU&t=34s
附件: https://i.ytimg.com/vi/8Pfya5NI1tU/maxresdefault.jpg)
---
## Document Note

# 如何有效地进行写作与笔记？
  - 强调理解写作过程及其结构化的重要性。
  - 提出在写作中需明确保留哪些信息及如何思考整合。
  # 如何利用Zettelkasten方法提升笔记质量？
  - 介绍Zettelkasten方法在笔记整理中的应用。
  - 强调个人化的注释和思考在阅读与写作中的重要性。
  # 如何从阅读中提炼写作灵感？
  - 强调在阅读时及时记录灵感和要点的必要性。
  - 讨论如何将不同文献中的想法进行整合，以支持自己的写作。
  # 如何形成有效的写作习惯？
  - 讨论写作过程中的反思及回顾笔记的重要性。
  - 提出将写作与个人经历相结合，形成独特的写作风格。
  # 如何在写作中保持灵活性与创造力？
  - 提出在写作中保持开放心态，以促进创造力的发展。
  - 强调个人背景和经历对写作内容的影响。
  # 如何将反馈融入到写作过程中？
  - 讨论如何利用他人的反馈来改进自己的写作。
  - 强调在写作过程中，与他人合作的重要性。

## Summary

本文作者结合与作家 Bob Doto 的对话，探讨了写作与笔记整理的实用技巧，尤其聚焦于 Zettelkasten 笔记法在写作中的应用。作者强调，写作不仅是输出文字，更是一个持续思考和结构化思维的过程。通过阅读和做笔记，将零散的想法记录为独立且富有意义的“单一概念笔记”，有助于后续的整理和写作。作者提到，写作时应合理筛选信息，避免无用内容堆积，同时借助笔记系统不断回顾与整合思路，提升写作的深度和质量。

此外，作者分享了自己从阅读到写作的转变过程，强调写作是一个循序渐进的习惯，需要不断反思和调整。通过将笔记转化为文章或论文，作者体验到写作的实际价值，并逐渐形成自己的写作框架和风格。文章还提及，写作不仅是知识输出，更是自我探索和叙事重构的过程，帮助作者更好地理解和表达内心的思想。整体来看，文章鼓励读者将笔记与写作紧密结合，利用科学的笔记方法提升写作效率和内容质量。

---

<strong>问题 1：</strong>  
为什么将笔记拆分成“单一概念笔记”对写作有帮助？  

答案：  
拆分成单一概念笔记可以让每条笔记独立表达一个核心思想，便于后续查找、整合和重组，从而更高效地构建文章结构和深化主题。

<strong>问题 2：</strong>  
作者如何看待写作与笔记整理之间的关系？  

答案：  
作者认为写作和笔记整理是相辅相成的过程。通过系统的笔记整理，可以持续回顾和激发新思路，帮助写作变得更有条理和深度。

<strong>问题 3：</strong>  
写作过程中，作者建议如何处理大量的阅读信息？  

答案：  
作者建议合理筛选信息，只保留有价值的内容，并通过做笔记进行归纳总结，避免积累无用信息，确保写作材料的质量和针对性。

## Full Document
if your digital not taking system looks like a chaotic mess then this video is for you because so does mine showing my personal log seek database is not something that I do very often but I think it's probably worthwhile for people to see just how messy things are in the background I recently published a long form essay and I'm very interested in how to improve this whole input to Output worklow our notes are only as good as the functions that they serve and for me I'm wanting to get more into 

writing so I'm starting to speak to a couple of writers to understand their processes and how they take notes make notes and transform that into output today I'm chatting to Bob doto who is the author of a system for writing he is a proper zcast and expert and he's literally written a book about it he's also been the moderat forums online for a number of years and his book is really really practical when I started using log seek for digital not taking I read 

AR's book how to take smart notes which is about Nicholas Lumen zle cting methods but it left a lot out in terms of the actual how to build the system Bob's book is a really practical guide to building a zetel Casten now full disclaimer I do not call my system a zle Casen I've spoken about how I call it a messy Cason because I trying I'm trying to do a number of different things in one place building a second brain a zle Caston no taking random things all hudg 

Pudge thrown in there I found this interview with Bob to be really encouraging I basically turned it into my own personal coaching session asking him about some of the things that have come up in the recent writing processes for me and he really has got some good practical perspectives what to retain what not to retain how to even think about the whole writing process there's little tidbits of wisdom scattered throughout this video and I really think Bob brings a very practical approach I've linked to his book in the notes below and I hope you enjoy the 

discussion I put a note in my preparation which was like the year 2020 Bob doto releases his book ahead of son irin's like how to take smart notes and I feel like the world would be a very different place like a lot less confused and a lot less um yeah like the whole thing about literature notes and reference notes and fleeting notes like I feel like your book has really simplified this for a lot of people um well I like I sort of I call it my messy cast where I just like have this like 

random scattered approach and after reading your book I'm trying to be more structured and trying to like bring in a couple of those things because I'm I think I'm trying to actually go to like another level of writing which is like writing long form essays and potentially writing a book and it's very different to writing like short form essays and I call it like this recursive productivity space like I write essays about productivity or I write this it's like yeah but writing about like meaningful 

topics to me these like you know chewing on on difficult discussions like the essay I sent you and I Al I also I mean feel like we go down rabbit holes about your spiritual background as well because there's a whole lot to be said there but yeah so so that the transition to like much longer form writing and needing to be a bit more structured about it um I feel like your book does a really good job of putting in really actionable steps I mean those summaries at the end of every chapter are I don't know why every book doesn't do that but 

yeah but but I saw something you you said the other day about like about son irons and something around the confusion there so maybe just to speak to that before we get into it because like the other thing is I've got like a lot of questions like there's a couple ways we could go down this almost one where I'm like I show you how I'm doing things and I don't do this very often on on the channel because I'm so scared of like showing like potentially showing some random personal notes but like I've just decided stuff it I'm going to be a risk 

and because I've got I've got so many notes but the yeah maybe we'll get into that just now but like I just wanted to T to the the sun Iron's discussion and yeah the timing of your book yeah so sun is a really interesting because the book is very useful very valuable um and it obviously has been very helpful and informative for a lot of people like that is that is the Gateway way drug for for zedcast and 

that type things you know they people come across that book and that's where they enter and then they often come out of it with a lot of questions part of that as I understand it because I've talked to a few people who read it in German like first like they were German speaking and they saw the book and read it in German and it seems to be less confusing in German than it is for the English speaking audience in part that's due to how terms like literature note and 

fleeting note are rendered in English like in English in German those as I understand it those are they read more like descriptors like you know a literature note like you know not a literature note as a thing but a note that you take while reading and rather than saying every time a note you take while reading or a note you take while reading to in order to get XYZ on paper so you know he just calls a literature note and or a fleeting note for some else in English 

these read more like objects like a like a very distinct object now there's a reason why that's the case and part of that is because Nicholas Lumen took very distinct kinds of notes he did take a literature note he just didn't call it that he took a a kind of note that he took while reading and it looked a certain way the paper was oriented differently than the rest of the notes so it is a distinct 

object the the problem with with sunka eron's book is that he uses these terms inconsistently right so he has this term permanent note which sometimes he uses to refer to all notes that are permanently stored in the zedal Casten meaning literature notes and and Main notes the the main single idea notes but sometimes he uses the term permanent note separate than literature note um and that has 

caused wild confusion in in this little micro culture just just so much confusion um and the fact that as far as I could tell he really didn't go out of his way to help people figure that out just perpetuated it you know that little the Tweet you're referring to you know I talk about what what happened you have inconsistencies in erands you have but you also have people who aren't rereading I read Aaron's like six times 

I read that book because I really wanted to understand what he was saying and see how it appli see how it related to Nicholas lum's practices which is pretty mirrored I mean aens gets it it really talks about what Lumen did you know he basically bases it on what Lumen said so it's it's pretty accurate um so you know but so people read it once they're a little confused but they have a sense they read an article online that article got it wrong they write their own article because that's the 

world we live in where everybody writes the Articles and then you know it just self- perpetuates and then you had what I called in the like OG's like the people pre- arand who weren't taking those questions seriously you know these people people are being like what's a literature note I can't figure out a literature note and then you have the de form being like I don't name any of my notes they're all just notes whatever and it's like that's not helpful these people have real questions and they're coming to through a very specific lens 

which is Aaron and you're not you're not engaging their questions you're just saying just don't worry about it do this like that's you're thinking wrong about this and it's like that's not how you teach I've been teaching a long time it's not you don't dismiss people's questions right so the difference to get to your one of your questions the difference between where my book fits in or the difference between my book and Erin's book is that I I answer those questions and people find the book 

helpful because I paid attention to their questions for four years and I wrote down those questions and I answered I answered what is a literature note like 150 times in a four year perod you know I just kept answering it and I didn't care because it helped me like every time I answered it I used language slightly differently I hone my language like I really wanted to be able to speak about this so people could get it because I it's like a puzzle to me it's like solving a puzzle so yeah so that's that's what separates that book 

and I think that's why people maybe like you or or other people find it helpful is because I speak to those questions people have those questions so I answer them or at least I give them a framework on how to understand the question and other places they can go to see different takes on it you know yeah I'm just I'm I'm scrolling through the notes at the bottom I took on the book here like are you are you okay for me to share my screen which will have like we'll probably have quite a few excerpts of the books and stuff but like course um yeah there's lots of gold in here I've 

got I've got a few sec let me actually just share and then I can don't have to be looking at the bottom screen here um I'm just going to move this panel because the last time I did a recording it did something funny but you can see when this eventually goes to full screen why is it not going to full screen looks fullish yeah but it's not it's not doing the bottom part 

usually the shortcut it must be something with zoom okay give it a second to think I closed all the the things that I had open because it was pretty um had lots of tabs open earlier but okay there we go on my end it looks pretty full yeah great there you go so you can see I've got this like the section which is like great advice which I've taken out as like um yeah B basically some of the tidbits on um that 

that really resonated with me I think the first one here is like cut out the first paragraph no need to wait around while the right clear yeah some she told me that but then the other part which I love is how you've thrown in all the spiritual wisdom here it's just like as your examples in the book you speak to these different things I love how you did that um like yeah then the note examples were my way of like infusing the book with things I'm into that I'm nothing to do with the book but 

I mean it's great I like it as I said that's how I discovered you was through your Ching to aen hant about PKM and spirituality and then you you recommended the book um Cho tra the way of meditation myth of Freedom um myth of Freedom yeah yeah mindblowing um but yeah I think I think where where where I'm feeling inclined to do is is take you through my actual question 

um and yeah we can just sort of run through it because you know it's now live in in my log seek database and I yeah I've tended to do videos I guess before where I speak to things and and just go shoot from the hip so let's see how that goes um yeah definitely I love I love the things to do section which was you know that that practical I'm going to go do this afterwards like I was so well done and I actually went and if I open the system for routing in the 

back here like I screenshotted all of those parts um and they are now available in my in my summary here so that's pretty cool but that's great to hear because those I was unsure about those I I'm not a howto writer or at least historically I've not been a how-to writer so those are really howto and I I went over those so many times to be like am I saying am I telling people 

what to like the same things that are in the chapter am I like I was really confused for a minute on whether those were going to be effective but I have heard obviously you and then a few other people were like man those sections at the end were so helpful and I'm like oh great but that was not my that wasn't the thing I felt strongest about so I'm glad those are actually helpful yeah I yeah really appreciate them I think there was something which you spoke to which was like full full gazettal is not an outline and I was like ah that makes a lot and like how different things in 

alph numeric sequence aren't necessarily related because I in my one of the videos that I did I used this properties like when I was still figuring out log seek I used properties like this and saying this is a drill down and this is a continuation from and I realized that that like a unnecessary complexity like I think it's something about just saying this relates to this in this way via text and like trying to like impose too 

much structure with a system or like templates whatever it just wasn't working for me um and also uh I don't work in Alpha numerics anymore well I tried to and I was like oh this is just doesn't flow for me like I I think there's something you mentioned U friction in the book and how good that is to like slow you down and make sure that he's thinking through these things but for me I have like a thousand thoughts and maybe like let's bring up this thing here like literally thousands of ideas this is how I really 

started with log Seeker I just like Drop ideas in I had thousands of fleeting notes at one stage I had like 300 and I use fleeting notes in the same context as you do like where it's just like a thought just like a a random popup and I just didn't know what to do with them and then I started building what I called scratch pads but I think you know most people know of as's um so if I just scroll down here now like here's my one on productivity and PKM um and I just started dropping 

all those notes into these pages so the beauty of log seek for me versus obsidian native is that it's like the outline of just being able to drag and drop things and move them across and then reshuffle them really really helped but like what your book encouraged me to do is to think more about structural notes in between um so for instance like you know this effective strategies for personal Knowledge Management is only as 

a result of of of reading a book to say like actually go and spend some time reading those things but I initially didn't like that and maybe I'm sorry I'm jumping around so much here but I think that's okay um and it was because of this example here notes on life design we initially all of these were separate notes as in separate pages in obsidian as they would be or like in log seek but then I I wanted to be able to access 

them all at one time and like be able to scroll through them and read them so having them all as like link Pages you know if I go and create and say turn into page would mean I'd have to like click into that page to go and find the information as opposed to it being available in the outline have you like experienced anything or I don't know what what are your thoughts on this like actually making individual notes and then you know tagging it making it a zle versus keeping things in have you is this a problem that you've 

spoken to other people about or I'm sure log people will love this I mean there's a few things I hear there you know when you were talking about like moving and shuffling you know that is kind of antithetical to the zle Casten as we as we kind of experience it through Lumen and as as we design our systems um based on those based on his practices um because we're not really 

shuffling anything right things have in fact he would talk about like things have a hard address you know in the in the analog so-called analog system the paper base um what that allows for is it allows you to not it forces you to think about things in particular contexts like in the context it was captured so if I have a note and it was written in light of another note it has an address that 

shows that relationship so when I go back to that note even from a different entry point like it maybe I'm thinking about something else but it brings me to this idea that was captured in different context I have to at least see that note in its context first it doesn't mean I have to stick with that but it gives me something kind of like to start with um the other thing that happens in a zcast see a zle cousin is like a very simple it's like a 

very simple container that has somewhat strict rules and that combination between Simplicity and strictness uh make for a very potent environment like learning writing environment and and what I mean by that is that we don't structure you know you're talking about like outlining and stuff we we're not structuring our ideas in when I talk about Fogo is not an outline 

we're not structuring ideas in hierarchically like oh this idea should come here you know because it's a broader concept and I don't want you know I want to get into the granular minutia detail later so I'm going to take this note and put it at the top inside the zedal caston's main compartment where all the single idea notes exist that isn't happening we're just showing that there is a relationship I often say we're just showing there is a relationship not the quality of that relationship that outlining happens later after the fact when I say okay now 

I want to actually see what's happening here I want to follow this train of thought thought in this one direction or I want to write this paper that's when I pull things out and I say okay now let's reorganize stuff in a way that is conducive to what I want to do make sense or you know it's disjunct intentionally disjunctive or purposefully coherent you know whatever whatever you you want to do with it so we keep those separate because otherwise otherwise it becomes messy that's where mess happens when we're going in there 

me like this should go here now and this should go there it's like no no no that's a lot of notes to be this should go here now about right yeah we take out a section of those and we say we leave them in there the way they are but we mirror them elsewhere in another document or whatever and that's where we start to say okay now how do these ideas really work together what can I say about them Etc so it would be very correct to say that I don't actually have a zal cast I've got something which is a bit of a 

zet cast monster Mor like yeah I mean you can lay it on thick I really don't mind because I I love this like it works for me but I I'm just looking at ways to like improve how I work with it because yeah like I'm not so I'm not really too worried about like the Lumen um or being sure being um what's it um bound to the Lumen approach I'm just like trying to think about like 

working with outlines and Link notes and all of these things um I mean look Lumen wasn't bound to the Lumen approach either I mean yeah Lumen shifted things and there's the you know the famous case where he started a new zle cast and a few years in he he restarted it you know he we get the sense that he kind of added some Alpha numerics that he hadn't planned on later as he had different kinds of notes he wanted to get in there somehow like you know he he it's his own system he wasn't like he has his 

experience of zle Casten has literally nothing to do with contemporary approaches to this stuff there's there was no like internet obviously at least not till he was almost dead um there there's no sense of I'm creating something for for the world and I'm going to monetize it like that is completely absent so he doesn't have to create a foolproof system he doesn't have to create a system without holes because he has memory he can he can change things as he goes and be like all 

right this diverts from what I've been doing but when I I'll I'll remember it I'll know why I did it so that's just to like because a lot of people think that he kind of invented this system in like a in like a cube like a soundproof room and then came out and was like here now Beth the system yeah it's like no he had basic ideas and it seems to be like he had a basic sense and he built it as he went so it has to fun function for you as it functioned for him you know if anything 

if you want to follow anything from Lumen it's like create a system that works for you yeah right exactly so but I I am I've been this way my whole life like I like constraints I like to work within constraints and I like to work within very simple basic constraints and basic principles because a I like that sort of framing but I also like it to be simple enough where I can push out of it break it if I need to so it's not like if I 

change one thing the whole like all this domino effect of collapse of little granular inter systems and everything all falls apart like I don't like that I like broad general principles broad General systems which is what I try to present in the book um because in that there's room to play right so yeah you know with regards to what you're doing I don't obviously don't know exactly what you're doing because I haven't seen it and I'm not you so I'm not living this system like you are but there are if I 

were looking at if someone was like is this a zle Casten you know I would be looking for what PR like what are the principles you're doing what what are the the the procedures you're taking to turn one thing into another um and are you allowing are you creating these different distinct components so for example like it with the with the Lumen 

style zedcast and you know we have this main compartment where all those notes are free to kind of act like a Rome and other notes come in and they inform and they change and the ideas are bumping up against one you have that and do you have this other place where you can work with what's in there with what's happening in there yeah so those are the kind of things I would look for um to to sort of make my assessment on yeah whether or not well yeah yeah go 

ahead no I mean to look at my drafts like so the essay I Shar online like um this is the final version was this one over here which I actually don't think I've brought it in no I haven't like I haven't brought in the final version and these are all like exerpts like over the 5,000 words I wrote like 15,000 in total probably that I've just like edited art 

and I did that in a number of different places so for instance like I've got like a couple of edits that are in uh log seek and then I've got a whole bunch of things that are in um in Google Drive just because I needed to collaborate and get feedback well I wanted to collaborate and get feedback from people on that um so it's like I realized like very quickly there's like there's a sprawl of information and don't 

I I guess maybe these are separate things which is like I've got all these little notes and then I aggregate them into an essay and I get feedback but now I'm like wanting to preserve feedback and wanting to preserve comments and and I think the best approach which I haven't actually gone and done yet but it seems like this is what you might recommend is to take some of those essay things and then convert those into zetel um to like bring them back into the system and curate them a little bit more um which I which requires me to slow 

down and not speed up ahead so I think like it's something about slowing down and and bringing in friction into your process um yeah yeah yeah I mean the one of the reasons why I like PKM and zle cust and stuff is because inevitably at some point you're going to get to sometimes rather quickly you're going to get to a discussion that actually has nothing to do with like the procedures and friction and stuff and 

get to like the psychological makeup of the person right you know because there's the question I I would ask is like why why why do you why do you feel like you need to capture the comments like I I had over a dozen beta readers and I kept their comments on like they sent me like when I wrote the book I had beta readers and they would comment on certain things I wanted them to comment on and I used those comments for the book but there I never saved them like I 

didn't I saved them like I I saved them in the sense that I I took them but I don't import them into the zedcast them because their job is done yeah you know their function is complete um and so if if you or anyone else is really like I really want to get these comments in I really want to get them into my notes I would say well why what what's the function what do you what are you what are you hoping to gain from them and if it's important then sure figure out a procedure system or method 

that like allows you to do that relatively easily and if it's not that important you can always just keep them in a like put them in a folder and then they're there if you ever need it you know it's kind of like the thing like you're going through your room and saying should I keep this should I not keep this and you're like I haven't looked at this object in 10 years but I some reason I think tomorrow I'm going to need it so I just put it back on the Shelf you know I do this over and over and over again that's some point you could be like I bet if I took this 

object and removed it I'd never think about it yeah that's okay you know Mary condo going Mary condo on your notes yeah does it bring yeah bring bring me joy um the for me it's like I respect the I value the feedback that I got from a writing stylistic thing because like it's the first time that I you know I've paid a friend to do some editing work as well and you know and there was s such insightful things that 

even though I put you know you can put it through a large language model whatever but like there's something about the human aspect that's saying wow like this this didn't make sense to me you can improve this stylistically this is this was out of place so for me it's like a it's a a coaching aspect of like I want to improve my writing um yeah so like when I think about if I should put something like that in my zle 

Casten or not the question I tend to ask myself not like for I don't sit there and formally interview myself but the question that comes to mind somewhere in my brain is do I want this do I want this idea to interact with other ideas so and and am I gonna possibly write about it uh those two things are very good curators for what goes into my zle Caston so if I get a comment from someone 

that's really helpful for writing or really helpful for that particular piece of writing say it's say it's helpful for that piece of writing it's like a sentence something about a sentence or a paragraph well then great it served its purpose and I'll maybe I'll keep it somewhere but I'm not going to put in zle Casten because I don't need that to interact but if I wanted to interact with another idea then I would bring it into the zle Casten because I think this idea is interesting it's interesting in this particular context but it may be interesting in another context down the road so let's bring it in there so 

that's how I discern otherwise otherwise I would just keep that stuff in a folder that's like writing tips something like that something like that you know and this takes me to the end of your book which I'm gonna you see if you don't if you don't mind if I show it but um show whatever is a V cast in a second brain no the second brains are comprehensive so I think what I'm showing here is like I'm trying to do everything in one and a zel cast is sort of a 

sub a subc container within a larger container of all the other things I'm trying to do so yeah I guess that's why it's so much more so much Messier I mean all of these things over here for instance I mean as keeping track of all my thoughts um then the next question was around Version Control like when you do multiple iterations like here iterations of what right like if you send a draft and you and and you 

have to change some things and like there's a lot of like major changes for instance I think I've it's it's probably in in in lie of the previous question why am I trying to do it and I think it's like because I think there might be something valuable there in the future but then I'm never ever going back and look at it so I'm sort of answering myself now and saying like why am I trying to preserve it I don't know why it's a psychological thing like I've put so much effort into this like I want I want it to remain 

like there is a stack somewhere in in a landfill you know slowly rotting is a stack about this got to fit it in my screen here taller than my screen will allow of printouts of that book and heavy heavy edits like line like physical line edits you know there is absolutely no reason whatsoever for me to save those Corrections there's just no reason I'm only one person it's one life I've got only so many books in me 

what can I possibly you it's hard enough to do the next book let alone to go through the last book and mine it you know for the little notes I wrote myself so it just goes in the trash you know um it it goes into garbage and at some point I think I think it sounds like you you write you obviously write you you've written essays and things things like that um 

you you want to write more I think the more you engage in the working writer Lifestyle the lifestyle of someone who is a working writer not necessarily for money but someone who's just like just you know I got to get this out I want this out I want something out every week I want something you know to whatever the the quickly it tells you what's important and what's not yeah right there's just not enough time there's not enough time to save every comment every version of a of a of 

an article there's not enough reason you know um so back to something you said before which is like okay you write something now go back and take the ideas out and put them in your zle cast and I teach that and I recommend it to people but it's not something that everyone has to do nor is it something that I am particularly like strict about it's just a way to bring ideas back it's like a way of thinking about material that 

allows you to bring it back into the zcast and so it can it can germinate reg germinate regenerate reg germinate um I do it but it's not like I go through every article I write and try to find the atomic ideas in each one and take everyone out and put it in my zel I don't do that at all I I do that for something something sticks out at me I'll just bring that in and you see the shackles coming off of me as you're saying this I feel like a relief in a sense that it's like uh because um 

yeah yeah there's so many people I encounter where I just want to be like just chill man just chill it's not not you but like I just want to be well me too yeah there's it's like Let There Be room for imperfection Let There Be room for forgetting things let there be room for uh losing a note let there be room for that like all of that if you're a 

writer and a thinker all of that is Grist for the mill you know like when people say you mentioned this earlier on and I was just this came up recently um in some other conversation like you know it's so isn't it so lame that people who get into zedel Casten they end up just writing about zedel Casten wow like write about something real and I'm like every doesn't matter the topic you can write about gardening you can write 

about uh the dung beetle if you are human and you take a human approach to your subject it doesn't matter the subject doesn't matter if you stop writing about spirituality and write about zedcast in the rest of your life eventually that stuff is going to filter in you know um the reason I thought that I could say that is because you've written about other topics previously like I've got like a respect for your writing because of that interestingly 

enough so yeah yeah maybe I meite mind shift for me yeah get involve yourself in things that are really exciting and interesting to you zedel Casten and pcam to to a lesser extent but part of it is very interesting to me it Taps into all these interesting ideas it leads me back into books I read 25 years ago that had nothing to do with zettle Caston but now take on a new take new meaning because of this work that I'm doing and um you 

know let yourself just like dig a hole for a while and see what's down there you know you might the whole might just be like technical zle cast like even this book getting interested in zettle Casen pushed me into a style and a realm of writing I had not done really before which is howto articles I never wrote howto articles 10 years ago I never wrote anything like that but I was writing those 101 like what is 

a fleeting note what is a permanent note what is a literature note I was writing these kind of basic howto 101 things as an experiment even for myself and that only came about because I was in a community that needed help understanding some stuff so I said well let me try it let me try let me see what this kind of writing is you know yeah and it opened up this whole new world I mean I love I'm really excited about writing these kinds of works but I never would have done it if I hadn't you know 

hadn't been involved in zettle cting yeah I feel like I've turned it into a little bit of like a coaching session no I'll talk about however you want to talk about it yeah I mean because I've got some some interesting examples here maybe yeah let's go into this one because I think this this part here this preserving context that I have gone backwards and forwards in this and I think I actually need to just break out of this because I've tried to preserve context and and uh sort of just falls 

apart down the line where you're trying to preserve the thing from its original Source you know in the sense of a legure note H sorry yeah what a reference note not a literature note I'm using the um that's all right but you know guys like Bohan and interview that I did with Alexia I don't know how to pronounce this name they really like to preserve everything in the Daily Journal to see the context of what happened on that day and whatever and I think for me 

moving things into a separate page so for instance into my's we're here um and just dropping the the link to a day was so freeing but I still I still come across it in in some other examples in my writing which is like this one over here uh yeah so processing observations on other notes so I I talk to this idea of 

um observation notes which is ah sorry just clicked the wrong thing um I think it is a bit slower today but anyways I I talk to this idea of the observation notes as a tag I don't know if you can actually see here so for instance this is a this argument here if I click into it or shift click into it it's going to take me to uh conversation that I watched on 

YouTube talking where these two people discuss the resurrection of Jesus and you know the full context is available if I go and click there so it's preserved it's preserved in here as comments you know but now when I want to use it it's in the context of apologetics and it's in that um reference of the Bible is not inherent but I think I need to just break break that whole thing and REM remove the the 

the link to the original and um yeah sort of just leave it behind but part of me is afraid of doing that and I don't know why is I sort of like I want to be able to go see the discussion that I had like you know it's interesting you say like the context I immediately go back and I think oh yes that was a very interesting discussion oh those two Scholars had very different philosophies and oh the one guy despite being um out 

of Evangelical Christian Community you still had very healthy views of the Divine and I was like hm so that context is all very interesting but like if I'm going to write about this the Bible is not in erant then I don't need to have all that context well I don't know maybe this is a discussion what do you think about that what do I think about what whether you should have retain the content of an idea that you took from someone else or 

that you copied from someone else or brought into your this is this is not something I copied it was a note that I took in observation of what they were saying so it's not it's not like it's not a reference but was yeah it's not a quote but it was definitely sparked by that conversation so what I do is I in my main notes which are the main notes in The Zed main Singlish idea notes I um if I if I any idea that I have that 

is sort of Spawn the spawn of someone else's idea I just put that their idea in there either as a quote or just a reference like a c page whatever so and so's book or something um it just it's there if I need it I I don't think about it too much it's just that when I go to write I need to know if my idea is a rephrasing of this person's idea in which case I need to give them credit like cite them or is my idea just kind 

of inspired by that person and I'll decide that at the time of writing I don't need to do anything with it prior and there's I have both I have ideas where I'm just kind of restating what someone else said because it's good and I might want to use that someday and there's times when I'm I show this in my course there's times when I write an idea it has like it's like the opposite of what that person said it's actually the opposite tap but was it came out of me reading that person so I want to put that in there why because when I go to 

write this this piece this hypothetical piece and I have my idea I may want to say this is what I think but so and so thinks this and that's wrong you know or whatever or I I just want to be you know look at how we both can say this you know different things you know whatever I may just want to I may want to use that piece of information uh in my writing later um and the the the whole thing is like you just don't know you don't know how you're going to use it so 

I make it so that if I want to use it it's available but if I don't want to use it it's not overpowering I can ignore it basically I can ignore it if I need to so yeah simple keep Simple keep it clean yeah I think I need to go and simplify this and like actually just use simpler Source simpler sourcing rather than like using the actual block ined and clicking around it's not usually this slow when when when I do this and 

I'm wondering if it's just because of the electron application and the fact that I'm on online core whatever I don't know but like the clicking is usually much quicker um but yeah anyways um my my feeling though and this is the point is that people are too enamored with the structure and like preserving it and like having this like really I don't know like you know you've described this way okay I'm not going to follow Bob Doo's way because Bob doto is a writer and he's done your surname right yeah that's actually you're 

probably one of the first people who's pronounced it right oh great um yeah he's got this he's got Authority and I can trust his approach and oh but then I come up against this thing but Bob said I must do it this way and I feel like I just want to break out of that and be like huh I'm I'm needing to find ways that serve me and I guess this is this is part of my process and like like you say when you actually start writing a lot it's going to these things will will start coming out and I and I think I just haven't been working with my 

notes in a material way in the last few years I've just been writing new things and writing this and not going back there's not been like a revision of going through my notes and a discipline of you know producing outputs I sort of think I'm a little bit rebellious if I say I want to write an essay every month and I'll be like nope I won't do it so I think that practice is what really makes it uh the system sustainable yeah I mean we all we all I 

most people I know including me you know have grand ideas about I'm going to do X this many times a month you know and it usually doesn't work out that way um or the things that I've done really consistently were things that I didn't intend for them to be consistently it's just something about that thing the the relationship between yield and effort like what I got out of it versus you 

know in relationship to what I did to get that it it just matched so I just kept doing it um I write a lot because I really really really enjoy writing I write the second I wake up you know 6: am it's not a discipline I love doing I pick up my phone and I'm my brain is just like going that's just who I am there's so people who aren't that you know um so yeah you know it's like if you if writing is something 

that like helps you do something you know writing for writing is how I think I like to write it down and see it and you know really mess with it kind of external then that's what why I do it a lot because I like to think so therefore I like to write yeah um I like to talk so I talk aloud I talk a loud in my apartment all the time I interview myself to hear my ideas outside of myself you know I say like if I'm trying 

to figure out something I'll start like saying it a lot no one's here you know I can't even imagine if someone had a recorder in here what they'd hear but you know it's I like to externalize things get them out so I can see and hear them so yeah I I feel like I I'm gonna stop sharing my screen now because there's like you know there many different things you only got five minutes left and I want to respect your time um and the the other set of questions that I just 

wanted to get a bit more background on from you as as on a personal capacity is like your your spiritual background because you clearly got a lot of interesting and diverse experiences um and yeah i' I've appreciated your writing and your suggestions so maybe to give you the opportunity to you know if you had to give one if if you had one take away from this conversation maybe or your book like 

what you would encourage a buddying young writer um what where where they can find more information because you mentioned you got a course and I saw from your website that you've also got a course that's starting in the winter in the US and then yeah from from my selfishly from a personal capacity where you find yourself spiritually at the moment uh which one so what am I ask what's my question what's the question where where can find out about my book oh your course no your course course the 

course is not live yet the course the I I've been teaching as a sort of intro to zle Casting course for for a few years and I I'm re I redesigned it I am in the process of redesigning it and another course so that will be out my plan is later in the year if not right into the new year um that'll be uh yeah there'll be these two new courses that have to deal with with settle cast writing um based based on the stuff that's in the 

book and and and more um so my website will certainly feature that and then you know all the places where I Yammer on online will also mention that um and something about my spiritual background yeah oh yeah where you find yourself spiritually at the moments oh at the mo I mean where I am at the moment is is a has to do with a long traj but you know these days I I many years 

ago I decided to reinvestigate my family tradition which is like a Catholic Catholic tradition um so I reinvested into that to see what was there after you know a few Decades of going everywhere you know I was part of every religious sect group left hand right hand all all of them um maybe a cult or two in there I love it all you know I'm I'm down with all of it so so I 

I got into all that and then and then yeah um I returned so to speak to that Catholic tradition but it's me it's my I am not by the book I read the book I read the book every day but I am not by I am not mainstream when it comes of that stuff uh so I have my my way of understanding those things um but yeah I mean I'm looking at this stack of books here right in front of me 

there's you know Raman Mahari there's stuff on the Holy Spirit multiple books on there there's stuff on prayer you know there's all sorts of but then next to that I have you know design in form like bow house Theory and you know so it's like you know it's it's all it all mixes for me but yeah that's where I that's where I ended up after when I think when I was 15 is when I started really getting into spiritual stuff and 

reading and doing nominal practices and stuff and and it focus on nity or no I mean nonduality is certainly interesting to me like in Hindi would be like nety netti like not this not that like the the um ADV vant and those those kinds of things are of course interesting they they're they're necessary things to get interested in if you interested in spirituality I think but there's lots of ways to approach that you know there's 

people have been handling that question of non-duality or I and thou for thousands and thousands of years um so I I you know there were times when I was more like a vehement non-dualist but I'm a I tell people all the time I always say this like I'm a Taurus you know like I don't need I like boundaries and I like rules but I don't like them too much you know and like adite uh is cool and interesting and and 

high yield uh but um you know I also like the world sometimes sometimes I hate I should say I like it all the time but uh you I engage in the world and the world is not a non-dist place you know the world is is very dualist and if you want to talk to someone you should probably learn how to talk to them dualistically yeah that's iCal element you know you know you live if we live in an apparent world that's a ter apparent 

is used very specifically like the world of appearances people engage in that world you want to engage with people you should probably learn how to engage in appearances right so um yeah at some point I had to mourn the loss of me not being a monk in a cave that that was not my trajectory at least not in the immediate which is what of course all of us young young young men want to do yeah young spiritually minded men at some 

point have in their minds that they'll be a monk in a cave um but yeah that you have to grieve that because most people are not cut out for that or at least not have the opportunity yeah so you grieve that loss and then you say okay so what am what am I where do I exist what am I you know oh I'm in a relationship okay you know well I I can't be there dualism in a relationship yeah um you know so keep it practical practice you know employ the 

employ the put the practice put the ideas into practice right yeah um yeah but I you know um yeah I'm happy to always happy to Veer into that we just hit the the hour box I mean I'm I'm very respectful of time because I think time is something which I've struggled with myself well historically I'm trying to rewrite my own narratives I'll probably edit this part but whatever we'll see so there's a couple 

of things around time around valuing the work that I do around productivity courses and that sort of stuff which is have have been coming up in big ways in the last few months SL weeks um which I feel like are important um segue is professionally feel like still sort of stepping over the threshold and being like you know as you said like actually engaging with the ideas actually engaging with the world in a non-dualistic sense you know spirituality has been like a really 

important part of my life and then not and now again but it's like for me I'm seeing like really in a practical engaging Like Loving energetic participation in life um but that also means valuing you know what I do and not like having bad narratives about it so MH yeah I'm I'm definitely going to cut this part out so if you watched all the way to the end thank you so much for 

watching as you can see I didn't cut certain parts out because I'm also using this as an opportunity to explore just being more of my authentic self and bringing more of my journey to the channel I guess so I really appreciate you watching all the way to the end I hope this also gives you some encouragement that you don't have to have a perfect system and that you found some value in Bob's perspectives just a reminder that his book is linked below and it really is a valuable intro to the zle Caston writing system as always thank you so much for watching and I'll 

hopefully see you in the next one cheers
