---
人员: 
  - "[[歸藏的 AI 工具箱]]"
tags:
  - articles
日期: 2024-08-05
时间: None
链接: https://mp.weixin.qq.com/s/Zw-H78tmChzq281X41tP-w
附件: https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8EgcagxoOibiaehNeuv5Xvo9S2eyjraiblHdbe1RaNKHLBuxBv8HmYL9ESSLtG904Z6o7nH6h290FbZVfQ/0?wx_fmt=jpeg)
---
## Document Note

## Summary

教大家设置一下 Arc 浏览器的网站快速搜索功能，同时还收集了所有 AI 搜索的搜索传参链接，坐产品也能用。

## Full Document
> **教大家设置一下 Arc 浏览器的网站快速搜索功能，同时还收集了所有 AI 搜索的搜索传参链接，坐产品也能用。**
> 
> 

最近 AI 搜索越来越多。不少人都是重度用户了。

由于浏览器还都没有对 AI 搜索做适配所以我们使用 AI 搜素的效率都比较低。

你需要输入地址，打开页面，然后再输入需要搜索的问题，等待浏览器回答。

而且每个搜索都有自己擅长的部分，由于 LLM 的幻觉问题不是每个 AI 搜索都是 100% 可靠的。

我们经常需要多个搜索相互比对一些问题的答案，其实橘子和哥飞都针对这类需求做了类似的浏览器插件。

但其实 Arc 内置了一个 **Site Search** 功能，可以帮助我们非常优雅且高效的使用不同的 AI 搜索。

比如下面这样，你可以在搜索框输入对应的搜索名称按下 TAB 键，然后输入想要询问的问题回车就行。

#### 那么这个功能应该如何使用呢？

首先你需要在输入框中输入 **arc://settings/searchEngines** 这个链接，进入到设置页面，可以看到 Arc 已经帮我们内置了一些网站比如 Twitter 以及 YouTube 甚至是 Perplexity。

然后我们需要添加我们想要的搜索网站，点击网站搜索右侧的添加就可以。

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8EgcagxoOibiaehNeuv5Xvo9S2eVSOib0ssuyssmbhicxDOcLJgZPjcFtb9psqGuwf3qQE65fhho5RI9xoQ/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
那么我们可以看到你需要填写三个字段，分别是：

网站名称：这个就是你按下 TAB 之后在搜索框右侧显示的名称你可以写全

快捷字符：这个字段就是你输入后按下 TAB 触发的字符，所以需要触发的网站名称的话你可以写的短一些，比如我这里就只写了 360.

网址格式：网址格式就是你需要传给网站搜索词的网址结构，一般由前面的网址 https://so.chat.360.cn/search 和后面的参数 ?q=%s 组成，其中 %s 会替换为你输入的搜索词。

![Image](https://mmbiz.qpic.cn/mmbiz_jpg/fbRX0iaT8EgcagxoOibiaehNeuv5Xvo9S2e1d13Ab8V0ueKHEKM2I1MctyLoM1wQUl8dkAIQpCoCOickQOSsZ3w76w/640?wx_fmt=other&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)可能大家发现了，这里面最麻烦的事情是，如何获得那么多AI 搜索网站的网址格式，藏师傅既然发了教程肯定不会写半截的对吧。
那么重点来了，我收集了市面上几乎全部 AI 搜索的网址格式，甚至即刻和小红书的，你需要哪个只需要复制填进去就可以啦。

#### AI 搜索网址格式合集：

**360 AI搜索**

```
https://so.chat.360.cn/search?q=%s
```

**Fole AI 搜索**

```
https://felo.ai/search/?q=%s
```

**秘塔 AI 搜索**

```
https://metaso.cn/?q=%s
```

**GenSpark AI 搜索**

```
https://www.genspark.ai/search?query=%s
```

**Monica**

```
https://s.monica.im/search/?q=%s
```

**Perplexity**

```
https://www.perplexity.ai/search?s=o&q=%s
```

**即刻**

```
https://web.okjike.com/search?keyword=%s
```

**小红书**

```
https://www.xiaohongshu.com/search_result?keyword=%s
```

**Thinkany**

```
https://thinkany.ai/zh/search?q=%s
```

**好了教程到这里就结束了，如果觉得有用的话恳请点个赞、在看或者分享你的朋友们，拜谢🙏**
