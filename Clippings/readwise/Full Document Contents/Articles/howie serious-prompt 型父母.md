---
人员: 
  - "[[howie serious]]"
tags:
  - articles
日期: 2024-11-13
时间: None
链接: https://mp.weixin.qq.com/s/FKBWAtSDn1wJolfzTUOP6w
附件: https://mmbiz.qpic.cn/sz_mmbiz_jpg/Wp9RhwK45W8VLAl9y4hk5vZEJdenFBIAUq2zm0q1Aicv2GWnGiantXbfFvf6srHalnWpHwGlUibxZYkysOA2rdN2A/0?wx_fmt=jpeg)
---
## Document Note

## Summary

父母陪孩子学习时，只要做好这一件事……

## Full Document
每天晚上，雷打不动有1个多小时陪孩子学习数学和编程。昨天晚上，小树遇到一个数学题不会做：

#### 思考的阻塞，来自知识的漏洞

学校里还没学过

，孩子不知道这个（平方差）公式可以（因式分解）/拆解为

。这个题不会做是正常的。

毕竟，思考的阻碍，几乎都来自知识的漏洞。这是**思考的第一只拦路虎：知识漏洞**。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Wp9RhwK45W8VLAl9y4hk5vZEJdenFBIAOMzk6HdpD6RtlWHbCES8ic3bxxicrmP0WkPzSry3b1goDGGoBwLl8kKA/640?wx_fmt=png&from=appmsg)
于是，我现场给她推导了这个公式（用两个正方形相减的方式，直观，具体，还能得出通用结论）。我说，**这个公式不要死记硬背，一定要自己推导。以后遇到所有的公式，都必须会自己推导。如果不能推导，说明我们不理解 why**。你可以记住

，但这是不对的，**记住不等于理解，所以记住不是真的掌握，必须理解知识背后的 why**。

#### 解题思路哪里来？

然后，小树发现，这等于

，每个数字之间差 4，是个等差数列。然后又呆住了：这下可怎么办？

等差数列求和又是一个公式。和平方差因式分解一样，这两个知识点都是初中二年级才学的。但是，它确确实实出现在小学四年级的课外练习册里面了（这方面，教培集团们真的是“功在当代，利在千秋”啊！）。

解题时，我们会有好几个思路。小树尝试一个思路（孩子想快速解题，顺着本能会一路狂奔），然后遇到了问题，然后就卡在那里了。

于是，孩子遇到了**思考的第二只拦路虎：思路阻塞。**

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Wp9RhwK45W8VLAl9y4hk5vZEJdenFBIAkGricicKpEXMz8f6L7P4sPg6l11mN8OjGDCQ04X2f7WTrMs4vfSXibPVQ/640?wx_fmt=png&from=appmsg)
#### 如何得到思路？

我让她不要思考的太快。思考太快，卡到弯路里就回不来了。数学和编程一样，这些学科的**知识非常少，所以不求快，不要着急，关键是思考的过程**。

> 现在，我们一步一步思考，think step by step。你先把解题过程一步一步写下来。
> 
> 

小树开始在草稿纸上写步骤。

* 第一步：
* 第二步：

我说停！现在，放下笔。坐好了，不着急往下赶，对着这个题目，**盯着它看，一直看，直到看懂这个题目，看出这个题目中的规律**。

然后，娃就乖乖盯着题目看。看了一会，她就笑了：不过就是从 1 加到 100,一共 100 个数，答案是 5050 嘛。

本以为这个题目很难，是个吃人的大老虎，原来不过是一只纸老虎啊。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Wp9RhwK45W8VLAl9y4hk5vZEJdenFBIAXibowaSoiaVZLY06IBzfLsDh3XwX7ExRZibxc9j5TFdibN3FPe4icoDNTWw/640?wx_fmt=png&from=appmsg)
关于解题，关键是**学习、练习并掌握解决一切问题的“解题元技能”**。之前用万字长文写过这个问题，此处不赘述。有需求移步：[《解题元技能》](https://mp.weixin.qq.com/s?__biz=MzI4MzE2MDA0MA==&mid=2247490908&idx=1&sn=c46a516684a55b4340cbd45ae16a4d20&chksm=eb8fa1dbdcf828cd76d8b3eacffec4274fe58f4e14d23a601a50dc00aed1bbcd83b708fc6fb1&payreadticket=HGu7fcpexSRlY9_UOcXOOLlzhM7GZA3e3yuYrhWTKMwxWTrhbLw6Vb5jASGBAQ-tCFlUeko&scene=21#wechat_redirect)。（这个文章写的时候设置了付费。现在以《费曼学习法训练营》的形式集体学习中，有需求的话，可以点击“阅读原文”加入。）

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Wp9RhwK45W8VLAl9y4hk5vZEJdenFBIAfiad8j4pmNy3luLAaDRficEeFbRicRtT0NL44MibPgmYxyvrFxg3Ro7Oyw/640?wx_fmt=png&from=appmsg)
#### 思考的乐趣

费曼在小时候和爸爸一起去树林里看鸟。费曼爸爸说，你知道用各种语言称呼这只鸟的名字，但是，**知道名字并不等于理解**。现在，忘掉这些名词，真正观察一下这只鸟。

我经常对小树说，**读题，读题，再读题**！我们不要觉得读题目浪费时间，觉得自己题目读懂了。不要看一眼就以为自己读懂题目了。**解题只分四步，而第一步就是读题，真正读懂题目，读到不看题目能讲出来，讲的很清晰的程度**。

> 现在，放下笔，看看这个题目，盯着这个题目看。这个题目到底在问什么？它告诉了我们什么信息？题目里包含了几条信息？每一条信息都要用自己的话说出来。每一条信息，到底告诉了我们什么？
> 
> 

解题时，当我们真正把题目读进去了，读懂了，后面的解题过程就容易了。我们会遇到**知识欠缺、思路僵化导致的思考阻塞**，但是，这都是可以通过知识学习和解题练习来稳步解决。

聊完了这些。然后，我们又一起聊了一个衍生的题目：

？算出来之后，再算一下：

？因为题目比较简单，我们就玩心算加口算。我们坐在一起，用手操作空气中的假想的数组，用嘴说出思考的过程，嘻嘻哈哈地在扯淡中就把题目给聊了，一起快乐地度过了今天的解题时光。

然后，她又写了两个 C++程序，还是两个解题，只不过用编程语言这种形式简洁、强调秩序的人工语言。还练习了一下盲打，字母模块已经达到熟练水平，编程打字模块已经达到入门一级。

然后，娃心满意足，快快乐乐地结束了今天的亲子学习。

#### 来自实例的几条启发

##### 不要教，要prompt

**父母陪孩子学习，不要走教培模式：不要代替孩子思考，不要帮孩子思考。父母只应该做一件事：prompt（提示）**。

父母可以提供脚手架，把思考问题转化成孩子稍微努力一下就能够得着的程度，转化为孩子在最近学习区范围的思考。整个过程中，父母只是提供 prompt，而非灌输答案、教授僵化孩子思维的解题思路。

**你提供 prompt**，孩子就可以主动思考；**你提供答案**，孩子就停止思考，而且记不住你的答案；**你提供解题思路**，孩子继续停止思考，她会以为自己思考了，以为自己会做了，然后下次遇到时仍然不会。因为，没有经历自己的主动思考过程，**她不知道背后的 why，于是连带着 what 和 how 都记不住**。

父母不要用成年人的知识积累和抽象思考能力去要求孩子，去灌输你觉得很简单很明显的东西。**父母不要以为自己会教，去扮演老师或教培的角色**。我个人从来不敢、也不屑于去做”教“的工作。如果父母有这样的习惯和行为模式，只是因为每个人都是有局限的，而父母没有认识到自己的局限性。

##### 清晰思考之难

**写作等高级认知活动之难，难在清晰思考是困难的。而思考之难，在于思考经常被阻塞**。要么是因为背景知识有漏洞，要么是解题思路僵化。

不论成年人还是孩子，因为当前个人知识体系的局限性，一定会遇到阻塞。所以，要想清晰思考，一定要基础牢靠。**在稳固的知识基础上，概念清晰准确，逻辑推理、复杂思考才有可能发生**。

孩子在解题中，在思考上遇到问题，父母光喊口号（你要思考啊！你要深度思考独立思考清晰思考！你要努力思考啊！）（你怎么不动脑子呀！动呀，动呀！），越这样，孩子越会对着题目发呆。

当然，还有的父母喜欢拍桌子，喜欢生气。这会导致孩子的应激反应，长期下去，不但无法找到思考的乐趣，反而会陷入抑郁。

**孩子解题时遇到思考阻塞，唯一的办法是让孩子给父母讲题（费曼学习法）：讲自己对题目本身的理解，讲自己的解题思路**。父母听孩子讲解，一方面自己可以借机了解下题目（孩子做题时，父母应该在旁边读自己的书），听的过程中父母提出一些问题（这个题目什么意思，我怎么还没看懂？你这一步为什么要这么做，我还没想明白？），借机组织自己的解题思路；这个过程中，父母会发现孩子解题时遇到阻塞的根本原因，到底是哪个知识点欠缺，那个环节把自己绕进去了。

就像医生看病一样，诊断了病因，然后对症下药就容易了：一步步引导孩子主动思考即可。

##### ChatGPT作为思考教练

有父母说，我不会啊，小学题目我也不会做。

这不是借口，**两年前你可以拿“不会**”**当借口，现在不行了**，因为 ChatGPT 发布快两年了！如果父母不会引导孩子思考，甚至也无法识别孩子在解题中在什么环节遇到了什么阻塞，那么，让 chatgpt 来引导。

以刚才那道题为例，事实上，chatgpt 提供了比更好的解题思路，更好版本的引导。**普通数学题，用 gpt-4o 模型足够；上难度的题目，用国际奥数铜牌水平的 o1 模型也足够了**。

![Image](https://mmbiz.qpic.cn/sz_mmbiz_png/Wp9RhwK45W8VLAl9y4hk5vZEJdenFBIATvLT31PwGSoUIc0RSKMTeqRGfoYhklHyibunuiahfN7cnaJ0z6HKSkhQ/640?wx_fmt=png&from=appmsg)
##### AGI时代的孩子，学习到底是学什么？

在 AGI 时代，学习的目标不是某个具体的知识和技能（平方差公式、因式分解、等差数列），而是思考的习惯和能力。[这个世界，愈发分裂为思考者与不思考者这两个阶层](https://mp.weixin.qq.com/s?__biz=MzI4MzE2MDA0MA==&mid=2247492503&idx=1&sn=e10f35b41a6424f4ce2e8396d2a78e57&chksm=eb8c5f10dcfbd606d856a3db5a28e9a9dddac0261480d33c4d60f215a24eb06e5f5fbb5d5476&token=1615620255&lang=zh_CN&scene=21#wechat_redirect)。

父母与孩子一起学习，**孩子到底在学什么？**

只学习一件事：主动思考，独立思考，深度思考，清晰思考。

父母与孩子一起学习，**父母到底在干什么？**

父母不是化身教培老师，给孩子灌输死的知识，灌输惰化孩子思维的解题套路。

**父母只要做一件事，只应该做一件事：prompt。**
