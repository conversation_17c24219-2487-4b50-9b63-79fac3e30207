---
人员: 
  - "[[Augment Code]]"
tags:
  - tweets
日期: 2025-06-24
时间: None
相关:
  - "[[repo]]"
  - "[[model]]"
  - "[[noise]]"
  - "[[impact]]"
  - "[[output]]"
  - "[[tuning]]"
  - "[[科技]]"
  - "[[context]]"
  - "[[fix bugs]]"
  - "[[phrasing]]"
  - "[[workflow]]"
  - "[[add tests]]"
  - "[[edge case]]"
  - "[[prompting]]"
  - "[[retrieval]]"
  - "[[structure]]"
  - "[[summarize]]"
  - "[[fix issues]]"
  - "[[prioritize]]"
  - "[[large tasks]]"
  - "[[regressions]]"
  - "[[atomic steps]]"
  - "[[instructions]]"
  - "[[prompt edits]]"
  - "[[system design]]"
  - "[[tool contracts]]"
  - "[[achievable goal]]"
  - "[[planning phases]]"
  - "[[external context]]"
  - "[[optimizing noise]]"
  - "[[list possible bugs]]"
  - "[[prompt improvements]]"

链接: https://twitter.com/augmentcode/status/1937538066031477025/?rw_tt_thread=True
附件: https://pbs.twimg.com/profile_images/1849169714100269057/YF9iS2wt.png)
---
## Document Note

## Summary

本文强调了提示词（prompting）在使用语言模型中的重要作用。起初，优化提示词能显著提升模型输出质量，通过调整措辞和上下文，结果会明显改善。然而，随着优化深入，会出现边际效应递减的情况：修正某个细节可能导致其他部分失效，微小改动引发不可预期的问题，模型即使在清晰指令下仍难达成目标。这时，继续微调提示词已难带来实质进步，说明进入了“噪声优化”阶段。为突破瓶颈，应改变模型周围的结构，而不仅仅是提示文本。具体策略包括将复杂任务拆解为多个原子步骤，优化外部上下文（如更优检索、更清晰的工具接口），并在动作之间加入轻量级的规划环节。举例而言，不是一次性让模型“总结仓库、修复漏洞、添加测试”，而是分步执行：先总结，再列出可能漏洞，按影响排序，逐一修复。这样每步目标清晰，便于模型聚焦。总之，提示词优化能放大良好系统设计的效果，但不能替代合理的工作流程设计；如果流程混乱，再多精细提示也难产出优质结果。理解何时是在调优提示词，何时是在调噪声，是提高效率的关键。

  
<strong>问题 1：</strong>  
为什么提示词优化在一开始效果显著，后来却会遇到边际效应递减？  

答案：  
初期调整提示词能明显改善模型理解和输出，但随着优化深入，模型开始对细微变化变得敏感，修正一处问题可能引发另一处错误，提示词优化进入“噪声”阶段，难以再带来实质性提升。  

<strong>问题 2：</strong>  
当提示词优化遇到瓶颈时，应该采取什么策略？  

答案：  
应改变模型周边的结构，将复杂任务拆解为原子步骤，改善外部上下文（如检索和工具接口），并在步骤间增加规划环节，而不仅仅是继续调整提示文本。  

<strong>问题 3：</strong>  
提示词优化与系统设计之间有什么关系？  

答案：  
提示词优化能够放大良好系统设计的效果，但如果基础工作流程混乱，再精细的提示词也无法保证稳定且高质量的输出。提示词不是替代系统设计的手段，而是辅助工具。

## Full Document
Prompting can take you a long way. 

But there’s a point where better prompts stop producing better results.

Here’s how to recognize when you’ve hit that wall — and how to move forward👇

---

1/ At first, prompt improvements have an outsized effect.

You tweak phrasing, sharpen context, and the model’s output gets visibly better.

It feels like you’re making real progress — because you are.

---

2/ But eventually, you start seeing diminishing returns:

- Fixing one edge case breaks another  
- Tiny prompt edits cause unpredictable regressions  
- The model keeps missing obvious goals, even with clean instructions

It’s not that your prompts are bad. It’s that you’re optimizing noise.

---

3/ When you reach this point, more clever wording won’t save you.

You need to change the structure around the model — not just the text inside it.

The next moves are:

- Break large tasks into atomic steps  
- Improve external context (better retrieval, cleaner tool contracts)  
- Insert lightweight planning phases between actions

---

4/ Example:

Instead of asking: "Summarize the repo, fix bugs, and add tests"

You break it into:  
- Summarize the repo  
- List possible bugs  
- Prioritize based on impact  
- Fix issues one at a time

Each step grounds the model in a specific, achievable goal.

---

5/ Prompting multiplies good system design. It doesn’t replace it.

If the underlying workflow is messy, no prompt — no matter how polished — can consistently produce clean outputs.

---

6/ Know when you're tuning the prompt — and when you're tuning noise.
