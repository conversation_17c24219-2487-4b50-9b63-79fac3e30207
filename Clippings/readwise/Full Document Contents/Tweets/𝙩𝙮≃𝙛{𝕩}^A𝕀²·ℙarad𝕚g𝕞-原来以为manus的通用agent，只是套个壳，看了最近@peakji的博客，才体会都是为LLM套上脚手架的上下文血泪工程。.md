---
人员: 
  - "[[𝙩𝙮≃𝙛{𝕩}^A𝕀²·ℙarad𝕚g𝕞]]"
tags:
  - tweets
日期: 2025-07-23
时间: None
链接: https://twitter.com/TaNGSoFT/status/1947941448437092375/?rw_tt_thread=True
附件: https://pbs.twimg.com/profile_images/1906536308492967937/shJ5rz75.jpg)
---
## Document Note

## Summary

原来以为manus的通用agent，只是套个壳，看了最近@peakji的博客，才体会都是为LLM套上脚手架的上下文血泪工程。

我们原来以为的“套壳agent”是这样的：
一个UI界面 + 一个精心设计的System Prompt + 一个ReAct循环 = 一个Agent。

我们以为只是给一个聪明的“大脑”（LLM）设计一套“衣服”和“对话脚本”，而Manus的血泪史告诉我们，现实是这样的：
真正的Agent = 一个围绕LLM构建的、极其精密的、外部化的“脚手架系统”。

包括：
稳固的地基（KV-Cache设计）：确保每一次能量输出都建立在稳定、高效的基础上，而不是流沙。
清晰的蓝图和安全绳（复述机制 & 文件系统）：时刻提醒能量源要往哪里去，并给它一个可以存放工具和草稿的工作台。
精确的轨道和限位器（屏蔽而非删除）：规定能量在特定时刻只能朝特定的方向涌动，而不是四处溅射。
一个自带“事故分析”功能的反馈回路（保留错误信息）：把每一次小型的“能量泄露”或“计算错误”，都变成加固整个系统的宝贵数据。

## Full Document
原来以为manus的通用agent，只是套个壳，看了最近[@peakji](https://twitter.com/peakji)的博客，才体会都是为LLM套上脚手架的上下文血泪工程。

我们原来以为的“套壳agent”是这样的：  
一个UI界面 + 一个精心设计的System Prompt + 一个ReAct循环 = 一个Agent。

我们以为只是给一个聪明的“大脑”（LLM）设计一套“衣服”和“对话脚本”，而Manus的血泪史告诉我们，现实是这样的：  
真正的Agent = 一个围绕LLM构建的、极其精密的、外部化的“脚手架系统”。

包括：  
稳固的地基（KV-Cache设计）：确保每一次能量输出都建立在稳定、高效的基础上，而不是流沙。  
清晰的蓝图和安全绳（复述机制 & 文件系统）：时刻提醒能量源要往哪里去，并给它一个可以存放工具和草稿的工作台。  
精确的轨道和限位器（屏蔽而非删除）：规定能量在特定时刻只能朝特定的方向涌动，而不是四处溅射。  
一个自带“事故分析”功能的反馈回路（保留错误信息）：把每一次小型的“能量泄露”或“计算错误”，都变成加固整个系统的宝贵数据。

---

本质上还是为了克服LLM的上下文窗口有限以及单轮次交互的激活路径单一性问题，为LLM驱动的agent解决具体问题提供一个包括记忆与行动的可反馈的交互环境。而这种语言认知智能的agent的价值体现还是需要回到语用哲学层面。

“语用哲学”（Pragmatics）研究的核心，恰恰就不是语言的语法（Syntax）或语义（Semantics），而是“在特定语境中，人如何使用语言来行动和达成目的”：

言语即行动（Speech-Act Theory）： 语用学认为，说话不仅仅是描述事实，更是一种行动（如承诺、命令、请求）。Manus的Agent完美体现了这一点：LLM生成的不再是“聊天的话”，而是<tool\_code>这样的“行动指令”。语言直接引发了世界（环境）的改变。

语境决定意义（Context is Everything）： 一句话的真正意思，完全取决于它所处的语境。Manus的“上下文工程”做的所有事，本质上都是在为LLM的下一次“说话”（生成行动指令）构建一个极其丰富且精确的语境。这个语境包含了历史、目标、成功经验、失败教训，从而让LLM的输出具有了明确的、服务于最终目标的“语用意义”。

意图是核心（Intention-driven）： 我们评判一个Agent的好坏，最终不是看它说的话多漂亮（语义），也不是看它的代码有没有语法错误（语法），而是看它有没有成功地理解并完成我们的“意图”（语用）。Manus用https://[t.co/XsfRF4gEUz这样的机制，就是为了让Agent时刻不忘最初的“语用目标”。](http://t.co/XsfRF4gEUz这样的机制，就是为了让Agent时刻不忘最初的“语用目标”。)

---

“如果模型的进步是上涨的潮水，我们希望Manus是那艘船，而不是被淹没的、固定在海床上的柱子。” —[@peakji](https://twitter.com/peakji)

---

铁律一：为KV-Cache而设计！你上下文里的每个字都在烧钱  
这是生产级Agent最重要的指标，没有之一：KV-Cache命中率。  
   
你的Agent每执行一步，上下文都在疯狂增长，而输出（通常是工具调用）却很短。在Manus，这个输入输出Token比例是100:1。每一次缓存失效，都意味着成本暴增10倍（以Claude Sonnet为例）。

血泪教训： 你在系统提示里加一个随时变化的时间戳，看起来很智能，实际上是亲手把KV-Cache命中率降到了零，每一次请求都在烧钱。你用不稳定的JSON序列化，键的顺序每次都变，缓存被悄无声息地击穿，而你却浑然不觉。

唯一正道：  
保持Prompt前缀绝对稳定： 一个字符的变动都会让缓存全盘失效。

上下文必须“只进不出”（Append-only）： 绝不修改历史步骤。

序列化必须是确定性的： 确保每次生成的上下文文本完全一致。  
把你的上下文当成数据库索引来优化，这才是专业玩家的素养。

---

铁律二：要“屏蔽”，不要“删除”！别把Agent自己搞糊涂

当Agent的工具库越来越大，它就越来越“蠢”，因为它会选错工具。一个自然的想法是动态加载工具，只给它当前需要的。  
   
血泪教训： 我们试过，这是个巨坑。原因有二：1. 任何对工具定义的改变，都会让处在Prompt前部的它，导致后续所有KV-Cache全部失效。2. 当历史步骤里还引用着一个你现在已经“删除”的工具时，模型会彻底精神错乱，因为它看到了一段无法理解的历史。

唯一正道：屏蔽，而不是移除（Mask, Don't Remove）。  
把所有工具定义都留在原地，但在解码（Decoding）阶段，通过“屏蔽Token Logits”的技术，动态地禁止或强制模型选择某个工具。比如，利用function calling的required或specified模式，甚至可以设计一套工具前缀命名法（如browser\_\*），从而在不改变上下文的情况下，精准控制模型的行为空间。这才是外科手术式的优雅。

---

铁律三：把“文件系统”当成无限大的上下文

128K甚至1M的上下文窗口听起来很大？在真实Agent场景里，一个网页或PDF就能轻易撑爆它。而且模型在长上下文下的表现会下降。  
   
血泪教训： 靠粗暴地截断或压缩上下文来解决问题，是饮鸩止渴。你无法预知10步之后，哪一段被你丢掉的信息会成为任务的关键。这种不可逆的压缩，风险极高。

唯一正道：把文件系统视为终极上下文。  
它是无限大的、持久的，并且Agent可以自己操作。教会它write和read。网页内容太长？从上下文中删掉，但保留URL。文档太长？删掉内容，但保留文件路径。这种“可恢复式压缩”，既减小了上下文长度，又没有永久丢失信息。这才是可扩展的思路。
