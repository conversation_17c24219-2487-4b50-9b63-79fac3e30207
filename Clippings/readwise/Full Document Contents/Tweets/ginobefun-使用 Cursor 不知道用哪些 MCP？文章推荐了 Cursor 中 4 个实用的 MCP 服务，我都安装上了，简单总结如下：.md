---
人员: 
  - "[[ginobefun]]"
tags:
  - tweets
日期: 2025-04-15
时间: None
链接: https://twitter.com/hongming731/status/1912041259532996993/?rw_tt_thread=True
附件: https://pbs.twimg.com/profile_images/1905130477033488384/_EJ-bZL7.jpg)
---
## Document Note

## Summary

使用 Cursor 不知道用哪些 MCP？文章推荐了 Cursor 中 4 个实用的 MCP 服务，我都安装上了，简单总结如下：

1. Sequential Thinking

核心能力：能把复杂问题分解为简单的步骤，便于 AI 分步处理，同时展示 AI 的思考过程。
使用场景：适合处理编程任务等复杂问题，逐步分析并优化解决方案。

2. Server Memory

核心能力：帮助 AI 记住此前交互内容，让对话更连贯。
使用场景：适合需求持续变化、多轮交互的编程和需求分析等任务，提升信息连续性和准确性。

3. Fetch

核心能力：从外部数据源获取信息，以 Markdown 格式返回，增强 AI 的信息准确性。
使用场景：适合需要外部数据支撑的任务，如专业知识查询和信息获取。

4. Playwright 和 Puppeteer

核心能力：实现浏览器自动化交互，包括网页操作、截图和执行 JavaScript 脚本。
使用场景：适合网页自动化测试和截图分析，节省大量人工操作时间。

这些 MCP 工具都通过 Cursor 简单配置和调用，能够显著提高日常开发和信息处理效率，推荐试试。

## Full Document
使用 Cursor 不知道用哪些 MCP？文章推荐了 Cursor 中 4 个实用的 MCP 服务，我都安装上了，简单总结如下：

1. Sequential Thinking

核心能力：能把复杂问题分解为简单的步骤，便于 AI 分步处理，同时展示 AI 的思考过程。  
使用场景：适合处理编程任务等复杂问题，逐步分析并优化解决方案。

2. Server Memory

核心能力：帮助 AI 记住此前交互内容，让对话更连贯。  
使用场景：适合需求持续变化、多轮交互的编程和需求分析等任务，提升信息连续性和准确性。

3. Fetch

核心能力：从外部数据源获取信息，以 Markdown 格式返回，增强 AI 的信息准确性。  
使用场景：适合需要外部数据支撑的任务，如专业知识查询和信息获取。

4. Playwright 和 Puppeteer

核心能力：实现浏览器自动化交互，包括网页操作、截图和执行 JavaScript 脚本。  
使用场景：适合网页自动化测试和截图分析，节省大量人工操作时间。

这些 MCP 工具都通过 Cursor 简单配置和调用，能够显著提高日常开发和信息处理效率，推荐试试。

![](https://pbs.twimg.com/media/GojvSNpWoAARXQA.png)![](https://pbs.twimg.com/media/GojvT94XQAA3g7-.png)![](https://pbs.twimg.com/media/GojvVDBWcAAhakZ.png)![](https://pbs.twimg.com/media/GojvYsyXAAANZfH.jpg)

---

文章链接：[bestblogs.dev/article/422136](https://www.bestblogs.dev/article/422136)

原文地址：[mp.weixin.qq.com/s/mShTSOispTM0…](https://mp.weixin.qq.com/s/mShTSOispTM0JQftcRgkrQ)
