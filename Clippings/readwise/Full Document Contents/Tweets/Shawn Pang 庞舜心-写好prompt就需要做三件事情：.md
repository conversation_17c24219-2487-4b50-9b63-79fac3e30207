---
人员: 
  - "[[<PERSON> Pang 庞舜心]]"
tags:
  - tweets
日期: 2025-04-22
时间: None
链接: https://twitter.com/0xthefool/status/1914518588515365033/?rw_tt_thread=True
附件: https://pbs.twimg.com/profile_images/1885945732831858688/uCZD8meP.jpg)
---
## Document Note

## Summary

写好prompt就需要做三件事情：

1. 找最经典最头部的prompt学习一遍，不求全懂，囫囵吞枣就可以。
直接推荐这个GitHub库，把v0, Cursor, Manus, Lovable, Devin, Replit, Windsurf的泄漏的系统及prompt汇总在了一起 。
https://t.co/WiOgS7S8at 

2. 再去读最基础的Prompt Engineering读物，对应思考看到的案例和自己要用的场景
- 谷歌的prompt engineering 101 https://t.co/Pm8cIEqAGf 
- Anthropic的 Prompt Engineering Guide https://t.co/8YMjl7LAhx 

3. POE上直接调用各个公司各个模型，多做测试迭代

## Full Document
写好prompt就需要做三件事情：

1. 找最经典最头部的prompt学习一遍，不求全懂，囫囵吞枣就可以。  
直接推荐这个GitHub库，把v0, Cursor, Manus, Lovable, Devin, Replit, Windsurf的泄漏的系统及prompt汇总在了一起 。  
<https://t.co/WiOgS7S8at> 

2. 再去读最基础的Prompt Engineering读物，对应思考看到的案例和自己要用的场景  
- 谷歌的prompt engineering 101 <https://t.co/Pm8cIEqAGf>   
- Anthropic的 Prompt Engineering Guide <https://t.co/8YMjl7LAhx> 

3. POE上直接调用各个公司各个模型，多做测试迭代
