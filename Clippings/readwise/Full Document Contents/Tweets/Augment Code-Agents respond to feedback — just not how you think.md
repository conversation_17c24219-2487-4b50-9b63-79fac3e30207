---
人员: 
  - "[[Augment Code]]"
tags:
  - tweets
日期: 2025-07-01
时间: None
相关:
  - "[[nice]]"
  - "[[model]]"
  - "[[nudge]]"
  - "[[agents]]"
  - "[[praise]]"
  - "[[signal]]"
  - "[[科技]]"
  - "[[quality]]"
  - "[[rewrite]]"
  - "[[building]]"
  - "[[feedback]]"
  - "[[momentum]]"
  - "[[redirect]]"
  - "[[teammate]]"
  - "[[iterating]]"
  - "[[reinforce]]"
  - "[[timezones]]"
  - "[[confidence]]"
  - "[[correction]]"
  - "[[corrections]]"
  - "[[continuation]]"
  - "[[encouragement]]"
  - "[[feedback loop]]"
  - "[[long sessions]]"
  - "[[course-correct]]"
  - "[[null edge case]]"
  - "[[output quality]]"
  - "[[prompt history]]"

链接: https://twitter.com/augmentcode/status/1940081118377877925/?rw_tt_thread=True
附件: https://pbs.twimg.com/profile_images/1849169714100269057/YF9iS2wt.png)
---
## Document Note

## Summary

本文指出，AI 代理（agents）对反馈的反应方式与人类不同，但反馈仍然是提升输出质量的关键。提升效果最快的方法是强化有效内容，纠正无效内容，并保持反馈循环紧密。代理不会像人类那样“学习”，但会根据提示历史中的信号调整输出，尤其是在较长会话中。有效反馈应包括肯定成功的部分，如“这很好，覆盖了主要用例”，并轻微纠正不足，如“差不多，但时间区域处理要改进”，这样既保持代理的信心，也确保质量稳定。鼓励使提示更具体，纠正让模型锁定需改进的点。相比从零开始重写，细致迭代更像团队合作。代理不需要表扬，而是需要明确的信号，因此当内容接近目标时，应通过引导和强化逐步改进，而不是简单删除重试。

<strong>问题 1：</strong>  
为什么 AI 代理对反馈的反应不同于人类的学习方式？

答案：  
AI 代理不会像人类通过理解和记忆学习，而是根据提示历史中的信号调整输出，反馈影响的是接下来生成内容的方向，而非内部知识结构的改变。

<strong>问题 2：</strong>  
如何通过反馈有效提升 AI 代理的输出质量？

答案：  
通过强化有效部分（肯定成功）、温和纠正不足（具体指出问题），并保持反馈循环紧密，使代理逐步调整，保持信心和输出质量稳定。

<strong>问题 3：</strong>  
为什么“迭代式反馈”优于“从头重写”？

答案：  
迭代式反馈像团队协作，保持已有优势和信心，细微调整不足，避免反复推翻，有助于持续稳定地提升输出质量。

## Full Document
Agents respond to feedback — just not how you think.

The fastest way to improve output quality over time?

✅ Reinforce what works  
✅ Redirect what doesn’t  
✅ Keep the feedback loop tight

Here’s how smart feedback makes agents smarter 👇

---

Agents don’t “learn” like humans. But they do respond to signals in the prompt history — especially in long sessions.

That means your feedback helps shape what comes next.

---

When something works, say so: “Nice — this covers the main use case. Let’s just handle the null edge case too.”

This encourages continuation, not correction.  
It keeps momentum high — and quality stable.

---

You can also course-correct gently: “Almost right. But this loop doesn’t handle timezones. Try again with that in mind.”

The agent takes the correction and retains the confidence. That balance matters.

---

Why this works:

- Encouragement makes prompts more grounded  
- Corrections become more specific  
- The model anchors on what not to change

It’s the difference between rewriting from scratch…

vs. iterating like a teammate.

---

Agents don’t need praise. They need signal.

So next time something’s close? Don’t just delete and retry.

Nudge. Reinforce. Keep building.
