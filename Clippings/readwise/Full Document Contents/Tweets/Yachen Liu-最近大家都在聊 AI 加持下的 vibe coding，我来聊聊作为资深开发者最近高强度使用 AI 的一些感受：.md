---
人员: 
  - "[[<PERSON><PERSON>]]"
tags:
  - tweets
日期: 2025-06-06
时间: None
链接: https://twitter.com/Blankwonder/status/1930845854346445085/?rw_tt_thread=True
附件: https://pbs.twimg.com/profile_images/1769760069393281024/vESyD2hO.jpg)
---
## Document Note

## Summary

最近大家都在聊 AI 加持下的 vibe coding，我来聊聊作为资深开发者最近高强度使用 AI 的一些感受：

一句话总结，AI 让不会写代码的人具备了“直接造辆车”的能力，而让资深开发者一个人就有了“独立建造航母”的可能。

Blog 版本：https://t.co/ftAJ6ntm1S

项目重构

最近使用 claude-4 对我之前的一些代码进行了重构。原因是原来的实现中，为了降低编写时的心智负担，会使用一些性能偏低但是易于书写的代码。比方说自动锁管理、ARC、使用 array 数据结构代替 queue。

然而用 AI 实现就没了这些负担，我先让 AI 为原始实现编写完整测试用例，确保原代码行为明确，然后让 AI 对整个 class 进行重构，追求极致性能，写完新代码后再重新运行测试保证行为一致。

就这样，我轻松完成了部分核心数据结构的重构。尽管重构后的代码量几乎翻倍，但逻辑清晰、复杂度可控，换来的则是约 20% 的性能提升。

核心是，AI 编写代码不怕苦不怕累，没有必要为了简化代码而牺牲性能。人类工程师目前主流习惯是牺牲部分运行性能以换取开发效率。

AI 编程语言

这牵扯出的另一个观察是，什么编程语言对 AI 更友好，我的观察是可读性越高、行为越明确的语言效果越好。语法糖等简化编码技术，反而不利于 AI 使用。（AI 在发现一些奇怪的行为是运算符重载导致的不知道会不会跟我一样跳脚骂街）

而像 SwiftUI 那些优势仅在开发效率上的技术，在 AI 时代更显得有些生不逢时。反正都是 AI 写，AI 用 UIKit/AppKit 实现不过是代码长一点而已，在可控性和行为明确性方面更适合 AI 自动化维护，性能也高的多。

AI 的资深

虽然 AI 的编码技能，比起资深的工程师其实可能还是会有差距，但是要论知识丰富程度，则远非任何个体可比。

这个优势体现在当我要去实现一些技术盲区时，原本的流程大概是：先读几本书，再对照比较一系列 RFC，再请教下相关领域的朋友确认自己已经理解。或者先按照自己的想象做个最小工程实践，然后再根据各种问题一点点填坑。

比方说最近在实现 IPv6 ND 协议栈，一些特定的 RA 消息构造在某些操作系统上就是无法生效，原本这可能要耗费我几天的时间去研究，阅读各种文献甚至 kernel 源码实现，而现在只需请教 AI，就能非常准确地找到答案。

AI 的这种资深，在你对某个技术的表层足够了解，但是缺乏经验和细节信息时，能够极快的帮你补全。

极强的 debug 能力

我的项目里有一个藏了很久的问题，在特定情况下会出现 TCP 性能下降，由于并没有产生任何明确的报错，这让修正这个问题变得异常麻烦。

我原本是单纯向 AI 描述了我的使用场景和问题表现，AI 提出了几种猜想，大部分我看一眼就知道不靠谱，剩下几个试了下也并无效果。索性，我直接把 100MB 的抓包结果丢给了 o3 让他分析。它在几分钟内就精准指出了问题所在，甚至给出了改进建议。这种调试能力在人类团队中几乎无法复现。

如此庞大的数据量，人工分析非常困难。即使借助各种工具，仅学习用法、配置环境就已令人头大。（因为 TCP 流控分析的各种工具链基本都是上个世纪的项目）

现在我已经习惯了这种 vibe debug，遇到什么问题，直接把 verbose 日志和问题描述丢给 AI，大概率就能直接找到问题，这其实也是得益于 AI 的不怕苦不怕累的精神。

Peer review

作为独立开发者，我的 code review 一直以来只能靠自己，但是自己写的 bug，很多时候自己是看不出来的🙈，现在我只需将 git diff 的结果交给 AI，就能请它帮我 review。

同样的，我也会 review AI 给出的结果，AI 当然也会犯错，高级低级的都有。但是比起人类同事来说，AI 没有 ego，能很好地接受反馈并立即调整；很多人类做不到，或至少过程很曲折。

职业影响

就目前 AI 的能力来看，无疑是对初级开发者就业市场产生了巨大的压力，对于资深工程师来说，反而是一种赋能。（我目前还是能为找到 AI 的错误并指导它而沾沾自喜，但也不知道还能持续多久。）

这比较让人担忧的是，这可能导致职业断层，因为初级开发者根本没有机会得到训练机会而成长。

不过这已经早已不仅仅是软件工程师所面临的问题，本质上来说，所有脑力工作者的职业都受到了巨大威胁。像咨询、律师等职业，还可以依靠私域信息门槛维持。而像医生这样完全依赖公域信息的职业，初级职位也同样完全可以被 AI 替代了，当然最终取决于患者的接受程度。

我最近一次体检后的报告喂给 o3 进行解读，他给出的信息量、准确性、建议，均远超全科医生给出的解读。不仅仅是因为 AI 的信...

## Full Document
最近大家都在聊 AI 加持下的 vibe coding，我来聊聊作为资深开发者最近高强度使用 AI 的一些感受：

一句话总结，AI 让不会写代码的人具备了“直接造辆车”的能力，而让资深开发者一个人就有了“独立建造航母”的可能。

Blog 版本：<https://t.co/ftAJ6ntm1S>

**项目重构**

最近使用 claude-4 对我之前的一些代码进行了重构。原因是原来的实现中，为了降低编写时的心智负担，会使用一些性能偏低但是易于书写的代码。比方说自动锁管理、ARC、使用 array 数据结构代替 queue。

然而用 AI 实现就没了这些负担，我先让 AI 为原始实现编写完整测试用例，确保原代码行为明确，然后让 AI 对整个 class 进行重构，追求极致性能，写完新代码后再重新运行测试保证行为一致。

就这样，我轻松完成了部分核心数据结构的重构。尽管重构后的代码量几乎翻倍，但逻辑清晰、复杂度可控，换来的则是约 20% 的性能提升。

核心是，AI 编写代码不怕苦不怕累，没有必要为了简化代码而牺牲性能。人类工程师目前主流习惯是牺牲部分运行性能以换取开发效率。

**AI 编程语言**

这牵扯出的另一个观察是，什么编程语言对 AI 更友好，我的观察是可读性越高、行为越明确的语言效果越好。语法糖等简化编码技术，反而不利于 AI 使用。（AI 在发现一些奇怪的行为是运算符重载导致的不知道会不会跟我一样跳脚骂街）

而像 SwiftUI 那些优势仅在开发效率上的技术，在 AI 时代更显得有些生不逢时。反正都是 AI 写，AI 用 UIKit/AppKit 实现不过是代码长一点而已，在可控性和行为明确性方面更适合 AI 自动化维护，性能也高的多。

**AI 的资深**

虽然 AI 的编码技能，比起资深的工程师其实可能还是会有差距，但是要论知识丰富程度，则远非任何个体可比。

这个优势体现在当我要去实现一些技术盲区时，原本的流程大概是：先读几本书，再对照比较一系列 RFC，再请教下相关领域的朋友确认自己已经理解。或者先按照自己的想象做个最小工程实践，然后再根据各种问题一点点填坑。

比方说最近在实现 IPv6 ND 协议栈，一些特定的 RA 消息构造在某些操作系统上就是无法生效，原本这可能要耗费我几天的时间去研究，阅读各种文献甚至 kernel 源码实现，而现在只需请教 AI，就能非常准确地找到答案。

AI 的这种资深，在你对某个技术的表层足够了解，但是缺乏经验和细节信息时，能够极快的帮你补全。

**极强的 debug 能力**

我的项目里有一个藏了很久的问题，在特定情况下会出现 TCP 性能下降，由于并没有产生任何明确的报错，这让修正这个问题变得异常麻烦。

我原本是单纯向 AI 描述了我的使用场景和问题表现，AI 提出了几种猜想，大部分我看一眼就知道不靠谱，剩下几个试了下也并无效果。索性，我直接把 100MB 的抓包结果丢给了 o3 让他分析。它在几分钟内就精准指出了问题所在，甚至给出了改进建议。这种调试能力在人类团队中几乎无法复现。

如此庞大的数据量，人工分析非常困难。即使借助各种工具，仅学习用法、配置环境就已令人头大。（因为 TCP 流控分析的各种工具链基本都是上个世纪的项目）

现在我已经习惯了这种 vibe debug，遇到什么问题，直接把 verbose 日志和问题描述丢给 AI，大概率就能直接找到问题，这其实也是得益于 AI 的不怕苦不怕累的精神。

**Peer review**

作为独立开发者，我的 code review 一直以来只能靠自己，但是自己写的 bug，很多时候自己是看不出来的🙈，现在我只需将 git diff 的结果交给 AI，就能请它帮我 review。

同样的，我也会 review AI 给出的结果，AI 当然也会犯错，高级低级的都有。但是比起人类同事来说，AI 没有 ego，能很好地接受反馈并立即调整；很多人类做不到，或至少过程很曲折。

职**业影响**

就目前 AI 的能力来看，无疑是对初级开发者就业市场产生了巨大的压力，对于资深工程师来说，反而是一种赋能。（我目前还是能为找到 AI 的错误并指导它而沾沾自喜，但也不知道还能持续多久。）

这比较让人担忧的是，这可能导致职业断层，因为初级开发者根本没有机会得到训练机会而成长。

不过这已经早已不仅仅是软件工程师所面临的问题，本质上来说，所有脑力工作者的职业都受到了巨大威胁。像咨询、律师等职业，还可以依靠私域信息门槛维持。而像医生这样完全依赖公域信息的职业，初级职位也同样完全可以被 AI 替代了，当然最终取决于患者的接受程度。

我最近一次体检后的报告喂给 o3 进行解读，他给出的信息量、准确性、建议，均远超全科医生给出的解读。不仅仅是因为 AI 的信息更全面，AI 可以为报告中每一项异常数据，检索最新研究与各国医疗指南，并整合后给出建议，甚至由于 GPT 已经了解我的生活习惯，能更优针对性的给出意见。而这种工作量对于人类医生来说是不可接受的（当然大多数情况下也确实没有必要）。

很多人对 AI 医疗的顾虑是：AI 犯错了怎么办？然而其实人类医生也会犯错，而且就现在的 AI 水平来看，AI 犯错的概率应该已经比一般人类医生低了。当然最优解还是兼听则明，把 AI 的意见告知医生，也把医生的反馈告知 AI，基本最后都会达成一致。对于一些不重要的小问题，仅 AI 意见完全足够。

A**I 的限制**

当然 AI 也不是万能的，甚至可以说局限性相当明显。claude-4 虽然非常强，但是随着 context 的增长，注意力溃散的非常严重，后面基本就像喝多了一样。

当前的最佳实践是：尽量保持 context 精简，聚焦具体任务，依靠人力来拆解复杂目标。

比方说先用一个 context 确定具体需求，再开一个 context 将明确好了的需求转换为具体任务列表，再把任务单独交给一个个 context 去具体实现。这样效果会好很多。

仔细一看，这不就是人类的团队协作模式嘛 😂

这让我想起不久前由 GPT o1 和 DeepSeek R1 的思维链引发的 AI 能力巨幅提升。其实在思维链能力出来之前，就可以靠 prompt 指引 AI 一步步思考，取得类似的效果，甚至催生了 prompt 工程师职业。然而直接在模型层面将这种能力整合后，prompt 引导就非常多余了。

那么目前编程实践中，如今常用的 context 切分技巧，我认为在不久的将来也可能被模型层原生支持，即 AI 自主可以通过切换 context 的方式维持注意力，保持高效。这可能带来 AI 能力的又一次飞跃式进步。
