---
人员: 
  - "[[向阳乔木]]"
tags:
  - tweets
日期: 2025-04-22
时间: None
链接: https://twitter.com/vista8/status/1914534633292882345/?rw_tt_thread=True
附件: https://pbs.twimg.com/profile_images/28889602/20070314_b0295ade0c516903fd31D3r1hlye1a1Q.jpg)
---
## Document Note

## Summary

🚨体验完字节的AI编程工具Trae的MCP版更新。

我觉得挺超预期的，不仅补齐了MCP和rules规则配置。

而且带来了“智能体”这种微创新，想象空间巨大。

真的可以用一段提示词，调用多个指定的MCP完成相当复杂的任务。

国内首个支持MCP的AI IDE，完全免费，非常值得一试。

出个教程，给大家展示下亮点：

## Full Document
🚨体验完字节的AI编程工具Trae的MCP版更新。

我觉得挺超预期的，不仅补齐了MCP和rules规则配置。

而且带来了“智能体”这种微创新，想象空间巨大。

真的可以用一段提示词，调用多个指定的MCP完成相当复杂的任务。

国内首个支持MCP的AI IDE，完全免费，非常值得一试。

出个教程，给大家展示下亮点：

![[Attachments/a1e21b6c50f13882237b55c23809963c_MD5.jpg]]

---

为什么叫Trae？

Trae = The Real AI Engineer

这么命名野心还挺大。

下载地址：<https://t.co/f8vKMUmpi8>

支持Windows、Mac M芯片和Intel芯片。

国内版：内置Deepseek R1、V3、v3-0324和Doubao 1.5 pro，同时支持自定义模型。

海外版：内置Claude 3.5、3.7 sonnet，Gemini 2.5 pro，GPT-4o、GPT-4.1，同时支持自定义模型。

---

MCP，终于来了

相比Windsurf、Cursor这些编程工具，Trae的MCP配置相对简单。

点击右上角设置图标，点击MCP。

点击添加，就能看到MCP应用市场。

带“轻松配置”标签的MCP，安装相对简单，点击添加，填写自己的Token就行。

其他安装稍微有点费劲，这也是现在MCP很难普及的原因之一。

希望字节同学，未来能把所有MCP安装方式都变成傻瓜化一键安装。

如果Trae官方找不到的MCP，可去第三方MCP应用市场找，手动配置。

![[Attachments/3abdf814dc2eceaa7d84122f8bae59ed_MD5.jpg]]![[Attachments/16a330f65329f83da685305e5bdda569_MD5.jpg]]

---

如果TARE官方找不到的MCP，可去第三方MCP应用市场找，手动配置，个人推荐这些：

<https://t.co/YBcDoMIs1D>

<https://t.co/LJHQdBBASR>

<https://t.co/50GwhGfLpC>

<https://t.co/qnSKYWkYcE>

以idoubi开发大神的https://[t.co/vL2RcizRdU为例：](http://t.co/vL2RcizRdU为例：)

首页的MCP质量都比较高，也比较流行。

比如安装Minimax，点击进入

<https://t.co/vwrFv7pUZz>

复制页面中的MCP配置文件，修改成自己的API key。

同时设置：音频保存地址，资源模式（存本地还是给远程URL），例如：

{  
  "mcpServers": {  
    "MiniMax": {  
      "command": "uvx",  
      "args": [  
        "minimax-mcp"  
      ],  
      "env": {  
        "MINIMAX\_API\_KEY": "你的API key",  
        "MINIMAX\_MCP\_BASE\_PATH": "本地存储位置，如/Users/<USER>/Desktop",  
        "MINIMAX\_API\_HOST": "<https://t.co/xiAO3iAXxI>",  
        "MINIMAX\_API\_RESOURCE\_MODE": "local"  
      }  
    }  
  }  
}

![[Attachments/c762f96013b2ca638547c4a5ab3edd0c_MD5.jpg]]

---

智能体是这次更新最大亮点！

这次Trae更新，不仅带来了MCP，而且带来一个新功能叫：“智能体”。

个人理解，就像字节的Coze一样，选一堆MCP作为工具，然后用提示词定义智能体的工作流。

这样做，想象空间超大。

搭建智能体后，以后只需要@智能体，用自然语言提需求，AI就能自动完成一些超级复杂的任务。

![[Attachments/44297b8214e5a5ca36b3c250d8782404_MD5.jpg]]![[Attachments/020037f8145ac0be339f71a5a335511d_MD5.jpg]]

---

MCP + 智能体带来的Trae新玩法

前天我写了一个Prompt。

支持把任意内容变成中英双语对照阅读材料。

但有网友提到，如果没有语音，还是哑巴英语啊。

Trae 更新MCP后，就可以轻松实现这个需求。

1. 先创建一个智能体  
2. 输入Workflow提示词  
3. 勾选要用的MCP 

提示词

1. 调用fetch，根据用户提供的网址，抓取主题内容。内容翻译成中英双语，提取CET4以上单词10个以内，解释并造句。写入一个md文件，名字根据网页取，可读性高。

2. 调用filesystem，根据抓取的文件名内容创建一个文件夹，移动md到这里，后续生成文件都放这里。

3. 调用MiniMax MCP把md文档用美式英语少女声音生成语音，下载mp3文件并改名为audio.mp3，存上面创建的文件夹中。

4. 把以上生成的音频和MD文档，制作成一个漂亮的响应式单页HTML，存上面创建的文件夹中。

![[Attachments/2bef1bd5c2c84f1a3509e43072f2fb5d_MD5.jpg]]

---

运行时，只需要@智能体，输入：“处理 URL”。

智能体就开始干活儿，一会儿网页就做好了。

真的可以边听边学习。

![[Attachments/1c98dd6695cbee4f49d100db34e82f26_MD5.jpg]]![[Attachments/616c28b78c19103b81962c35ec6358c4_MD5.jpg]]![[Attachments/dbef1c83f70a64edff8f0b18795b8d80_MD5.jpg]]

---

更强大的上下文

对于AI工具来说，上下文极其重要，AI编程工具也不例外。

Trae的这次更新，除了MCP和智能体，上下文强化，也非常值得点赞。

支持联网搜索、文档集等丰富内容。

能通过URL，本地上传方式添加常用的文档集。

这相当实用，如果不会写什么功能，只需找到有类似功能的Github仓库URL，丢给AI，让它帮你读代码、写代码。

联网搜索找资料也是刚需。

最最最值得提的是，Trae也开始支持Rules，就像给 AI 定“工作手册”。

用过 AI 编程的人都知道，AI并没想象中聪明，而且每次生成的函数名、接口名都可能不一样。

找类似Bug，真的想骂街。

如果把一些要求写入到Rules里，编程时就能少犯类似的简单错误。

而且一些专业Rules，能有效提升编程效率和一次运行成功率。

![[Attachments/b31e0bd77e75ae521a2c2eb40980e054_MD5.jpg]]![[Attachments/7d97d1d56ae3355c3fe374313409ece1_MD5.jpg]]

---

写在后面

据说原MarsCode编程助手更名为Trae 插件，也支持了Builder模式，可根据自身需求自由选择或结合 Trae 使用。

给我的感觉：字节跳动在 AI 编程领域投入巨大，势在必得。

在 AI 编程工具赛道这么卷的今天，团队依然有耐心，做出“智能体”这种微创新，非常值得夸赞。

为什么呢？这种设计不仅体验好，也规避了一大堆MCP插件调用混乱的问题。

希望进一步优化 MCP 安装体验（虽然已经比很多产品好了）。

让用户无感一键安装，甚至不需要知道什么是MCP，用户只需要关心AI的处理结果

那时候一定就是成了！

---

附录：个人觉得比较实用的MCP推荐

• MiniMax MCP： 国内性价比最好的语音API，把文本转成语音，需充值付费。

• EdgeOne Pages MCP： 把网页部署到腾讯的服务器，会返回一个临时访问网址，免费。

• Amap Maps： 高德地图MCP，查找饭馆、咖啡厅，交通线路等，申请API免费用。

• Sequential Thinking： 将你的任务（提示词）分解为更小的步骤，一步步顺序思考执行，免费。

• Firecrawl MCP Server： 当下比较有名的内容搜索抓取MCP，需申请Key，有些免费额度。类似的还有Brave Search（绑定信用卡或免费额度）、Tavily等。

• Filesystem： 读写电脑本地文件。比如整理你的下载文件等，免费。

• Puppeteer： 自动化控制Chrome内核的测试浏览器，模拟真人操作，抓取内容等，类似的还有微软的Playwright。

• Fetch： 简单的抓取网页内容并转换为Markdown。

还有很多笔记软件支持MCP。

如Obsidian、苹果备忘录等，但安装都稍微有点复杂，以后有机会讲。

另外，昨天用AI写了一个七牛云的MCP，通过自然语言控制上传音频、图片和网页，非常实用，项目已开源

<https://t.co/dHBAZqjwhd>

未来，MCP生态会更加繁荣，言出随法，过瘾的狠。

教程就写到这里，如果有问题，欢迎留言交流。

MCP配置还是坑不少，希望加速进化。

---

公众号原文地址：  
[mp.weixin.qq.com/s/m8AB9qsr45\_j…](https://mp.weixin.qq.com/s/m8AB9qsr45_jS3mzlQybag)

阅读体验可能更好。

同时再打个广告，关注我的公众号：“向阳乔木推荐看”。

2-3天会更新一篇，我在AI领域的想法和实战教程。

![[Attachments/f17d5d666412016b24614bbf74a87af6_MD5.jpg]]
