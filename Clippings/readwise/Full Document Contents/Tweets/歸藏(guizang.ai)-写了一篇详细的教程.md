---
人员: 
  - "[[歸藏(guizang.ai)]]"
tags:
  - tweets
日期: 2025-05-06
时间: None
链接: https://twitter.com/op7418/status/1919669181852877093/?rw_tt_thread=True
附件: https://pbs.twimg.com/profile_images/1636981205504786434/xDl77JIw.jpg)
---
## Document Note

## Summary

写了一篇详细的教程

教大家如何生成这种一图流模型或者产品介绍宣传图

顺便教一下怎么用 Figma 修改从网页生成的图片

## Full Document
写了一篇详细的教程

教大家如何生成这种一图流模型或者产品介绍宣传图

顺便教一下怎么用 Figma 修改从网页生成的图片

![](https://pbs.twimg.com/media/GqQIqwIaoAAH68b.jpg)

---

如果等不及施工可以看这里：[mp.weixin.qq.com/s/uQQ7R8rBUXZ6…](https://mp.weixin.qq.com/s/uQQ7R8rBUXZ6EoxX4WxMRg)

---

也可以参考橘子这个：[x.com/oran\_ge/status…](https://x.com/oran_ge/status/1917215213717434827)

---

生成网页

我们就以 DeepSeek-Prover-V2 这个例子介绍一下，还是我之前讲 Vibe Coding 说的第一次生成结果至关重要，所以我们需要准备一些东西。

首先是模型的论文或者介绍博客：  
如果你要介绍产品更新也是一样，需要上传文档，论文的话就是 PDF 就行，如果是介绍博客就直接全选网页内容保存一个 Markdown 文件就行，也可以用我之前也介绍的 Obsidian 剪藏插件获取。

然后就是输入提示词了：  
这里我们只是基于藏师傅网页生成 3.0 提示词上加了一句话“尽量在一页展示全部信息”。  
这里你可以根据模型品牌的主题色更改第一条的颜色部分，比如 Qwen 就是白色背景紫色高亮，Grok 就是暗色背景橙色高亮。

![](https://pbs.twimg.com/media/GqQJnEJaUAAs9iQ.jpg)

---

提示词：

基于模型发布文档的关键信息，帮我用类似苹果发布会PPT的Bento Grid风格的视觉设计生成一个中文动态网页展示，具体要求为：

1. 尽量在一页展示全部信息，背景为白色、文字和按钮颜色为纯黑色，高亮色为#4D6BFE  
2. 强调超大字体或数字突出核心要点，画面中有超大视觉元素强调重点，与小元素的比例形成反差  
3. 网页需要以响应式兼容更大的显示器宽度比如1920px及以上  
4. 中英文混用，中文大字体粗体，英文小字作为点缀  
5. 简洁的勾线图形化作为数据可视化或者配图元素  
6. 运用高亮色自身透明度渐变制造科技感，但是不同高亮色不要互相渐变  
7. 数据可以引用在线的图表组件，样式需要跟主题一致  
8. 使用HTML5、TailwindCSS 3.0+（通过CDN引入）和必要的JavaScript  
9. 使用专业图标库如Font Awesome或Material Icons（通过CDN引入）  
10. 避免使用emoji作为主要图标  
11. 不要省略内容要点

---

一般这个时候第一次生的结果就已经不错了，如果没有问题的话你就可以用最大的显示面积截图就行。

但是经常会有一些小问题，比如在布局上可能会出现的问题有标题没有加上卡片和边框，或者在某个部分的卡片没有占满全部的空间，比如我 Deepseek 这个就有问题。

这种问题让 AI 改的话他就会全部生成，而且我们也不好描述，这时候就可以导入到 Figma 我们手动调整一下，可能就十几秒就能搞定。

---

Figma 微调设计稿

首先我们需要找到这次要用的核心 Figma 插件 html. to. design 只需要在随便一个 Figma 文件里面点击下方 Tab 栏圈住的图标之后搜索就行。

![](https://pbs.twimg.com/media/GqQKcG8bAAApcEI.jpg)

---

然后我们输入需要导入的网页地址，如果你没有的话可以用 Youware 部署，然后点击 Import 按钮就行.

之后导入一般的之后他会让你选一下其他选项，这里除了不让开的开关你都开了就行，然后 Font Mappings 字体设置这里需要选替换字体，如果你的网页有中文的话我推荐你换成 Pingfang SC。

![](https://pbs.twimg.com/media/GqQKlA7bAAEpdL2.jpg)![](https://pbs.twimg.com/media/GqQKlWPbAAIIaY3.jpg)

---

然后你就能看到导入的网页了，是一个完整的画板，里面还有 Youware 的下导航这里有很多我们不需要的部分，你就可以直接选中这部分删掉就行。

删掉没用的 tab 栏之后我们发现 Iframe 这个里面也没有内容，然后实际的内容都在红框没有权重的那个 Iframe 里面。

所以我们需要删掉最下面的Iframe图层，之后讲 Container 和 1920w light 取消编组，这样我们就剩下了真正有用的部分。

![](https://pbs.twimg.com/media/GqQK6SCbAAEj4qO.jpg)![](https://pbs.twimg.com/media/GqQK-b4bAAA1BxB.jpg)

---

终于要进行我们的改动了

首先我们希望给头部的标题也加上卡片，这时候我们选了一下发现头部 Header 的宽度比下面所有卡片加起来的宽度是要宽的所以先把他们的宽度统一改成 1472px。

之后我们想要复制下面灰色卡片的样式而不需要他的内容，只需要随便选一个下面的灰色卡片，然后右键-复制粘贴为-复制属性就行，粘贴也是一样选择上么的 Header 卡片选择粘贴属性，你就发现标题也有卡片了。

接下来我们修复，模型规模这里的卡片没有占满全部空间的问题，选中模型规模的卡片，按住 Option 按钮我们发现他到双模训练卡片的宽度是 398 然后他们需要有 24px 的间距。

所以模型规模的卡片宽度应该为 350+398-24，你直接在宽度输入框写数学公式就行，Figma 会帮你算好的，现在是不是 OK 了。

![](https://pbs.twimg.com/media/GqQLLhRbAAE5_iF.jpg)![](https://pbs.twimg.com/media/GqQLM-ubAAEXCLc.jpg)![](https://pbs.twimg.com/media/GqQLOaNbQAA6LiM.jpg)![](https://pbs.twimg.com/media/GqQLPqjaYAAthlr.jpg)

---

最后我们做导出前的最后一步，整个卡片四周的边距有些问题，左右很宽上下很窄，我们想要他们一样，这个时候我们只需要选中 html-Body 这个图层，然后把红框部分的间距都改成统一的 32 就行。

之后将html-Body 宽度这里改成适应内容。

最后将最上面的 Iframe 画框右键取消编组，你会发现已经搞定了，然后我们选中这个画板，把最右侧拉到底部在导出这里点一下加号，然后点击导出按钮就行。

![](https://pbs.twimg.com/media/GqQLYg2b0AA1YHp.jpg)![](https://pbs.twimg.com/media/GqQLaAebAAA_1T7.png)![](https://pbs.twimg.com/media/GqQLbjuakAApvFE.jpg)

---

如果你想要想我上面的的展示图片那样给图片加个渐变边框的话可以用 postspark（https:// postspark. app/screenshot） 这类工具。

![](https://pbs.twimg.com/media/GqQLiwKbEAAcNON.jpg)

---

好了，今天的图片生成教程就到这里了。

最后欢迎在评论区或者其他平台 @ 我，交作业
