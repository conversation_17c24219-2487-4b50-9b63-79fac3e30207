---
人员: 
  - "[[orange.ai]]"
tags:
  - tweets
日期: 2025-04-22
时间: None
链接: https://twitter.com/oran_ge/status/1914656168691294306/?rw_tt_thread=True
附件: https://pbs.twimg.com/profile_images/1760074488073629696/ceSsuVCY.png)
---
## Document Note

## Summary

今天字节的 AI 编程工具 Trae 迎来超重磅更新：

- 自定义智能体
- MCP
- 全局的自定义规则
- 网页和文件作为上下文

四个功能都很强，直接把 Trae 的功能带到了一线 AI 编程工具产品的行列。
特别是自定义智能体+MCP，这是言出法随的新境界。
就着这次更新，我也终于写完了小白入门级 MCP 教程！
👇

## Full Document
今天字节的 AI 编程工具 Trae 迎来超重磅更新：

- 自定义智能体  
- MCP  
- 全局的自定义规则  
- 网页和文件作为上下文

四个功能都很强，直接把 Trae 的功能带到了一线 AI 编程工具产品的行列。  
特别是自定义智能体+MCP，这是言出法随的新境界。  
就着这次更新，我也终于写完了小白入门级 MCP 教程！  
👇

![](https://pbs.twimg.com/media/GpI5Wg3a4AEZVU5.jpg)![](https://pbs.twimg.com/media/GpI5ZT6a4AEfdOx.jpg)![](https://pbs.twimg.com/media/GpI5fTaacAAEI8c.jpg)![](https://pbs.twimg.com/media/GpI5iaCa4AEcWU6.jpg)

---

首先是Trae 的下载和安装 ，直接点击下载安装即可，支持Windows和Mac  
 [sourl.cn/5p7ePD](https://sourl.cn/5p7ePD)  
Trae 的国内版最近一个特别棒的更新是支持了添加自己的模型了。编程方面最强的还是 Claude，我们这次也用它来实战。如果你用的是 Trae 的海外版，它已经自带了 Claude，开箱即用。

![](https://pbs.twimg.com/media/GpI5-dPa4AA9-Bt.jpg)

---

MCP 的两种模式  
MCP 现在一共有两种模式，SEE 和 Stdio：  
SEE 的配置方式非常简单，只有一个链接，基本上都是 Stdio 的方式，然而这种 MCP 很少。  
Stdio 是主流的 MCP 方式，我们要用 MCP，肯定要搞定它。

搞定它安装2个命令行工具：  
一个是 uvx，一个是 npx。

UVX 的安装  
uvx 我们需要安装 uv：

Windows 用户：可以按“Win”键，点击搜索后输入"PowerShell"，然后右键选择“以管理员身份运行”，粘贴下面的命令回车就行，运行完记得重启。

powershell -ExecutionPolicy ByPass -c "irm <https://t.co/RxLoK097vx> | iex"

Mac 用户，点击“启动台”搜索“终端”应用，然后输入下面的代码回车就行。

curl -LsSf <https://t.co/Yjh3umT4J8> | sh

这时候你大概率会遇到第一个坑：

这是因为这个网址访问不了。  
请自行开启科学上网，并打开全局+增强模式，多试几次。

UPX 的安装  
Upx 的安装就比较简单了，只需要安装 Node.js 就行，访问官网（<https://t.co/0VFEIbLezO）点击下载正常安装就行。>

恭喜你，终于安装完了 MCP 的基础工具，在这里你已经超过了 99.99% 的人。

为你感到骄傲。

最难的部分已经过去了，接下来让我们打开 Trae，开始安装我们需要的 MCP。

![](https://pbs.twimg.com/media/GpI6SSia4AMVcP-.png)![](https://pbs.twimg.com/media/GpI6hqAaMAA4TU_.jpg)

---

自定义智能体听起来好像没啥用。  
但在我 Vibe Coding 的快要绝望的时候，捏了个智能体，一下就成功了。  
后来我猜测是默认 prompt 太针对程序员了，工程都太复杂，vibe coding 需要的是简单快速。
