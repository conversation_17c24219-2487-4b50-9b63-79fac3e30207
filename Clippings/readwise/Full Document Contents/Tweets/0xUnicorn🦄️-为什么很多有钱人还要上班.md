---
人员: 
  - "[[0xUnicorn🦄️]]"
tags:
  - tweets
日期: 2024-09-01
时间: None
链接: https://twitter.com/0xUnicorn/status/1830210224793534473
附件: https://pbs.twimg.com/profile_images/1646533981297594368/T10GcY77.jpg)
---
## Document Note

## Summary

为什么很多有钱人还要上班?

底层的人确实很难理解有钱人的上班行为，当年我也是这样，后来发现在井里待久了，难免会认为天就那么点大。。

来点干货吧， 教大家理解有钱人的工作：

1.当你处于最底层的时候，你的上班是与客观世界具体对接的。

比如我是车工，我的工作就是车一个盘子，然后赚了一个加工的人工费。这样上班的原理在于，你做了A，必然就会得到A对应的劳动回报。那么换言之，你不做A，就得不到A对应的回报。收入和劳动是典型的线性增长。

由于人的体力是有限的，而这样的劳动，毫无自由度可言，会让你很容易估算出这一辈子能赚多少钱，这样的上班，仅有物质回报，精神层面的回报几乎为0。

2.到了第二层面，即底层+1的时候，工作的特色往往开始有部分精神层面的回报，而物质回报的线性趋势，开始变得模糊。但是工作回报和劳动强度依然成正比，人对客观规律的应用和收入回报联系不大。

比如我是个文员，那么我每天会有做材料、打印、回报文件、做会议记录等等工作，而我每天多做一点或者少做一点，和我的收入影响不会很大。这样就开始了我做了A+-n，获得了同样A的回报。

由于这个层面的工作存在n这个自由度，只要这个n不出格，那么这样就有了自由调整的空间。往负的方向走，可以划水，往正的方向走，可以多接触新事物锻炼自己。这样开始，除了物质层面以外，就有了精神层面的回报。但是问题在于，n的值往往很渺小，实现量变需要很长很长的时间，以至于几乎没有盼头。

3.到了第三个层面，即底层+2的时候，工作的劳动强度往往和收入开始变得联系不大，物质回报的层面开始有脱节的趋势，个人对客观规律的理解应用，放在了台面上。工作的主题从具象世界，开始转向抽象世界。

比如我是个销售小头目，手下三五个人。或者我是个屌丝项目经理，手里就一个苦力。组内和部分的正常工作，有没有我参与，其实无关紧要。甚至个把星期不去上班，本质上也不会有太大影响。但是我在关键时候，必须能顶好几个人的水平来用：大客户来了，下面人摆不平，我要能摆平。非标准的项目来了，我能重新拆解分工，然后能拼得完整。这个时候我所经历的经验、我所掌握的知识才是立足之本，即我对客观规律的理解应用。

到了这个层面，我做了ABCDEFG的劳动强度和我的收入关系不大。如果你很牛逼，那么上班可以天天挂机划水，如果你很菜，那么肯定会累成狗还没什么钱。从经济学讲，掌握了知识、经验，本质上就是掌握了部分生产资料，这个层面的特点，就是开始利用生...

## Full Document
为什么很多有钱人还要上班?

底层的人确实很难理解有钱人的上班行为，当年我也是这样，后来发现在井里待久了，难免会认为天就那么点大。。

来点干货吧， 教大家理解有钱人的工作：

1.当你处于最底层的时候，你的上班是与客观世界具体对接的。

比如我是车工，我的工作就是车一个盘子，然后赚了一个加工的人工费。这样上班的原理在于，你做了A，必然就会得到A对应的劳动回报。那么换言之，你不做A，就得不到A对应的回报。收入和劳动是典型的线性增长。

由于人的体力是有限的，而这样的劳动，毫无自由度可言，会让你很容易估算出这一辈子能赚多少钱，这样的上班，仅有物质回报，精神层面的回报几乎为0。

2.到了第二层面，即底层+1的时候，工作的特色往往开始有部分精神层面的回报，而物质回报的线性趋势，开始变得模糊。但是工作回报和劳动强度依然成正比，人对客观规律的应用和收入回报联系不大。

比如我是个文员，那么我每天会有做材料、打印、回报文件、做会议记录等等工作，而我每天多做一点或者少做一点，和我的收入影响不会很大。这样就开始了我做了A+-n，获得了同样A的回报。

由于这个层面的工作存在n这个自由度，只要这个n不出格，那么这样就有了自由调整的空间。往负的方向走，可以划水，往正的方向走，可以多接触新事物锻炼自己。这样开始，除了物质层面以外，就有了精神层面的回报。但是问题在于，n的值往往很渺小，实现量变需要很长很长的时间，以至于几乎没有盼头。

3.到了第三个层面，即底层+2的时候，工作的劳动强度往往和收入开始变得联系不大，物质回报的层面开始有脱节的趋势，个人对客观规律的理解应用，放在了台面上。工作的主题从具象世界，开始转向抽象世界。

比如我是个销售小头目，手下三五个人。或者我是个屌丝项目经理，手里就一个苦力。组内和部分的正常工作，有没有我参与，其实无关紧要。甚至个把星期不去上班，本质上也不会有太大影响。但是我在关键时候，必须能顶好几个人的水平来用：大客户来了，下面人摆不平，我要能摆平。非标准的项目来了，我能重新拆解分工，然后能拼得完整。这个时候我所经历的经验、我所掌握的知识才是立足之本，即我对客观规律的理解应用。

到了这个层面，我做了ABCDEFG的劳动强度和我的收入关系不大。如果你很牛逼，那么上班可以天天挂机划水，如果你很菜，那么肯定会累成狗还没什么钱。从经济学讲，掌握了知识、经验，本质上就是掌握了部分生产资料，这个层面的特点，就是开始利用生产资料赚钱，而非生产力。而在这个过程中，工作的主体不再是车一个轮子、拆俩轴承这种具体事物，而是事物与事物的关系，人与人的利益共谋或者分配等问题。由于这种工作转向为对抽象世界的对接，精神收益大幅度增加，工作开始变得有意思。

4.到了第四个层面，即底层+3的时候。工作的主题是领导一个组织架构，通过合理的分配内部的资源，从而达到和这个组织架构相关联的事物形成闭环的能力。从此往后，劳动开始变得没有任何意义。但是这个层面的工作，在于与该组织架构相关联的事物或组织是相对固定的，所以这个层面的工作，有了工作时间安排上的完全自由度，而没有操作层面的完全自由度。

比如我是个技术总监/副总，手里几个小科室或者部门。那么我需要做的工作，是领导整个技术部门合理的和销售以及采购对接。如何对自己手里几十号甚至几百号人进行优化重组？销售部门一个技术需求来了，这个需求在我自己部门里面每个科室的流程是如何通过？核心环节的权责利如何切得干净而且还平衡？哪些事务是要给下面的人有自由度的，哪些又是要锁死的?这类都属于内部事务的管理。

对外的事物有：销售部门的需求中，事少钱多的怎么突现自己贡献？难啃骨头的怎么怼得对方无话可说？采购这边要如何让他们多开口子用些好的配套让我们部门省事？竞品技术分析中，明明我们55开，怎么喷得销售自惭形秽？明明成本比对方高，怎么说得采购部门面有愧色等等。同时还要一定程度上处理和上级的关系，甚至洞察理解董事会的决策动向等等。

这个层面的工作，只要你玩得转，找机会不上班的理由比比皆是，很多是可以自由安排工作时间的。但是你无法改变你是个技术部门的事实，所以你无法做采购或者销售等类型事情，故缺乏操作层面的自由度。

到了这个层面，事情几乎无法达到最优解。本身的工作一定程度上是动态的，是需要随着隔壁几个部门变化而变化的。由于事情没有最优价解，甚至没有标准答案。所以很多工作仅是为了自己这个部门不出岔子即可。所以这里面有开荒的，也有守成的。开荒的能力要求很高，即从0开始组建整个部门，一直到部门可以脱手自动运转。而守成的人要求往往很低，甚至有些开荒人太优秀了，部门可以适应很长一段时间的外部变化还不出问题，以至于守成的人是条狗都行。这些抛开不说。

这个层面是傻逼和牛逼差距最大的一个层面，也是利益最为错综复杂的一个层面。根据不同的外部环境，这个层面可以很爽，也可能很累。收入与劳动强度毫无关系，甚至很多时候与个人能力也没太大关系。你在的外部环境，往往就基本上决定了你的收入和劳动强度，所以也是跳槽最多，忠诚度最低的一个层面。

5.到了第五个层面，即底层+4的时候。工作的主题是领导一个组织架构，和客观世界对接。此时的工作，即具备工作时间上的自由度，也具备操作层面的自由度。抛开一部分人喜欢强行参与前面几个层面工作的“微操大师”，这个层面的唯一工作，就需要作出正确的决策。

这个就很容易理解了，你开个公司，你要让整个公司内部一坨子人，按照你的方向前进。和上个层面不同的是，很多时候你自己并不需要很牛逼。

即马云不会编程，只要他有资源能挖到会编程的人即可。但是马云必须要做出正确的决策。

所以，这个层面的人，需要大量且高质量的信息，通过自己大脑里对客观世界理解的最优模型，作出一个最优解。这个最优解往往一句话就能说清，但是需要大量的资源来得出。在这个产出过程中，会花钱请行业里面的高手给与建议；会请一些海量人情的牛人进行提点；会有无数个前面1234层面的人工花费上千上万个人工采集的信息数据；而这些东西集成在一起所带来的头脑风暴，给人所享受的精神洗礼，根本是普通人层面无法体会的。这样的工作所带来的享受，根本是普通生活中去喝酒吃肉玩嫩模所能媲美的。任何一个普通人，能有幸旁观经历过一次这样的工作，一辈子都会刻骨铭心。所以这个层面的玩家，工作即生活，生活即工作。

当然，这些只是这个层面最顶配的工作。很多没有这么爽但是也还行的减配版，很多可能是个国企的头目，新三板的大股东。而生活中绝大部分的，可能是个小公司的几个年轻创始人、手里有几个矿的路虎车主等等。

当然，大家见得最多的，就是哪些穿个人字拖提着几串钥匙的背心佬。

也许你会问，难道这些背心佬，每天工作的时候也会有那样浩瀚的头脑风暴么？

我们，就是风暴
