---
人员: 
  - "[[<PERSON><PERSON><PERSON><PERSON>]]"
tags:
  - tweets
日期: 2025-05-15
时间: None
链接: https://twitter.com/lipeng0820/status/1922885200561963426
附件: https://pbs.twimg.com/profile_images/1640053543825723393/tKGzLvKB.jpg)
---
## Document Note

## Summary

🎨初体验https://t.co/8RJf2dy4QX 做儿童绘本转动画

文本早先的ChatGPT编写，加上提示词后直接给到Lovart

工作流拆解：
0.理解提示词
1.选择视觉风格
2.Flux设计角色
3.分镜设计（使用HTML呈现）
4.可灵1.6生成视频
5.Suno生成BGM
6.TTS生成旁白
7.视频合成

51秒动画最终生成总耗时37分钟，成片⬇️

## Full Document
🎨初体验https://[t.co/8RJf2dy4QX](http://t.co/8RJf2dy4QX) 做儿童绘本转动画

文本早先的ChatGPT编写，加上提示词后直接给到Lovart

工作流拆解：  
0.理解提示词  
1.选择视觉风格  
2.Flux设计角色  
3.分镜设计（使用HTML呈现）  
4.可灵1.6生成视频  
5.Suno生成BGM  
6.TTS生成旁白  
7.视频合成

51秒动画最终生成总耗时37分钟，成片⬇️

Your browser does not support the video tag.

---

一刀未剪生成全过程：[youtu.be/595I3\_OxCdk](https://youtu.be/595I3_OxCdk)
