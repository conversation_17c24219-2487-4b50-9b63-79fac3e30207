---
人员: 
  - "[[orange.ai]]"
tags:
  - tweets
日期: 2025-01-27
时间: None
链接: https://twitter.com/oran_ge/status/1883835723734012114/?rw_tt_thread=True
附件: https://pbs.twimg.com/profile_images/1760074488073629696/ceSsuVCY.png)
---
## Document Note

## Summary

在学习推理模型的 Prompt 技巧，确实跟之前略有不同
DeepSeek R1，o1，Gemini think 这种深度思考模型太高级了，输出的适合经常喜欢装深沉
Prompt 里加上「说人话」「通俗易懂」「中学生能看懂」这样的提示词，用户体验立即提升。

## Full Document
在学习推理模型的 Prompt 技巧，确实跟之前略有不同  
DeepSeek R1，o1，Gemini think 这种深度思考模型太高级了，输出的适合经常喜欢装深沉  
Prompt 里加上「说人话」「通俗易懂」「中学生能看懂」这样的提示词，用户体验立即提升。

![](https://pbs.twimg.com/media/GiS586xa8AAihpr.jpg)

---

深度思考模型的文风分析能力非常之强，以前你可能要写很多提示词来定义文风，但是现在AI自己就可以分析出来，你只需要这么简单地说：  
请模仿xx的文风，写一篇文章，文章标题《xx》

![](https://pbs.twimg.com/media/GiS9bfQbMAAtmHp.jpg)![](https://pbs.twimg.com/media/GiS9xQwbMAALQ3K.jpg)

---

深度思考模型的场景想象力非常强，只需要你给它一个合适的场景，无须过多要求，它就能写出适合的文字片段：  
比如这个「当老黄看到英伟达的股价因为DeepSeek R1的爆火而暴跌16%，写出他的内心独白」  
可以对比下 R1、Gemini Think、O1 Pro、Claude 的表现

![](https://pbs.twimg.com/media/GiTBT-Ja0AAjqdN.jpg)![](https://pbs.twimg.com/media/GiTBT-HbAAASlqL.jpg)![](https://pbs.twimg.com/media/GiTBtaFagAArWLm.png)![](https://pbs.twimg.com/media/GiTB2o0bwAAlLnx.jpg)

---

带一点古文风格的文章是 DeepSeek R1 的绝对强项。  
可以通过共振式 Prompt 描述场景去激发模型的想象力和感受力，而不要通过规则去定义。  
比如「龙年岁末，除夕之夜，初雪落下，李白独酌，突然想起杜甫，挥笔写下一篇文章，惊艳世人，请写出这篇文章」  
你能看到模型对这个命题的思考，文风，主题，用词，段落结构，引用诗句，主题情感，可谓面面俱到。  
在写作时，模型还增加了背景设定，进一步引导自己进入状态。  
写出来的这篇作品通过虚实相生的笔法，在历史框架中植入奇幻元素。雪夜独酌的场景既符合李白豪放性格，又暗合杜甫"乱云低薄暮，急雪舞回风"的诗境。结尾处星斗阑干的意象，取自李白《长相思》"络纬秋啼金井阑"，却赋予其新的时空交融意味，展现两位诗人超越现实困境的精神共鸣。

![](https://pbs.twimg.com/media/GiTEoK4bQAARFdy.jpg)![](https://pbs.twimg.com/media/GiTFPdoagAESEoX.png)

---

ollama 的 deepseek 7b 是没有这么高级的写作能力的，而且还有很严重的中英文混杂现象。

![](https://pbs.twimg.com/media/GiTIhrMaMAAGqCJ.jpg)
