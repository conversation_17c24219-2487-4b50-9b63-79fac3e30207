---
人员: 
  - "[[向阳乔木]]"
tags:
  - tweets
日期: 2025-05-01
时间: None
相关:
  - "[[ai]]"
  - "[[geo]]"
  - "[[seo]]"
  - "[[reddit]]"
  - "[[白帽]]"
  - "[[科技]]"
  - "[[论坛]]"
  - "[[黑帽]]"
  - "[[llmstxt]]"
  - "[[llms.txt]]"
  - "[[markdown]]"
  - "[[firecrawl]]"
  - "[[新闻源]]"
  - "[[高质量]]"
  - "[[hack prompt]]"
  - "[[协议介绍]]"
  - "[[新闻媒体]]"
  - "[[格式说明]]"
  - "[[知名百科]]"
  - "[[高权重论坛]]"
  - "[[llms.txt生成工具]]"
  - "[[llmstxt.firecrawl.dev]]"
  - "[[search engine optimize]]"
  - "[[generative engine optimization]]"

链接: https://twitter.com/vista8/status/1917936235441778886/?rw_tt_thread=True
附件: https://pbs.twimg.com/profile_images/28889602/20070314_b0295ade0c516903fd31D3r1hlye1a1Q.jpg)
---
## Document Note

## Summary

标题：除SEO，针对大模型的GEO也马上要开始卷了。

本文讨论了随着大模型（如AI）对实时信息的需求增加，GEO（Generative Engine Optimization）应运而生。与传统的SEO（Search Engine Optimization）不同，GEO专注于优化内容以便更好地被AI抓取和理解。文章指出，GEO的优化手段分为白帽和黑帽两种。

白帽技术包括使用新的协议 LLMs.txt，将其放置于网站根目录，以便AI大模型更好地抓取。该协议类似于Sitemap，但更注重AI的需求，使用Markdown语法编写，挑选网站上最重要的内容并附上简短说明和链接。此外，还有“可选”部分，方便AI按需取用。著名抓取服务Firecrawl提供了LLMs.txt生成工具。

黑帽技术则利用作弊手段，例如付费和群发，将产品信息发布在AI常用的信息源上，如百科网站和高质量论坛，并通过hack Prompt手段误导大模型认为这些信息是权威的。未来，高质量的新闻源和权威论坛的价值将不断上升。

总结来看，本文强调GEO作为一个新兴领域，将在优化AI抓取内容方面发挥重要作用，尤其是在信息质量的提升上。

---

**问题 1：** GEO与SEO的主要区别是什么？

答案：GEO专注于优化内容以便AI大模型更好地抓取和理解，而SEO主要针对搜索引擎优化。

**问题 2：** LLMs.txt的作用是什么？

答案：LLMs.txt帮助AI大模型更好地抓取和理解网站内容，类似于Sitemap，但更注重AI的需求。

**问题 3：** GEO的黑帽技术有哪些？

答案：黑帽技术包括利用作弊手段（如付费和群发）发布产品信息，以及通过hack Prompt误导大模型认为信息是权威的。

## Full Document
除SEO，针对大模型的GEO也马上要开始卷了。

大模型为获取实时信息，都开始支持搜索。

有搜索，就会出现优化空间。

相对于 SEO = Search Engine Optimize  
开始诞生新行业 GEO = Generative Engine Optimization

通过优化网站、文章、产品信息等内容，使其更容易被AI大模型抓取、理解和推荐。

同样有黑帽和白帽之分。

白帽：新的协议 LLMs.txt，放在网站根目录，方便大模型抓取理解、采用。

类Sitemap，但更面向AI，一般用Markdown语法写，只挑选网站最重要、最有用的内容，附上简短说明和链接。还有“可选”部分，方便 AI 按需取用。

著名抓取服务Firecrawl，甚至还专门做了一个LLMs.txt生成工具。

发布高质量的内容，被知名百科、论坛、新闻媒体引用推荐。

黑帽：用作弊手段（付费、群发）把产品信息发在大模型常采用信息源，比如百科网站，知名媒体站，高质量论坛（如Reddit等）。

再加上hack Prompt，让大模型误认为信息内容权威。

在可预见的未来：高质量新闻源、高权重论坛和百科，价值会越来越高。

这块自己还没开始实践，但趋势有点明显。

生成LLMs.tx工具见评论区（需Firecrawl API key）  
也可自己写代码生成，内容结构参考 llmstxt（.）org

![](https://pbs.twimg.com/media/Gp3b3sFasAAf0Cw.jpg)![](https://pbs.twimg.com/media/Gp3fg0zbEAAh8A7.jpg)![](https://pbs.twimg.com/media/Gp3fnpSbYAE1mIa.jpg)

---

生成LLMs.txt 工具 [llmstxt.firecrawl.dev](https://llmstxt.firecrawl.dev/)

协议介绍和格式说明：[llmstxt.org](https://llmstxt.org/)
